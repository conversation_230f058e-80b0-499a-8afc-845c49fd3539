#include <iostream>
#include <string>

// 模拟 SessionData 类的 coverUTF16toUTF8 函数
// 使用我们新的 libiconv 实现

extern "C" {
    char* convert_utf16_to_utf8_string(const char* input, size_t input_len, size_t* output_len);
}

std::string coverUTF16toUTF8(std::string &value) {
    // 使用 libiconv 进行 UTF-16 到 UTF-8 转换
    size_t output_len = 0;
    char* converted = convert_utf16_to_utf8_string(value.c_str(), value.length(), &output_len);
    
    if (converted) {
        std::string result(converted, output_len);
        free(converted);  // 释放 C 函数分配的内存
        return result;
    } else {
        // 转换失败，返回原字符串
        return value;
    }
}

int main() {
    std::cout << "测试新的 UTF-16 到 UTF-8 转换函数" << std::endl;
    
    // 创建一个简单的测试字符串
    std::string test_input = "Hello";  // 这实际上不是 UTF-16，但用于测试函数调用
    
    std::string result = coverUTF16toUTF8(test_input);
    
    std::cout << "输入: " << test_input << std::endl;
    std::cout << "输出: " << result << std::endl;
    std::cout << "函数调用成功!" << std::endl;
    
    return 0;
}
