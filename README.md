# gw-hw
网关硬件版

安装pfring时，需安装 yum -y install bison && yum -y install flex


### libiconv 安装

```shell
rpm -Uvh https://forensics.cert.org/cert-forensics-tools-release-el7.rpm && yum --enablerepo=forensics install libiconv-devel -y
```


### 网关故障排查工具

#### 概述

快速获取可能导致问题的信息，简化操作，提高定位问题的效率
#### 功能
* 获取内存信息
* 获取cpu信息
* 获取绑定的网卡信息(如果有绑定的网卡))
* 获取加载的功能模块信息
* 配置使网关coredump时产生core文件
#### 用法

获取硬件信息
```shell
bash troubleshooting_tool.sh sys_info
```

获取加载的功能模块信息
```shell
bash troubleshooting_tool.sh module_info
```

配置使网关coredump时，产生core文件
```shell
bash troubleshooting_tool.sh set_coredump
```

### rc-local服务配置

#### 概述
在新版Linux系统中，rc-local服务已不再默认启用。我们进行了改进，确保在系统启动时能正确执行/etc/rc.local中的命令。

#### 功能
* 自动检测rc-local服务是否可用
* 自动修复rc-local服务配置问题
* 确保/etc/rc.local文件格式正确（包含shebang和exit语句）
* 自动添加必要的系统启动脚本到rc.local

#### 工作原理
当系统启动时，inst_config_systemctl函数会：
1. 检查rc-local.service文件是否存在，若不存在则创建
2. 确保/etc/rc.local文件存在且有可执行权限
3. 检查rc-local服务状态，若不活跃则尝试修复并重启
4. 自动添加需要的启动脚本到rc.local文件

#### 常见问题排查
如果rc-local服务启动失败，可以检查以下方面：
```shell
# 查看rc-local服务状态
systemctl status rc-local

# 确认rc.local文件格式及权限
file /etc/rc.local
ls -la /etc/rc.local

# 手动启动rc-local服务
systemctl start rc-local
```