[supervisord]
logfile = /opt/apigw/logs/hw-supervisord.log
logfile_maxbytes = 5MB
logfile_backups=10
loglevel = info
pidfile = /opt/apigw/logs/hw-supervisord.pid
nodaemon = false
minfds = 1024
minprocs = 200
umask = 022
user = root
identifier = supervisor
directory = /opt/apigw
nocleanup = true
childlogdir = /opt/apigw
strip_ansi = false
#environment = KEY1="value1",KEY2="value2"

[unix_http_server]
file = /opt/apigw/run/supervisor-hw.sock
chmod = 0700
#chown = apigw:apigw

[supervisorctl]
serverurl = unix:///opt/apigw/run/supervisor-hw.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:gw_parser]
command=/opt/apigw/gwhw/gw_parser -c /opt/data/apigw/gwhw/gw_parser.conf
process_name=%(program_name)s
numprocs=1
directory=/opt/apigw/gwhw
umask=022
priority=199
autostart=true
autorestart=unexpected
startsecs=5
startretries=600000
exitcodes=0
stopsignal=INT
stopwaitsecs=5
stopasgroup=false
killasgroup=false
#user=root
redirect_stderr=false
stdout_logfile=/opt/apigw/gwhw/logs/hw.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=50
stdout_capture_maxbytes=0
stdout_events_enabled=false
stderr_logfile=/opt/apigw/gwhw/logs/hw.err
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=30
stderr_capture_maxbytes=0
stderr_events_enabled=false
#environment=A="1",B="2"
environment=LD_LIBRARY_PATH="/opt/apigw/gwhw/lib/:/opt/openssl/lib/",MALLOC_ARENA_MAX=1
serverurl=AUTO

[program:file_monitor]
command=/opt/apigw/gwhw/tools/file_monitor.sh
process_name=%(program_name)s
numprocs=1
directory=/opt/apigw/gwhw
umask=022
priority=199
autostart=true
autorestart=unexpected
startsecs=10
startretries=3
exitcodes=0
stopsignal=INT
stopwaitsecs=2
stopasgroup=false
killasgroup=false
#user=root
redirect_stderr=false
stdout_logfile=/opt/apigw/gwhw/logs/file_monitor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=50
stdout_capture_maxbytes=0
stdout_events_enabled=false
stderr_logfile=/opt/apigw/gwhw/logs/file_monitor.err
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=30
stderr_capture_maxbytes=0
stderr_events_enabled=false
#environment=A="1",B="2"
environment=LD_LIBRARY_PATH="/opt/apigw/gwhw/lib/:/opt/openssl/lib/",MALLOC_ARENA_MAX=1
serverurl=AUTO

[program:gw_stats_srv]
command=/opt/apigw/gwhw/stats_srv/gw_stats_srv-exe
process_name=%(program_name)s
numprocs=1
directory=/opt/apigw/gwhw/stats_srv
umask=022
priority=199
autostart=true
autorestart=unexpected
startsecs=10
startretries=3
exitcodes=0
stopsignal=INT
stopwaitsecs=2
stopasgroup=false
killasgroup=false
#user=root
redirect_stderr=false
stdout_logfile=/opt/apigw/gwhw/logs/stats_srv_oatpp.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=50
stdout_capture_maxbytes=0
stdout_events_enabled=false
stderr_logfile=/opt/apigw/gwhw/logs/stats_srv_oatpp.err
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=30
stderr_capture_maxbytes=0
stderr_events_enabled=false
#environment=A="1",B="2"
environment=LD_LIBRARY_PATH="/opt/apigw/gwhw/lib/:/opt/openssl/lib/"
serverurl=AUTO

[program:watch_dog]
command=/bin/bash /opt/apigw/gwhw/tools/watch_dog.sh
process_name=%(program_name)s
numprocs=1
directory=/opt/apigw/gwhw
umask=022
priority=199
autostart=true
autorestart=unexpected
startsecs=10
startretries=3
exitcodes=0
stopsignal=INT
stopwaitsecs=2
stopasgroup=false
killasgroup=false
#user=root
redirect_stderr=false
stdout_logfile=/opt/apigw/gwhw/logs/watch_dog.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=50
stdout_capture_maxbytes=0
stdout_events_enabled=false
stderr_logfile=/opt/apigw/gwhw/logs/watch_dog.err
stderr_logfile_maxbytes=5MB
stderr_logfile_backups=30
stderr_capture_maxbytes=0
stderr_events_enabled=false
#environment=A="1",B="2"
serverurl=AUTO

#[program:gw_stats_srv]
#command=/opt/apigw/gwhw/stats_srv/gw_stat_srv -b 0.0.0.0:9876
#process_name=%(program_name)s
#numprocs=1
#directory=/opt/apigw/gwhw/stats_srv
#umask=022
#priority=199
#autostart=true
#autorestart=unexpected
#startsecs=10
#startretries=3
#exitcodes=0
#stopsignal=INT
#stopwaitsecs=10
#stopasgroup=false
#killasgroup=false
#user=root
#redirect_stderr=false
#stdout_logfile=/opt/apigw/gwhw/logs/stats_srv.log
#stdout_logfile_maxbytes=10MB
#stdout_logfile_backups=50
#stdout_capture_maxbytes=0
#stdout_events_enabled=false
#stderr_logfile=/opt/apigw/gwhw/logs/stats_srv.err
#stderr_logfile_maxbytes=5MB
#stderr_logfile_backups=30
#stderr_capture_maxbytes=0
#stderr_events_enabled=false
##environment=A="1",B="2"
##environment=LD_LIBRARY_PATH="/opt/apigw/gwhw/lib/"
#serverurl=AUTO
#
#[program:remove_tmp_dir]
#command=/bin/bash -c "rm -rf /opt/apigw/gwhw/stats_srv/tmpdir/*; rm -rf /tmp/pymp-*"
#priority=1
#autostart=true
#autorestart=false
#startretries=0
#redirect_stderr=false
#stdout_logfile=/opt/apigw/gwhw/logs/remove_tmp_dir.log
#stdout_logfile_maxbytes=10MB
#stdout_logfile_backups=50
#stdout_capture_maxbytes=0
#stdout_events_enabled=false
#stderr_logfile=/opt/apigw/gwhw/logs/remove_tmp_dir.err
#stderr_logfile_maxbytes=5MB
#stderr_logfile_backups=30
#stderr_capture_maxbytes=0
#stderr_events_enabled=false
