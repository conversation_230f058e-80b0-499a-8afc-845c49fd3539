cmake_minimum_required(VERSION 3.1)
set(CMAKE_INSTALL_RPATH "/opt/openssl/lib")
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)

set(project_name gw_stats_srv) ## rename your project here

project(${project_name})

set(CMAKE_CXX_STANDARD 11)
# set(CMAKE_BUILD_TYPE "Debug")

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS} -O0 -D KiB=1024 -D MiB=1048576 -std=c++11 -Wall -g -ggdb -ldl")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS} -O3 -D KiB=1024 -D MiB=1048576 -std=c++11 -Wall -ldl")

include_directories(${PROJECT_SOURCE_DIR}/oatpp-1.3.0/oatpp/)
include_directories(${PROJECT_SOURCE_DIR}/src/rapidjson/)
include_directories(${PROJECT_SOURCE_DIR}/src/app/include/)
include_directories(${PROJECT_SOURCE_DIR}/src/log/)


add_library(${project_name}-lib
        src/AppComponent.hpp
        src/controller/GwStatService.cpp
        src/controller/GwStatService.hpp
        src/dto/DTOs.hpp
        src/app/AppStats.cpp
        src/app/include/AppStats.hpp
        src/app/AppLicense.cpp
        src/app/include/AppLicense.hpp
        src/app/AppSettings.cpp
        src/app/include/AppSettings.hpp
        src/app/AppEth.cpp
        src/app/include/AppEth.hpp
        src/app/AppHealth.cpp
        src/app/include/AppHealth.hpp
        src/log/Log.cpp
        src/log/Log.hpp
        src/app/Authorization.cpp
        src/app/include/Authorization.hpp
        src/app/include/authorization_lic.hpp
        src/app/include/AppTask.hpp
        src/app/AppTask.cpp
)

## link libs

if (BUILD_ARCH STREQUAL "x86")
        link_directories(../gw_parser/liblicutils_c_sdk)
        link_directories(./lib)
elseif (BUILD_ARCH STREQUAL "ARM")
        link_directories(/home/<USER>/3rd/licutils/lib/)
        link_directories(/home/<USER>/3rd/openssl/lib/)
        link_directories(/home/<USER>/3rd/zlib/lib/)
        link_directories(/home/<USER>/3rd/oatpp/lib/)
endif()

target_link_libraries(${project_name}-lib
        pthread
        oatpp
        oatpp-test
        licutils
)

target_include_directories(${project_name}-lib PUBLIC src)

set(OPENSSL_ROOT_DIR "/opt/openssl")
set(OPENSSL_LIBRARIES "/opt/openssl/lib")
set(OPENSSL_INCLUDE_DIR "/opt/openssl/include")

find_package(OpenSSL REQUIRED)

#[[
## add executables
add_executable(${project_name}-exe
        src/App.cpp
       )
target_link_libraries(${project_name}-exe ${project_name}-lib OpenSSL::SSL OpenSSL::Crypto)
add_dependencies(${project_name}-exe ${project_name}-lib)

set_target_properties(${project_name}-lib ${project_name}-exe PROPERTIES
        CXX_STANDARD 11
        CXX_EXTENSIONS OFF
        CXX_STANDARD_REQUIRED ON
)
]]

## add executables

add_executable(${project_name}-exe
        src/App.cpp
        test/app/MyApiTestClient.hpp)
target_link_libraries(${project_name}-exe ${project_name}-lib)
add_dependencies(${project_name}-exe ${project_name}-lib)

add_executable(${project_name}-test
        test/tests.cpp
        test/app/TestComponent.hpp
        test/app/MyApiTestClient.hpp
        test/MyControllerTest.cpp
        test/MyControllerTest.hpp
)

target_link_libraries(${project_name}-test ${project_name}-lib OpenSSL::SSL OpenSSL::Crypto)
add_dependencies(${project_name}-test ${project_name}-lib)

set_target_properties(${project_name}-lib ${project_name}-exe ${project_name}-test PROPERTIES
        CXX_STANDARD 11
        CXX_EXTENSIONS OFF
        CXX_STANDARD_REQUIRED ON
)

enable_testing()
add_test(project-tests ${project_name}-test)
