/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#undef OATPP_MACRO_API_CONTROLLER_PARAM_MACRO
#undef OATPP_MACRO_API_CONTROLLER_PARAM_INFO
#undef OATPP_MACRO_API_CONTROLLER_PARAM_TYPE
#undef OATPP_MACRO_API_CONTROLLER_PARAM_NAME
#undef OATPP_MACRO_API_CONTROLLER_PARAM_TYPE_STR
#undef OATPP_MACRO_API_CONTROLLER_PARAM_NAME_STR
#undef OATPP_MACRO_API_CONTROLLER_PARAM

#undef REQUEST
#undef HEADER
#undef PATH
#undef QUERIES
#undef QUERY
#undef BODY_STRING
#undef BODY_DTO

// INIT // ------------------------------------------------------

#undef REST_CONTROLLER_INIT

#undef OATPP_MACRO_API_CONTROLLER_MACRO_SELECTOR

// REQUEST MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_REQUEST
#undef OATPP_MACRO_API_CONTROLLER_REQUEST_INFO

// HEADER MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_HEADER_1
#undef OATPP_MACRO_API_CONTROLLER_HEADER_2
#undef OATPP_MACRO_API_CONTROLLER_HEADER

// __INFO

#undef OATPP_MACRO_API_CONTROLLER_HEADER_INFO_1
#undef OATPP_MACRO_API_CONTROLLER_HEADER_INFO_2
#undef OATPP_MACRO_API_CONTROLLER_HEADER_INFO

// PATH MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_PATH_1
#undef OATPP_MACRO_API_CONTROLLER_PATH_2
#undef OATPP_MACRO_API_CONTROLLER_PATH

// __INFO

#undef OATPP_MACRO_API_CONTROLLER_PATH_INFO_1
#undef OATPP_MACRO_API_CONTROLLER_PATH_INFO_2
#undef OATPP_MACRO_API_CONTROLLER_PATH_INFO

// QUERIES MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_QUERIES
#undef OATPP_MACRO_API_CONTROLLER_QUERIES_INFO

// QUERY MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_QUERY_1
#undef OATPP_MACRO_API_CONTROLLER_QUERY_2
#undef OATPP_MACRO_API_CONTROLLER_QUERY

// __INFO

#undef OATPP_MACRO_API_CONTROLLER_QUERY_INFO_1
#undef OATPP_MACRO_API_CONTROLLER_QUERY_INFO_2
#undef OATPP_MACRO_API_CONTROLLER_QUERY_INFO

// BODY_STRING MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_BODY_STRING

// __INFO

#undef OATPP_MACRO_API_CONTROLLER_BODY_STRING_INFO

// BODY_DTO MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_BODY_DTO

// __INFO

#undef OATPP_MACRO_API_CONTROLLER_BODY_DTO_INFO

// FOR EACH // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_FOR_EACH_PARAM_DECL
#undef OATPP_MACRO_API_CONTROLLER_FOR_EACH_PARAM_PUT
#undef OATPP_MACRO_API_CONTROLLER_FOR_EACH_PARAM_CALL
#undef OATPP_MACRO_API_CONTROLLER_FOR_EACH_PARAM_INFO

// ENDPOINT_INFO MACRO // ------------------------------------------------------

#undef ENDPOINT_INFO

// ENDPOINT MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_DECL_DEFAULTS

#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_DECL_0
#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_0

#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_DECL_1
#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_1

#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_MACRO_0
#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_MACRO_1

#undef ENDPOINT

#undef ENDPOINT_INTERCEPTOR

// ENDPOINT ASYNC MACRO // ------------------------------------------------------

#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_ASYNC_DECL_DEFAULTS
#undef OATPP_MACRO_API_CONTROLLER_ENDPOINT_ASYNC_DECL
#undef ENDPOINT_ASYNC
#undef ENDPOINT_ASYNC_INIT

#undef ENDPOINT_INTERCEPTOR_ASYNC
