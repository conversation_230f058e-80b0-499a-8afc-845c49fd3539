/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *                         Bened<PERSON><PERSON>-<PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#undef OATPP_MACRO_DTO_ENUM_PARAM_MACRO
#undef OATPP_MACRO_DTO_ENUM_PARAM_NAME
#undef OATPP_MACRO_DTO_ENUM_PARAM_NAME_STR
#undef OATPP_MACRO_DTO_ENUM_PARAM_VALUE
#undef OATPP_MACRO_DTO_ENUM_PARAM_VALUE_STR
#undef OATPP_MACRO_DTO_ENUM_PARAM

#undef VALUE

//////////////////////////////////////////////////////////////////////////

#undef OATPP_MACRO_DTO_ENUM_MACRO_SELECTOR

//////////////////////////////////////////////////////////////////////////

// VALUE MACRO

#undef OATPP_MACRO_DTO_ENUM_VALUE_1


#undef OATPP_MACRO_DTO_ENUM_VALUE_2


#undef OATPP_MACRO_DTO_ENUM_VALUE_3

#undef OATPP_MACRO_DTO_ENUM_VALUE

// FOR EACH

#undef OATPP_MACRO_DTO_ENUM_PARAM_DECL_FIRST

#undef OATPP_MACRO_DTO_ENUM_PARAM_DECL_REST


#undef OATPP_MACRO_DTO_ENUM_PARAM_PUT

// ENUM MACRO

#undef OATPP_ENUM_0

#undef OATPP_ENUM_1
// Chooser

#undef OATPP_ENUM_MACRO_0

#undef OATPP_ENUM_MACRO_1

#undef ENUM