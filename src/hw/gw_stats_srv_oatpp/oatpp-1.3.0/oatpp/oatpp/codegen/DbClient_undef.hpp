/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/


#undef OATPP_MACRO_DB_CLIENT_PARAM_TYPE
#undef OATPP_MACRO_DB_CLIENT_PARAM_NAME
#undef OATPP_MACRO_DB_CLIENT_PARAM_TYPE_STR
#undef OATPP_MACRO_DB_CLIENT_PARAM_NAME_STR

#undef OATPP_MACRO_DB_CLIENT_PARAM_MACRO
#undef OATPP_MACRO_DB_CLIENT_PREPARE_MACRO

#undef OATPP_MACRO_DB_CLIENT_PARAM_MACRO_TYPE

#undef OATPP_MACRO_DB_CLIENT_PARAM
#undef OATPP_MACRO_DB_CLIENT_PREPARE

#undef PARAM
#undef PREPARE

//////////////////////////////////////////////////////////////////////////

#undef OATPP_MACRO_DB_CLIENT_MACRO_SELECTOR

//////////////////////////////////////////////////////////////////////////

// PARAM MACRO USE-CASE

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_PUT_DECL

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_PUT_TYPE

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_PUT_BODY

// PARAM MACRO

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_1

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_2

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM

// PARAM_TYPE MACRO

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_TYPE_1

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_TYPE_2

#undef OATPP_MACRO_DB_CLIENT_PARAM_PARAM_TYPE

// PREPARE MACRO USE-CASE

#undef OATPP_MACRO_DB_CLIENT_PARAM_PREPARE_PUT_DECL

#undef OATPP_MACRO_DB_CLIENT_PARAM_PREPARE_PUT_TYPE

#undef OATPP_MACRO_DB_CLIENT_PARAM_PREPARE_PUT_BODY

// PREPARE MACRO

#undef OATPP_MACRO_DB_CLIENT_PARAM_PREPARE

// PARAMS USE-CASE

#undef OATPP_MACRO_PARAM_USECASE_DECL
#undef OATPP_MACRO_PARAM_USECASE_TYPE
#undef OATPP_MACRO_PARAM_USECASE_BODY

// FOR EACH

#undef OATPP_MACRO_DB_CLIENT_PARAM_PUT_DECL

#undef OATPP_MACRO_DB_CLIENT_PARAM_PUT_TYPE

#undef OATPP_MACRO_DB_CLIENT_PARAM_PUT

// QUERY MACRO

#undef OATPP_QUERY_0


#undef OATPP_QUERY_1

// Chooser

#undef OATPP_QUERY_MACRO_0

#undef OATPP_QUERY_MACRO_1

#undef QUERY
