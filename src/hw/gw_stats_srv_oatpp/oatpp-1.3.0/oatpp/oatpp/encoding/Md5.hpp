#pragma once
#include <string>
#include <sstream>
#include <cstring>
 
#define uchar unsigned char
#define uint unsigned int


namespace oatpp { namespace encoding {

uchar* longTouchar(long n);
std::string getHexStr(uchar* val, int length);
 
class Md5
{
public:
	const uint A = 0x67452301L;
	const uint B = 0xEFCDAB89L;
	const uint C = 0x98badcfe;
	const uint D = 0x10325476;
 
	uint F(uint x, uint y, uint z);
	uint G(uint x, uint y, uint z);
	uint H(uint x, uint y, uint z);
	uint I(uint x, uint y, uint z);
 
 
	void FF(uint& a, uint b, uint c, uint d, uint src, uint s, uint m);
	void GG(uint& a, uint b, uint c, uint d, uint src, uint s, uint m);
	void HH(uint& a, uint b, uint c, uint d, uint src, uint s, uint m);
	void II(uint& a, uint b, uint c, uint d, uint src, uint s, uint m);
 
	uint LeftMoveLoop(uint n, uint s);
	uchar* buf;
	uint virtualVals[4];
	void Process();
	uint* spTouintArray();
	uint offset;
	uint totalLen;
 
public:
	Md5(uint totalLen);
	uchar* getResult();
	void update(uchar* str, int size, bool isEnd);
};

}}
