# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

jobs:
  - job: ubuntu_20_04
    displayName: 'Build - Ubuntu 20.04'
    continueOnError: false
    pool:
      vmImage: 'ubuntu-20.04'
    workspace:
      clean: all
    steps:
      - script: |
          mkdir build
      - script: |
          git clone https://github.com/oatpp/oatpp
          mkdir -p oatpp/build
        displayName: 'Checkout - oatpp'
        workingDirectory: build
      - script: |
          cmake -DOATPP_BUILD_TESTS=OFF ..
          sudo make install
        displayName: 'Build - oatpp'
        workingDirectory: build/oatpp/build
      - script: |
          cmake ..
          make
        displayName: 'Build - Project'
        workingDirectory: build
      - script: |
          make test ARGS="-V"
        displayName: 'Test'
        workingDirectory: build
  - job: macOS
    displayName: 'Build - macOS'
    continueOnError: false
    pool:
      vmImage: 'macOS-latest'
    workspace:
      clean: all
    steps:
      - script: |
          mkdir build
      - script: |
          git clone https://github.com/oatpp/oatpp
          mkdir -p oatpp/build
        displayName: 'Checkout - oatpp'
        workingDirectory: build
      - script: |
          cmake -DOATPP_BUILD_TESTS=OFF ..
          sudo make install
        displayName: 'Build - oatpp'
        workingDirectory: build/oatpp/build
      - script: |
          cmake ..
          make
        displayName: 'Build - Project'
        workingDirectory: build
      - script: |
          make test ARGS="-V"
        displayName: 'Test'
        workingDirectory: build
  - job: windows
    displayName: 'Build - Windows'
    continueOnError: false
    pool:
      vmImage: 'windows-latest'
    workspace:
      clean: all
    steps:
      - script: |
          MD build
      - script: |
          git clone https://github.com/oatpp/oatpp
          MD oatpp\build
        displayName: 'Checkout - oatpp'
        workingDirectory: build
      - script: |
          cmake -DOATPP_BUILD_TESTS=OFF ..
          cmake --build . --target INSTALL
        displayName: 'Build - oatpp'
        workingDirectory: build\oatpp\build
      - script: |
          cmake ..
          cmake --build .
        displayName: 'Build - Project'
        workingDirectory: build
      - script: |
          my-project-test.exe
        displayName: 'Test'
        workingDirectory: build\Debug\
