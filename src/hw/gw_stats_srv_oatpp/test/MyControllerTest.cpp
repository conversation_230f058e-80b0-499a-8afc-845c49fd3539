#include "MyControllerTest.hpp"

#include "controller/GwStatService.hpp"

#include "app/MyApiTestClient.hpp"
#include "app/TestComponent.hpp"

#include "oatpp/web/client/HttpRequestExecutor.hpp"

#include "oatpp-test/web/ClientServerTestRunner.hpp"

void MyControllerTest::onRun() {

  /* Register test components */
  TestComponent component;

  /* Create client-server test runner */
  oatpp::test::web::ClientServerTestRunner runner;

  /* Add MyController endpoints to the router of the test server */
  runner.addController(std::make_shared<GwStatService>());

  /* Run test */
  runner.run([this, &runner] {

    /* Get client connection provider for Api Client */
    OATPP_COMPONENT(std::shared_ptr<oatpp::network::ClientConnectionProvider>, clientConnectionProvider);

    /* Get object mapper component */
    OATPP_COMPONENT(std::shared_ptr<oatpp::data::mapping::ObjectMapper>, objectMapper);

    /* Create http request executor for Api Client */
    auto requestExecutor = oatpp::web::client::HttpRequestExecutor::createShared(clientConnectionProvider);

    /* Create Test API client */
    auto client = MyApiTestClient::createShared(requestExecutor, objectMapper);

    // 测试样例
    /* Call server API */
    auto monitorResponse = client->getMonitoringInfo();

    /* Assert that server responds with 200 */
    OATPP_ASSERT(monitorResponse->getStatusCode() == 200);

    /* Read response body as MessageDto */
    auto monitorMessage = monitorResponse->readBodyToDto<oatpp::Object<MonitorMsg>>(objectMapper.get());

    /* Assert that received message is as expected */
    OATPP_ASSERT(monitorMessage);
    OATPP_ASSERT(monitorMessage->err == 0);
    OATPP_ASSERT(monitorMessage->msg == "ok");

    /*
    auto statsResponse = client->getStats(1697794691491);
    OATPP_ASSERT(statsResponse->getStatusCode() == 200);
    auto statsMessage = statsResponse->readBodyToDto<oatpp::Object<StatsMsg>>(objectMapper.get());
    OATPP_ASSERT(statsMessage);
    OATPP_ASSERT(statsMessage->err == 0);
    OATPP_ASSERT(statsMessage->msg == "ok");
    */

    auto flagResponse = client->getSourceFlag();
    OATPP_ASSERT(flagResponse->getStatusCode() == 200);
    auto flagMessage = flagResponse->readBodyToDto<oatpp::Object<FlagMsg>>(objectMapper.get());
    OATPP_ASSERT(flagMessage);
    OATPP_ASSERT(flagMessage->err == 0);
    OATPP_ASSERT(flagMessage->msg == "ok");

    
    auto lastStatsResponse = client->getLastStats();
    OATPP_ASSERT(lastStatsResponse->getStatusCode() == 200);
    auto lastStatMessage = lastStatsResponse->readBodyToDto<oatpp::Object<StatMsg>>(objectMapper.get());
    OATPP_ASSERT(lastStatMessage);
    OATPP_ASSERT(lastStatMessage->err == 0);
    OATPP_ASSERT(lastStatMessage->msg == "ok");

    auto errResponse = client->getErrorLog();
    OATPP_ASSERT(errResponse->getStatusCode() == 200);
    auto errMessage = errResponse->readBodyToDto<oatpp::Object<ErrLog>>(objectMapper.get());
    OATPP_ASSERT(errMessage);
    OATPP_ASSERT(errMessage->errorCode == 0);
    OATPP_ASSERT(errMessage->success == "true");

    auto trafficResponse = client->getEncryptedTraffic();
    OATPP_ASSERT(trafficResponse->getStatusCode() == 200);
    auto trafficMessage = trafficResponse->readBodyToDto<oatpp::Object<SslMsg>>(objectMapper.get());
    OATPP_ASSERT(trafficMessage);
    OATPP_ASSERT(trafficMessage->err == 0);
    OATPP_ASSERT(trafficMessage->msg == "ok");

    auto versionResponse = client->getVersionConfig();
    OATPP_ASSERT(versionResponse->getStatusCode() == 200);
    auto versionMessage = versionResponse->readBodyToDto<oatpp::Object<BasicInfo>>(objectMapper.get());
    OATPP_ASSERT(versionMessage);
    OATPP_ASSERT(versionMessage->errorCode == 0);
    OATPP_ASSERT(versionMessage->success == "true");

    auto licenseResponse = client->getLicenseDetail();
    OATPP_ASSERT(licenseResponse->getStatusCode() == 200);
    auto licenseMessage = licenseResponse->readBodyToDto<oatpp::Object<BasicInfo>>(objectMapper.get());
    OATPP_ASSERT(licenseMessage);
    OATPP_ASSERT(licenseMessage->errorCode == 0);
    OATPP_ASSERT(licenseMessage->success == "true");

    auto codeResponse = client->getMachineCode();
    OATPP_ASSERT(codeResponse->getStatusCode() == 200);
    auto codeMessage = codeResponse->readBodyToDto<oatpp::Object<UploadMsg>>(objectMapper.get());
    OATPP_ASSERT(codeMessage);
    OATPP_ASSERT(codeMessage->errorCode == 0);
    OATPP_ASSERT(codeMessage->success == "true");

    auto statusResponse = client->statusCheck();
    OATPP_ASSERT(statusResponse->getStatusCode() == 200);
    auto statusMessage = statusResponse->readBodyToDto<oatpp::Object<SettingMsg>>(objectMapper.get());
    OATPP_ASSERT(statusMessage);

    auto ethResponse = client->getEthInformation();
    OATPP_ASSERT(ethResponse->getStatusCode() == 200);
    auto ethMessage = ethResponse->readBodyToDto<oatpp::Object<EthMsg>>(objectMapper.get());
    OATPP_ASSERT(ethMessage);
    OATPP_ASSERT(ethMessage->err == 0);
    OATPP_ASSERT(ethMessage->msg == "ok");

    auto healthResponse = client->getHealth();
    OATPP_ASSERT(healthResponse->getStatusCode() == 200);
    auto healthMessage = healthResponse->readBodyToDto<oatpp::Object<HealthInfo>>(objectMapper.get());
    OATPP_ASSERT(healthMessage);


  }, std::chrono::minutes(1) /* test timeout */);

  /* wait all server threads finished */
  std::this_thread::sleep_for(std::chrono::seconds(1));

}
