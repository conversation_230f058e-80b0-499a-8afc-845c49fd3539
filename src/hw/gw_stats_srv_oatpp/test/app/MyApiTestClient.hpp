
#ifndef MyApiTestClient_hpp
#define MyApiTestClient_hpp

#include "oatpp/web/client/ApiClient.hpp"
#include "oatpp/core/macro/codegen.hpp"

/* Begin Api Client code generation */
#include OATPP_CODEGEN_BEGIN(ApiClient)

/**
 * Test API client.
 * Use this client to call application APIs.
 */
class MyApiTestClient : public oatpp::web::client::ApiClient {

  API_CLIENT_INIT(MyApiTestClient)

  API_CALL("GET", "/monitoring_info", getMonitoringInfo)

  API_CALL("GET", "/stats", getStats, QUERY(Int64,startTime))

  API_CALL("GET", "/source_flag", getSourceFlag)

  API_CALL("GET", "/last_stats", getLastStats)

  API_CALL("GET", "/hw-stats/get_errlog", getErrorLog)

  API_CALL("GET", "/hw-stats/encrypted_traffic", getEncryptedTraffic)

  API_CALL("GET", "/hw-admin/local/getVersionConfig.do", getVersionConfig)

  API_CALL("GET", "/hw-admin/license/licenseDetail.do", getLicenseDetail)

  API_CALL("GET", "/hw-admin/license/machineCode.do", getMachineCode) 

  API_CALL("POST", "/gwhw/status_check", statusCheck)

  API_CALL("GET", "/eth/eth_information", getEthInformation)

  API_CALL("GET", "/health/mdr",getHealth)
};

/* End Api Client code generation */
#include OATPP_CODEGEN_END(ApiClient)

#endif // MyApiTestClient_hpp
