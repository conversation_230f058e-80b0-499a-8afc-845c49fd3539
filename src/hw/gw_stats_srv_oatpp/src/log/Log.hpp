#pragma once
#include <time.h>
#include <string>

#define LOG_DEBUG(...) if (Log::logLevel() <= Log::LogLevel::DEBUG) \
    Log::log(__FILE__, __LINE__, Log::LogLevel::DEBUG, __VA_ARGS__)
#define LOG_INFO(...) if (Log::logLevel() <= Log::LogLevel::INFO) \
    Log::log(__FILE__, __LINE__, Log::LogLevel::INFO, __VA_ARGS__)
#define LOG_WARN(...) if (Log::logLevel() <= Log::LogLevel::WARN) \
    Log::log(__FILE__, __LINE__, Log::LogLevel::WARN, __VA_ARGS__)
#ifdef WIN32
    #define LOG_ERROR(...) Log::log(__FILE__, __LINE__, Log::LogLevel::ERROr, __VA_ARGS__)
#else
    #define LOG_ERROR(...) Log::log(__FILE__, __LINE__, Log::LogLevel::ERROR, __VA_ARGS__)
#endif

#define LOG_FATAL(...) Log::log(__FILE__, __LINE__, Log::LogLevel::FATAL, __VA_ARGS__)

#define ERRTEST(c) if (c) {LOG_FATAL("ERRTEST [%s] fail", #c);}
#define ERRTEST_ENO(c) if (c) {LOG_FATAL("ERRTEST [%s] fail, errno=%d, %s", #c, errno, strerror(errno));}
#define ERRTEST_RET(c, r) if (c) {LOG_FATAL("ERRTEST [%s] fail, ret=%d, %s", #c, r, strerror(r));}

// 先声明Log类
class Log
{
public:
    enum LogLevel
    {
        DEBUG,
        INFO,
        WARN,
        ERROR,
        FATAL,
        LOG_LEVEL_NUM
    };

    static void log(const char* fileName, const int& lineNum, LogLevel logLevel, const char* format, ...);
    
    // 新增：API访问日志方法 - 独立的访问日志
    static void logAccess(const std::string& method, const std::string& endpoint, int statusCode);

    static LogLevel logLevel() { return m_logLevel; }
    static void setLogLevel(LogLevel logLevel) { m_logLevel = logLevel; }

    // 新增：API接口上下文管理方法 - 用于应用日志
    static void setApiContext(const std::string& endpoint);
    static void clearApiContext();
    static const std::string& getApiContext();

    static const int getTid();
    static const char* getFormatTime(); // 修改：现在返回简化的时间格式

private:
    static LogLevel m_logLevel;
    static const char* m_logLevelName[LOG_LEVEL_NUM];

    static thread_local char m_logBuf[KiB];
    
    // 新增：线程局部存储的API接口上下文 - 用于应用日志
    static thread_local std::string m_apiContext;
};

// 新增：API接口上下文管理宏
#define API_CONTEXT_SET(endpoint) Log::setApiContext(endpoint)
#define API_CONTEXT_CLEAR() Log::clearApiContext()

// 新增：RAII风格的API上下文管理类
class ApiContextGuard {
public:
    explicit ApiContextGuard(const std::string& endpoint) {
        Log::setApiContext(endpoint);
    }
    ~ApiContextGuard() {
        Log::clearApiContext();
    }
    // 禁用拷贝构造和赋值
    ApiContextGuard(const ApiContextGuard&) = delete;
    ApiContextGuard& operator=(const ApiContextGuard&) = delete;
};

#define API_CONTEXT_GUARD(endpoint) ApiContextGuard _api_guard(endpoint)

// 新增：API访问日志记录类 - 独立于应用日志
class ApiAccessLogger {
public:
    explicit ApiAccessLogger(const std::string& method, const std::string& endpoint) 
        : m_method(method), m_endpoint(endpoint), m_statusCode(200) {
        // 只设置应用日志的上下文，不记录访问日志
        Log::setApiContext(endpoint);
    }
    
    ~ApiAccessLogger() {
        // 记录访问日志
        Log::logAccess(m_method, m_endpoint, m_statusCode);
        // 清理应用日志上下文
        Log::clearApiContext();
    }
    
    void setStatusCode(int statusCode) {
        m_statusCode = statusCode;
    }
    
    // 禁用拷贝构造和赋值
    ApiAccessLogger(const ApiAccessLogger&) = delete;
    ApiAccessLogger& operator=(const ApiAccessLogger&) = delete;

private:
    std::string m_method;
    std::string m_endpoint;
    int m_statusCode;
};

#define API_ACCESS_LOGGER(method, endpoint) ApiAccessLogger _access_logger(method, endpoint)
