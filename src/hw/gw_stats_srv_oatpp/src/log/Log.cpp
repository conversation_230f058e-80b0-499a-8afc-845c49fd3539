#include "Log.hpp"
#include <stdio.h>
#include <errno.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <chrono>

using namespace std;

Log::LogLevel Log::m_logLevel = Log::LogLevel::INFO;
const char* Log::m_logLevelName[Log::LOG_LEVEL_NUM] = {
    "DEBUG",
    "INFO",
    "WARN",
    "ERROR",
    "FATAL"
};

thread_local char Log::m_logBuf[KiB] = { 0 };
thread_local std::string Log::m_apiContext = "";

const int Log::getTid()
{
    return syscall(SYS_gettid);
}

const char* Log::getFormatTime()
{
    time_t now = time(nullptr);
    thread_local char m_formatTime[20] = {0};
    thread_local struct tm m_timeInfo;
    
    if (localtime_r(&now, &m_timeInfo) != nullptr)
    {
        snprintf(m_formatTime, sizeof(m_formatTime), "%4d-%02d-%02d %02d:%02d:%02d",
            m_timeInfo.tm_year + 1900,
            m_timeInfo.tm_mon + 1,
            m_timeInfo.tm_mday,
            m_timeInfo.tm_hour,
            m_timeInfo.tm_min,
            m_timeInfo.tm_sec);
    }
    else
    {
        snprintf(m_formatTime, sizeof(m_formatTime), "localtime_r errno=%d", errno);
    }
    
    return m_formatTime;
}

void Log::setApiContext(const std::string& endpoint)
{
    m_apiContext = endpoint;
}

void Log::clearApiContext()
{
    m_apiContext.clear();
}

const std::string& Log::getApiContext()
{
    return m_apiContext;
}

void Log::logAccess(const std::string& method, const std::string& endpoint, int statusCode)
{
    printf("[%s] [INFO] - \"%s %s HTTP/1.1\" %d\n", 
           getFormatTime(), 
           method.c_str(), 
           endpoint.c_str(), 
           statusCode);
    fflush(stdout);
}

void Log::log(const char* fileName, const int& lineNum, LogLevel logLevel, const char* format, ...)
{
    bool isNeedFree = false;
    char* log = nullptr;
    const char* p = nullptr;
    int logLen = 0;
    int times = 0;//几倍
    va_list arg;

    if ((p = strrchr(fileName, '/')) != nullptr)
    {
        fileName = p + 1;
    }

    if (!m_apiContext.empty()) {
        logLen = snprintf(m_logBuf, sizeof(m_logBuf), "[%s] [%d] [%s] [%s] - ",
            getFormatTime(),
            getTid(),
            m_logLevelName[logLevel],
            m_apiContext.c_str());
    } else {
        logLen = snprintf(m_logBuf, sizeof(m_logBuf), "[%s] [%d] [%s] - ",
            getFormatTime(),
            getTid(),
            m_logLevelName[logLevel]);
    }
    
    va_start(arg, format);
    logLen += vsnprintf(m_logBuf + logLen, sizeof(m_logBuf) - logLen, format, arg);
    va_end(arg);
    logLen++;//do not forget '\0'
    log = m_logBuf;

    times = (logLen + KiB - 1) / KiB;//向上取整
    if (times > 1)
    {
        log = (char*)malloc(times * KiB);
        if (nullptr == log)
        {
            if (!m_apiContext.empty()) {
                snprintf(m_logBuf, sizeof(m_logBuf), "[%s] [%d] [%s] [%s] - allocate memory fail, logLen=%d times=%d - %s:%d\n",
                    getFormatTime(),
                    getTid(),
                    m_logLevelName[logLevel],
                    m_apiContext.c_str(),
                    logLen,
                    times,
                    fileName,
                    lineNum);
            } else {
                snprintf(m_logBuf, sizeof(m_logBuf), "[%s] [%d] [%s] - allocate memory fail, logLen=%d times=%d - %s:%d\n",
                    getFormatTime(),
                    getTid(),
                    m_logLevelName[logLevel],
                    logLen,
                    times,
                    fileName,
                    lineNum);
            }
            log = m_logBuf;
        }
        else
        {
            int logBufLen = times * KiB;

            isNeedFree = true;
            if (!m_apiContext.empty()) {
                logLen = snprintf(log, logBufLen, "[%s] [%d] [%s] [%s] - ",
                    getFormatTime(),
                    getTid(),
                    m_logLevelName[logLevel],
                    m_apiContext.c_str());
            } else {
                logLen = snprintf(log, logBufLen, "[%s] [%d] [%s] - ",
                    getFormatTime(),
                    getTid(),
                    m_logLevelName[logLevel]);
            }
            va_start(arg, format);
            vsnprintf(log + logLen, logBufLen - logLen, format, arg);
            va_end(arg);
        }
    }

    if (Log::LogLevel::DEBUG == m_logLevel)
    {
        printf("%s - %s:%d\n", log, fileName, lineNum);
    }
    else
    {
        printf("%s\n", log);
    }

    fflush(stdout);

    if (isNeedFree) { free(log); }

    if (FATAL == logLevel) { abort(); }
}