#include <sys/types.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdio.h>
#include <dirent.h>
#include <algorithm>
#include <chrono>
#include <thread>
#include <cstdlib>
#include <vector>
#include <set>
#include "AppSettings.hpp"

#define SOCKET_SERVER "127.0.0.1"
#define SOCKET_PORT 23
#define SHELLCMD_INFO_FILE "./tmp.txt"

void Settings::system_cmd(std::string cmd, int timestamp)
{
    std::chrono::seconds timerDuration(timestamp);
    std::this_thread::sleep_for(timerDuration);
    std::system(cmd.c_str());
}

Settings::~Settings(void)
{
}

void Settings::set_domain_ip_to_file(std::string domain, std::string ip)
{
    std::string cmd = "cat -n /etc/hosts | grep " + domain + " | awk '{print $1}'";
    std::string express = "";
    FILE* fp = popen(cmd.c_str(), "r");
    char buffer[16] = "0";
    if (fp) {
        while (fgets(buffer, sizeof(buffer), fp)) {
            buffer[strlen(buffer) - 1] = '\0';
            std::string line_num = buffer;
            express = express + "-e '" + line_num + "d' ";
            memset(buffer, 0, sizeof(buffer));
        }
        if (express.length()) {
            cmd = "sed -i " + express + " /etc/hosts";
            system(cmd.c_str());
        }

        cmd = "echo \"" + ip + " " + domain + "\" >> /etc/hosts";
        system(cmd.c_str());

        pclose(fp);
    }
}

std::string Settings::send_command(std::string command)
{
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(SOCKET_PORT);
    addr.sin_addr.s_addr = htonl(INADDR_LOOPBACK);
    int ret = connect(sockfd, (const struct sockaddr*)&addr, sizeof(addr));
    if (ret == -1) {
        return R"({"success"="false", "errCode"=-108, "msg"="connect gw 23 port failed"})";
    }
    ret = send(sockfd, command.c_str(), command.length(), 0);
    std::string response = "";
    char buf[1024] = {0};
    ret = recv(sockfd, buf, sizeof(buf) - 1, 0);
    if (ret < 0) {
        return R"({"success"="false", "errCode"=-107, "msg"="recv message failed"})";
    }
    response = response + buf;
    close(sockfd);
    return response;
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::DomainToIp(String content)
{
    auto dto = SettingMsg::createShared();
    std::string err_msg = "";
    try {
        err_msg = "get post json data fail";
        rapidjson::Document doc;
        doc.Parse(content->c_str());
        rapidjson::Value& val = doc;

        err_msg = "open config file [" + m_gw_parser_cfg + "] fail";
        std::ifstream file(m_gw_parser_cfg);
        std::string json((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
        rapidjson::Document gw_parser_conf_doc;
        gw_parser_conf_doc.Parse(json.c_str());
        rapidjson::Value& gw_parser_conf = gw_parser_conf_doc;
        file.close();

        err_msg = "open config file gw_stat_srv.conf fail";
        file.open("gw_stats_srv.conf", ios::in);
        std::string stat((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
        rapidjson::Document gw_stat_srv_conf_doc;
        gw_stat_srv_conf_doc.Parse(stat.c_str());
        rapidjson::Value& gw_stat_srv_conf = gw_stat_srv_conf_doc;
        file.close();

        err_msg = "get minio domain fail";
        std::string minio_domain = gw_parser_conf["minio"]["endpoint"].GetString();
        std::string::size_type index = minio_domain.rfind(':');
        if (index != std::string::npos) {
            std::string domain(minio_domain.begin(), minio_domain.begin() + index);
            minio_domain = domain;
        }

        err_msg = "get kafka domain fail";
        std::string kafka_domain =
            gw_parser_conf["parser"]["kafka"]["brokers_list"]["http_ruled"][0]["host"].GetString();
        index = kafka_domain.rfind(':');
        if (index != std::string::npos) {
            std::string domain(kafka_domain.begin(), kafka_domain.begin() + index);
            kafka_domain = domain;
        }

        err_msg = "get nacos domain fail";
        std::string nacos_domain = gw_stat_srv_conf["nacos_conf"]["server_addrs"].GetString();
        index = nacos_domain.rfind(':');
        if (index != std::string::npos) {
            std::string domain(nacos_domain.begin(), nacos_domain.begin() + index);
            nacos_domain = domain;
        }

        err_msg = "set minio domain fail";
        set_domain_ip_to_file(minio_domain, val["ip"].GetString());

        err_msg = "set kafka domain fail";
        set_domain_ip_to_file(kafka_domain, val["ip"].GetString());

        err_msg = "set nacos domain fail";
        set_domain_ip_to_file(nacos_domain, val["ip"].GetString());
        std::thread(system_cmd,
                    "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser", 1)
            .detach();
    } catch (const std::exception& e) {
        dto->success = false;
        dto->error_msg = err_msg;
    }
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::DropHttpFileEvent(String content)
{
    rapidjson::Document doc;
    doc.Parse(content->c_str());
    if (content == "" || doc.HasParseError()) {
        return createResponse(Status::CODE_200, "get json fail!");
    }
    rapidjson::Value& val = doc;
    std::string response = "";
    if (val["is_drop"].GetInt() == 1) {
        response = send_command("drop_http_file_event");
    } else {
        response = send_command("save_http_file_event");
    }

    return createResponse(Status::CODE_200, response);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::SetHttpUrlFilter(String content)
{
    auto dto = SendMsg::createShared();
    rapidjson::Document doc;
    doc.Parse(content->c_str());
    rapidjson::Value& val = doc;
    std::string url = "";
    for (auto it = val["urls"].GetArray().begin(); it != val["urls"].GetArray().end(); it++) {
        url = url + it->GetString() + '\n';
    }
    std::ofstream file;
    file.open(m_http_url_filter);
    if (file.is_open()) {
        file << url;
        dto->msg = "set http url filter success";
    }
    file.close();
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::CleanHttpUrlFilter(void)
{
    auto dto = SendMsg::createShared();
    fstream file(m_http_url_filter, ios::out | ios::trunc);
    if (file.is_open()) {
        dto->msg = "clean http url filter success";
        file.close();
    } else {
        dto->success = "false";
        dto->errorCode = -1;
        dto->msg = "file not exist";
    }
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::SetControlStrategy(String content)
{
    auto dto = SendMsg::createShared();
    rapidjson::Document doc;
    doc.Parse(content->c_str());
    rapidjson::Value& val = doc;
    std::string command = "";
    if (1 == val["parameter"].GetInt()) {
        if (!val.HasMember("id") || !val.HasMember("controlName") || !val.HasMember("controlType") ||
            !val.HasMember("controlContents")) {
            dto->success = "false";
            dto->errorCode = -1;
            dto->msg = "lost some parameter";
            return createDtoResponse(Status::CODE_200, dto);
        }
        command = "add_control_strategy|";
        command = command + val["id"].GetString() + "|" + val["controlName"].GetString() + "|" +
                  val["controlType"].GetString() + "|[";
        for (const auto& ip : val["controlContents"].GetArray()) {
            command = command + "\"" + ip.GetString() + "\",";
        }
        command.erase(command.length() - 1);
        command = command + "]";
    } else if (2 == val["parameter"].GetInt()) {
        if (!val.HasMember("id")) {
            dto->success = "false";
            dto->errorCode = -1;
            dto->msg = "lost some parameter";
            return createDtoResponse(Status::CODE_200, dto);
        }
        command = "del_control_strategy|";
        command = command + val["id"].GetString();
    } else {
        dto->success = "false";
        dto->errorCode = -1;
        dto->msg = "unknown parameter";
        return createDtoResponse(Status::CODE_200, dto);
    }
    return createResponse(Status::CODE_200, send_command(command));
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::StartGwhw(void)
{
    auto dto = SettingMsg::createShared();
    std::thread(system_cmd, "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser",
                1)
        .detach();
    /*
    try
    {
        dto->error_msg = "exec start cmd fail";
        FILE* pipe = popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser", "r");
        if (pipe)
        {
            char buffer[1024];
            std::string result = "";
            while (!feof(pipe))
            {
                if (fgets(buffer, 1024, pipe) != NULL)
                {
                    if(strstr(buffer, "started") != NULL)
                    {
                        dto->error_msg = "success";
                        return createDtoResponse(Status::CODE_200, dto);
                    }
                    result += buffer;
                }
            }
            dto->error_msg = result;
            pclose(pipe);
        }
    }
    catch(const std::exception& e)
    {
      std::cerr << e.what() << '\n';
    }

    dto->success = false;
    */
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::StopGwhw(void)
{
    auto dto = SettingMsg::createShared();
    std::thread(system_cmd, "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser", 1)
        .detach();
    /*
    try
    {
        dto->error_msg = "exec stop cmd fail";
        FILE* pipe = popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf stop gw_parser", "r");
        if (pipe)
        {
            char buffer[1024];
            std::string result = "";
            while (!feof(pipe))
            {
                if (fgets(buffer, 1024, pipe) != NULL)
                {
                    if(strstr(buffer, "stopped") != NULL)
                    {
                        dto->error_msg = "success";
                        return createDtoResponse(Status::CODE_200, dto);
                    }
                    result += buffer;
                }
            }
            dto->error_msg = result;
            pclose(pipe);
        }
    }
    catch(const std::exception& e)
    {
      std::cerr << e.what() << '\n';
    }

    dto->success = false;
    */
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::RestartGwhw(void)
{
    auto dto = SettingMsg::createShared();
    std::thread(system_cmd, "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser",
                1)
        .detach();
    /*
    try
    {
        dto->error_msg = "exec restart cmd fail";
        FILE* pipe = popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser", "r");
        if (pipe)
        {
            char buffer[1024];
            std::string result = "";
            while (!feof(pipe))
            {
                if (fgets(buffer, 1024, pipe) != NULL)
                {
                    if(strstr(buffer, "started") != NULL)
                    {
                        dto->error_msg = "success";
                        return createDtoResponse(Status::CODE_200, dto);
                    }
                    result += buffer;
                }
            }
            dto->error_msg = result;
            pclose(pipe);
        }
    }
    catch(const std::exception& e)
    {
      std::cerr << e.what() << '\n';
    }

    dto->success = false;
    */
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::UninstallGwhw(void)
{
    auto dto = SettingMsg::createShared();
    std::thread(system_cmd, "rm -rf /opt/apigw/ && systemctl stop gwhw", 1).detach();
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::UpdateGwhw(
    std::shared_ptr<IncomingRequest> request)
{
    auto dto = SettingMsg::createShared();
    try {
        dto->error_msg = "get update package fail";
        oatpp::data::resource::TemporaryFile tmp("/home");
        request->transferBody(tmp.openOutputStream());
        if (!request->getBodyStream()) {
            dto->success = false;
            return createDtoResponse(Status::CODE_200, dto);
        }

        dto->error_msg = "save file fail";
        struct stat fileStat;
        if ((stat("/home/<USER>/", &fileStat) == 0) && S_ISDIR(fileStat.st_mode)) {
            system("rm -rf /home/<USER>/");
        }
        mkdir("/home/<USER>/", S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);

        OATPP_ASSERT_HTTP(tmp.moveFile("/home/<USER>/update_gwhw.zip"), Status::CODE_500, "Failed to save file")

        dto->error_msg = "exec update script fail";
        system(
            "touch /home/<USER>/1.txt && echo \"* * * * * bash /opt/apigw/gwhw/tools/update_gwhw.sh\" >> "
            "/var/spool/cron/root");
        std::thread(system_cmd, "systemctl stop gwhw", 5).detach();
    } catch (const std::exception& e) {
        dto->success = false;
        return createDtoResponse(Status::CODE_200, dto);
    }

    dto->error_msg = "success";
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::StatusCheck(void)
{
    auto dto = SettingMsg::createShared();
    try {
        dto->error_msg = "exec status check cmd fail";
        FILE* pipe = popen("bash /opt/apigw/gwhw/tools/status_check.sh", "r");
        if (pipe) {
            char buffer[1024];
            std::string result = "";
            while (fgets(buffer, 1024, pipe) != NULL) {
                result += buffer;
            }
            pclose(pipe);
            if (result == "kafka telnet success\n") {
                dto->error_msg = "success";
                return createDtoResponse(Status::CODE_200, dto);
            }
            dto->error_msg = result;
        }

    } catch (const std::exception& e) {
        std::cerr << e.what() << '\n';
    }
    dto->success = false;
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Settings::setKafkaSaslParam(String content)
{
    auto dto = SettingMsg::createShared();
    std::string err_msg = "";
    try {
        rapidjson::Document doc;
        doc.Parse(content->c_str());
        rapidjson::Value& val = doc;

        string sasl_enable = val["sasl_enable"].GetString();
        string username = val["username"].GetString();
        string password = val["password"].GetString();
        string mechanisms = val["mechanisms"].GetString();
        string security_protocol = val["security_protocol"].GetString();
        system_cmd("sed -i '/\"sasl_enable\":/c\\      \"sasl_enable\":\"" + sasl_enable +
                       "\",' /opt/data/apigw/gwhw/gw_parser.conf",
                   0);
        system_cmd(
            "sed -i '/\"username\":/c\\      \"username\":\"" + username + "\",' /opt/data/apigw/gwhw/gw_parser.conf",
            0);
        system_cmd(
            "sed -i '/\"password\":/c\\      \"password\":\"" + password + "\",' /opt/data/apigw/gwhw/gw_parser.conf",
            0);
        system_cmd("sed -i '/\"mechanisms\":/c\\      \"mechanisms\":\"" + mechanisms +
                       "\",' /opt/data/apigw/gwhw/gw_parser.conf",
                   0);
        system_cmd("sed -i '/\"security_protocol\":/c\\      \"security_protocol\":\"" + security_protocol +
                       "\",' /opt/data/apigw/gwhw/gw_parser.conf",
                   0);
        std::thread(system_cmd,
                    "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser", 1)
            .detach();
        err_msg = "success";

    } catch (const std::exception& e) {
        err_msg = "get post json data fail";
        dto->success = false;
        dto->error_msg = err_msg;
    }
    return createDtoResponse(Status::CODE_200, dto);
}
