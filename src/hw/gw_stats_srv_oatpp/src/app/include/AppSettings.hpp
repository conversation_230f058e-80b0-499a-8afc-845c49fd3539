#ifndef AppSettings_hpp
#define AppSettings_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/data/resource/TemporaryFile.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"

#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class Settings : public oatpp::web::server::api::ApiController
{
public:
    Settings(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
        : oatpp::web::server::api::Api<PERSON><PERSON>roller(objectMapper)
        , m_gw_parser_cfg("/opt/data/apigw/gwhw/gw_parser.conf")
        , m_http_url_filter("/opt/urlbase/url_filter_dynamic.file")
    {

    }

    virtual ~Settings(void);

public:
    // 域名映射
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> DomainToIp(String);
    // http文件事件
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> DropHttpFileEvent(String);
    // 设置http过滤的url
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> SetHttpUrlFilter(String);
    // 清除过滤条件
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> CleanHttpUrlFilter(void);
    // 旁路阻断
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> SetControlStrategy(String);
    // 启用网关
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> StartGwhw(void);
    // 停用网关
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> StopGwhw(void);
    // 重启网关
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> RestartGwhw(void);
    // 卸载网关
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> UninstallGwhw(void);
    // 升级网关
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> UpdateGwhw(std::shared_ptr<IncomingRequest>);
    // 状态检查
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> StatusCheck(void);
    // 设置kafka参数
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> setKafkaSaslParam(String);

private:
    void set_domain_ip_to_file(std::string domain, std::string ip);
    std::string send_command(std::string command);
    static void system_cmd(std::string cmd, int timestamp);

private:
    std::string m_gw_parser_cfg;
    std::string m_http_url_filter;
};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif //AppSettings_hpp
