#ifndef AppLicense_hpp
#define AppLicense_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include <unistd.h>
#include <sys/stat.h>
#include <ctime>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"
#include "oatpp/web/mime/multipart/TemporaryFileProvider.hpp"
#include "oatpp/web/mime/multipart/Reader.hpp"
#include "oatpp/web/mime/multipart/PartList.hpp"

#include "log/Log.hpp"
#include "Authorization.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class License : public oatpp::web::server::api::ApiController
{
public:
    License(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
        : oatpp::web::server::api::ApiController(objectMapper)
        , m_gw_parser_cfg("/opt/data/apigw/gwhw/gw_parser.conf")
        , m_version_file_dir("/opt/apigw/gwhw/version.txt")
        , m_product_sn("gw010502")
        , m_product_id("2")
        , m_license_file_dir("/opt/licutils/")
        , m_license_file_name("license.lic")
    {
        ifstream ifs(m_gw_parser_cfg);
        if(ifs.is_open())
        {
            std::string content( (istreambuf_iterator<char>(ifs)),(istreambuf_iterator<char>()) );
            rapidjson::Document doc;
            doc.Parse(content.c_str());
            rapidjson::Value& value = doc;
            m_pem_dir = value["parser"]["pem_dir"].GetString();
            ifs.close();
        }

        mkdir(m_pem_dir.c_str(),S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
        
    }

    virtual ~License(void);

public:
    // 获取网关基本信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetBasicInfo(void);
    // 获取授权信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetLicenseInfo(void);
    // 获取机器码
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetMachineCode(void);
    // 上传授权文件
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> UploadLicense(std::shared_ptr<IncomingRequest>);
    // 导入证书
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> UploadPem(std::shared_ptr<IncomingRequest>);
    // 删除证书
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> DeletePem(String);

private:
    std::string get_version_info();
    //int init_licutils();

private:
    std::string m_gw_parser_cfg;
    std::string m_version_file_dir;
    std::string m_product_sn;
    std::string m_product_id;
    std::string m_license_file_dir;
    std::string m_license_file_name;
    std::string m_pem_dir;
};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif//AppLicense_hpp