#ifndef AppEth_hpp
#define AppEth_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include <vector>
#include <mutex>
#include <map>
#include <set>
#include <chrono>
#include <thread>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"

#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class Eth : public oatpp::web::server::api::ApiController
{
public:
    Eth(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
        : oatpp::web::server::api::ApiController(objectMapper)
        , m_all_eth_file("/proc/net/dev")
        , m_virtual_eth_dir("/sys/devices/virtual/net/")
        , m_gw_parser_cfg("/opt/data/apigw/gwhw/gw_parser.conf")
        , m_dpdk_black_list("/opt/data/apigw/gwhw/dpdk_black_list.txt")
        , m_dpdk_white_list("/opt/data/apigw/gwhw/dpdk_white_list.txt")
    {
        std::ifstream file(m_gw_parser_cfg);
        std::string json((std::istreambuf_iterator<char>(file)),std::istreambuf_iterator<char>());
        rapidjson::Document gw_parser_conf_doc;
        gw_parser_conf_doc.Parse(json.c_str());
        rapidjson::Value& gw_parser_conf = gw_parser_conf_doc;
        file.close();

        // 获取当前安装模式
        std::string source_path = gw_parser_conf["plugin"]["source_path"].GetString();
        if(source_path.find("file_source") != std::string::npos)
        {
            mode_name.push_back("agent");
        }
        if(source_path.find("nic_source") != std::string::npos)
        {
            mode_name.push_back("nic");
        }
        else if(source_path.find("dpdk_source") != std::string::npos)
        {
            mode_name.push_back("dpdk");
        }
        if(source_path.find("pcap_source") != std::string::npos)
        {
            mode_name.push_back("pcap");
        }
        
        // 获取当前网卡
        get_eth_basic_info();

        for(auto eth_name = physics_eth.begin(); eth_name != physics_eth.end(); ++eth_name)
        {
            std::thread(get_eth_speed, *eth_name).detach();
            eth_speed[*eth_name] = "";
        }

    }

    virtual ~Eth(void);

public:
    // 获取网口信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetEthInformation(Boolean health = false);
    EthInformation::Wrapper GetEthInfo(void) {return ethinfo;}
    // 网口绑定
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> BindEth(String);

private:
    void get_eth_basic_info(void);
    void refresh_eth_basic_info(void);
    static void get_eth_speed(std::string ethname);
    int str_count(char *str, char *substr);
    void delete_space(std::string &s);
    EthInfoDetail::Wrapper get_eth_info(std::string ethname);
    void system_cmd(std::string cmd, int timestamp);
    void find_pci(std::string ethname);

private:
    std::string m_all_eth_file;
    std::string m_virtual_eth_dir;
    std::string m_gw_parser_cfg;
    std::string m_dpdk_black_list;
    std::string m_dpdk_white_list;
    std::vector<std::string> mode_name;
    std::vector<std::string> all_eth;
    std::vector<std::string> virtual_eth;
    std::vector<std::string> physics_eth;
    std::map<std::string, std::string> eth_pci_map;
    EthInformation::Wrapper ethinfo;
    static std::map<std::string, std::string> eth_speed;
    static std::mutex mx;
};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif//AppEth_hpp