#ifndef _AUTHORIZATION_LIC_H_
#define _AUTHORIZATION_LIC_H_
#include <stdint.h>
#define MAX_CHAR_LEN     (256)
#define SA_GIT_VER_SHA "96b34387dc3ea2cceeaef1d201396816bcef5a68"
#define SA_VERSION_INFO  "v1.0.0"

/* 错误码 */
/* 通用错误码 -101 ～ -200 */
#define AUTH_LIC_ERR_ALREADY_INIT                          -101                 /* 已经执行过初始化函数 */
#define AUTH_LIC_ERR_NO_INIT                               -102                 /* 没有执行初始化函数 */
#define AUTH_LIC_ERR_IN_PARAM_IS_INVAILD                   -103                 /* 输入的参数非法 */
#define AUTH_LIC_ERR_MALLOC_FAILED                         -104                 /* 申请空间失败 */
#define AUTH_LIC_ERR_CREATE_JSON_OBJ_FAILED                -105                 /* 创建JSON对象失败 */
#define AUTH_LIC_ERR_SERIAL_JSON_OBJ_FAILED                -106                 /* 序列化产品对象失败 */
#define AUTH_LIC_ERR_BASE64_ENCODE_FAILED                  -107                 /* base64编码失败 */
#define AUTH_LIC_ERR_PRODUCT_ID_NOT_MATCH                  -108                 /* 产品ID不匹配 */
#define AUTH_LIC_ERR_INIT_MUTEX_FAILED                     -109                 /* 初始化互斥锁失败 */
#define AUTH_LIC_ERR_LOCK_MUTEX_FAILED                     -110                 /* 获取锁资源失败 */
#define AUTH_LIC_ERR_LOCK_MUTEX_TIMEOUT                    -111                 /* 获取锁资源超时 */

/* 加载license文件功能错误码范围 -201 ～ -300 */
#define AUTH_LIC_ERR_LIC_FILE_NOT_EXIST                    -201                /* license文件不存在 */
#define AUTH_LIC_ERR_GET_LIC_FILE_STAT_FAIL                -202                /* 获取license状态失败 */
#define AUTH_LIC_ERR_OPEN_LIC_FILE_FAILED                  -203                /* 打开license文件失败 */
#define AUTH_LIC_ERR_READ_LIC_FIEL_FAILED                  -204                /* 读取license文件内容失败 */
#define AUTH_LIC_ERR_NEW_BIO_OBJ_FAILED                    -205                /* 创建BIO 对象失败 */
#define AUTH_LIC_ERR_GET_RSA_PUBLIC_KEY_FAILED             -206                /* 获取RSA公钥失败 */

/* 获取机器码功能的错误码范围 -301 ～ -400 */
#define AUTH_LIC_ERR_GET_MAC_ADDR_SOCK_FAILED              -301                /* 获取MAC地址连接sock失败 */
#define AUTH_LIC_ERR_GET_MAC_ADDR_IOCTL_FAILED             -302
#define AUTH_LIC_ERR_GET_MAC_ADDR_FAILED                   -303                /* 获取MAC地址失败 */  
#define AUTH_LIC_ERR_CREATE_UNIQUE_CODE_FAILED             -304                /* 创建机器码JSON对象失败 */
#define AUTH_LIC_ERR_SERIAL_UNIQUE_CODE_FAILED             -305                /* 序列化机器码JSON对象失败 */
#define AUTH_LIC_ERR_AES_ENCRYPY_UNIQUE_CODE_FAILED        -306                /* 使用AES算法加密机器码失败 */
#define AUTH_LIC_ERR_RSA_ENCRYPT_HASH_FAILED               -307                /* 使用RSA算法加密md5产生的hash失败 */
#define AUTH_LIC_ERR_ENCODE_CIPHER_UNIQUR_CODE_FAILED      -308                /* base64编码机器码密文失败 */
#define AUTH_LIC_ERR_ENCODE_CIPHER_HASH_FAILED             -309                /* base64编码hash密文失败 */
#define AUTH_LIC_ERR_COMPRESS_UNIQUE_CODE_FAILED           -310                /* 压缩机器码失败 */
 
/* 验证签名功能的错误码范围 -401 ～ -500 */
#define AUTH_LIC_ERR_UNCOMPRESS_FAILED                     -401                /* 解压授权文件失败 */
#define AUTH_LIC_ERR_PARSE_LIC_JSON_FAILED                 -402                /* 解析license文件的JSON格式失败 */
#define AUTH_LIC_ERR_GET_AUTH_CONTENT_FAILED               -403                /* 获取加密授权内容失败 */
#define AUTH_LIC_ERR_GET_SIGN_CONTENT_FAILED               -404                /* 获取签名内容失败 */
#define AUTH_LIC_ERR_GET_AES_KEY_FAILED                    -405                /* 获取aes密钥种子失败 */
#define AUTH_LIC_ERR_BASE64_DECODE_AUTH_FAILED             -406                /* 授权信息base64解码失败 */
#define AUTH_LIC_ERR_BASE64_DECODE_SIGN_FAILED             -407                /* 签名信息base64解码失败 */
#define AUTH_LIC_ERR_BASE64_DECODE_AES_FAILED              -408                /* aes密钥种子解码失败 */
#define AUTH_LIC_ERR_RSA_DECRYPT_AES_KEY_FAILED            -409                /* rsa解密aes密钥种子失败 */
#define AUTH_LIC_ERR_AES_DECRYPT_AUTH_FAILED               -410                /* aes解密授权内容失败 */
#define AUTH_LIC_ERR_RSA_DECRYPT_SIGN_FAILED               -411                /* rsa解密签名信息失败 */
#define AUTH_LIC_ERR_VERIFY_SIGN_FAILED                    -412                /* 验证签名信息失败 */

/* 更新license文件内容功能的错误码范围 -501 ～ -600 */
#define AUTH_LIC_ERR_PARSE_AUTH_JSON_FAILED                -501                /* 解析授权信息的JSON格式失败 */
#define AUTH_LIC_ERR_PRODUCT_INFO_NOT_EXIST                -502                /* 产品信息不存在 */
#define AUTH_LIC_ERR_VERSION_INFO_NOT_EXIST                -503                /* 产品版本号信息不存在 */
#define AUTH_LIC_ERR_VERSION_INFO_NOT_MATCH                -504                /* 产品版本号信息不匹配 */
#define AUTH_LIC_ERR_SERIAL_INFO_NOT_EXIST                 -505                /* 产品序列号信息不存在 */
#define AUTH_LIC_ERR_SERIAL_INFO_NOT_MATCH                 -506                /* 产品序列号信息不匹配 */
#define AUTH_LIC_ERR_UNIQUE_CODE_INFO_NOT_EXIST            -507                /* 机器码信息不存在 */
#define AUTH_LIC_ERR_UNIQUE_CODE_INFO_NOT_MATCH            -508                /* 机器码信息不匹配 */

/* 获取license文件内容功能的错误码范围 -601 ～ -700 */
#define AUTH_LIC_ERR_GET_FUNCTION_LIMIT_NOT_EXIST          -601                /* 获取功能限制点不存在 */
#define AUTH_LIC_ERR_GET_FUNCTION_LIMIT_VAULE_FAILED       -602                /* 获取功能现在点信息失败 */
#define AUTH_LIC_ERR_GET_BASE_LIMIT_VALUE_FAILED           -603                /* 获取基础限制点信息失败 */

/* 检查产品过期或功能点过期功能的错误范围 -701 ～ -800 */
#define AUTH_LIC_ERR_LESS_PRODUCT_START_USE_TIME           -701                /* 没有到使用产品的时间 */
#define AUTH_LIC_ERR_PRODUCT_EXPIRED                       -702                /* 产品时间过期 */
#define AUTH_LIC_ERR_LESS_FUNCTION_START_USE_TIME          -703                /* 没有到使用功能的时间 */
#define AUTH_LIC_ERR_FUNCTION_EXPIRED                      -704                /* 功能模块过期 */  
#define AUTH_LIC_ERR_GET_CURRENT_TIME_FAILED               -705                /* 获取当前时间失败 */
 
#pragma pack(4)
typedef struct
{
    char a_pro_version[MAX_CHAR_LEN];    /* 产品版本号 */
    char a_pro_id[MAX_CHAR_LEN];         /* 产品ID */
    char a_pro_serialid[MAX_CHAR_LEN];   /* 产品序列号 */
}st_pro_info_t;
#pragma pack()

/* 函数功能: 产品用于认证license文件是否合法 */
/* 参数:    p_lic_file_path :IN: 产品license文件路径指针 */
/*         p_st_pro_info :IN: 产品信息内容结构体指针 */
/* 返回值:  0: license文件合法 */
/*         < 0: license文件非法 */
int init_license(const char *p_lic_file_path, const st_pro_info_t *p_st_pro_info);

/* 函数功能:  获取机器码 */
/* 参数:      p_machine_code :OUT: 指向机器码内容的指针 */
/*           u32_machine_code_buf_len :IN: 指针指向空间的大小 */
/* 注意:      p_machine_code空间由调用者申请 */
int get_unique_code(char *p_machine_code, uint32_t u32_machine_code_buf_len);

/* 函数功能:  校验license文件数字签名 */
/* 参数:     p_lic_content :IN: license文件内容 */
/*          u32_content_len :IN: license文件内容长度 */
/* 返回值:   0: 校验license文件数据签名成功 */
/*          < 0:  校验license文件数字签名失败*/
int verify_license(const char *p_lic_content, uint32_t u32_content_len);

/* 函数功能:  更新license文件 */
/* 参数:      p_lic_content :IN: license文件内容 */
/*           u32_content_len :IN: license文件内容长度 */
/*           p_st_pro_info :IN:  产品信息内容结构体指针 */
/* 返回值:    0: 校验license文件成功 */
/*           < 0: 校验license文件失败 */
int update_license(const char *p_lic_content, uint32_t u32_content_len, const st_pro_info_t *p_st_pro_info);

/* 函数功能:  获取功能模块的限制信息内容 */
/* 参数:      p_product_id :IN: 产品ID号指针 */
/*           p_function_id :IN: 功能模块ID号指针  */
/*           p_function_limit_point :IN: 功能模块的限制点指针 */
/*           p_function_limit_value :OUT: 功能模块的限制点内容指针 */
/*           u32_function_limit_value_len :IN: 功能模块的限制点内容的长度 */
/* 返回值:    0: 获取功能限制点内容成功 */
/*           < 0: 获取功能限制点内容失败  */
int get_funtion_limit_info(const char *p_product_id
                         , const char *p_function_id
                         , const char *p_function_limit_point
                         , char *p_function_limit_value
                         , uint32_t u32_function_limit_value_len);

/* 函数功能: 获取基础限制点的内容 */
/* 参数:    p_product_id :IN: 产品ID指针 */
/*         p_base_limit_point :IN: 基础的限制点指针 */
/*         p_base_limit_value :OUT: 基础的限制点内容 */
/*         u32_base_limit_value_len :IN: 基础限制点内容的长度 */
/* 返回值:  0: 获取基础限制点内容成功 */
/*         < 0: 获取基础限制功能点失败 */
int get_product_base_info(const char *p_product_id
                        , const char *p_base_limit_point
                        , char *p_base_limit_value
                        , uint32_t u32_base_limit_value_len);

/* 函数功能:  检查产品或模块功能是否过期 */
/* 参数       p_product_id :IN: 产品ID指针 */
/*           p_function_id :IN: 功能模块ID指针 */
/* 返回值:    0: 产品或模块功能没有过期 */
/*           < 0: 产品或模块功能文件已经过期或错误 */
/* 说明:      p_function_id为NULL时，检查产品是否过期 */
/*           p_function_id不为NULL时，会检查产品和模块功能是否过期,两项均满足 */
int check_expired(const char *p_product_id, const char *p_function_id);

/* 函数功能：  释放license文件内容的cJSON空间 */
void release_lic_info();


#endif 
