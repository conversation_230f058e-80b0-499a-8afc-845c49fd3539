#ifndef __APP_TASK__
#define __APP_TASK__

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include <vector>
#include <map>
#include <sstream>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"

#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class AppTask : public oatpp::web::server::api::ApiController
{
public:
    AppTask(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
        : oatpp::web::server::api::Api<PERSON>ontroller(objectMapper)
        {}

    virtual ~AppTask(void);

public:
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> getTaskStatus(void);
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> setTaskStatus(String);
private:
    std::map<std::string, std::string> parseUrlEncodedData(const std::string& data) {
        std::map<std::string, std::string> result;
        std::istringstream stream(data);
        std::string pair;

        while (std::getline(stream, pair, '&')) {
            std::size_t pos = pair.find('=');
            if (pos != std::string::npos) {
                std::string key = pair.substr(0, pos);
                std::string value = pair.substr(pos + 1);
                result[key] = value;
            }
        }

        return result;
    }

};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif//AppHealth_hpp
