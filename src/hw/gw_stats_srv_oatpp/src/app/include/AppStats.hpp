#ifndef AppStats_hpp
#define AppStats_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include <ctime>
#include <vector>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"

#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class Stats : public oatpp::web::server::api::ApiController
{
public:
    Stats(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
        : oatpp::web::server::api::ApiController(objectMapper)
        , m_gwhw_log_path("/opt/apigw/gwhw/logs/hw.log")
        , m_gwhw_err_path("/opt/apigw/gwhw/logs/hw.err")
        , m_stats_file_dir("/opt/stats/")
        , m_stats_file("/opt/stats/stats.file")
        , m_stats_timeval_key("time_val")
        , m_gw_parser_cfg("/opt/data/apigw/gwhw/gw_parser.conf")
        , m_ssl_ips_file("/opt/stats/ssl_ips.file")
        , m_stats_limit(5000)
    {
        ifstream ifs(m_gw_parser_cfg);
        if(ifs.is_open())
        {
            std::string content( (istreambuf_iterator<char>(ifs)),(istreambuf_iterator<char>()) );
            rapidjson::Document doc;
            doc.Parse(content.c_str());
            rapidjson::Value& value = doc;
            m_stats_file_dir = value["parser"]["stats_dir"].GetString();
            m_stats_file = m_stats_file_dir + value["parser"]["stats_file"].GetString();
            ifs.close();
        }

        ifs.open("gw_stats_srv.conf",ios::in);
        if(ifs.is_open())
        {
            std::string content( (istreambuf_iterator<char>(ifs)),(istreambuf_iterator<char>()) );
            rapidjson::Document doc;
            doc.Parse(content.c_str());
            rapidjson::Value& value = doc;
            if(value.HasMember("stats_limit"))
            {
                m_stats_limit = value["stats_limit"].GetInt();
            }
            ifs.close();
        }
    }

    virtual ~Stats(void);

public:
    // 获取监控信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GwhwMonitoringInfo(void);
    // 获取状态信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetStats(Int64 startTime);
    // 查询网关模式
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetSourceFlag(void);
    // 获取最新一条状态信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetLastStats(void);
    // 获取错误日志
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetErrorLog(void);
    // 获取加密流量信息
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetEncryptedTraffic(void);
    
private:
    StatsInfo::Wrapper string_to_dto(std::string buf);

public:
    std::string get_last_stat_info();

private:
    std::string m_gwhw_log_path;
    std::string m_gwhw_err_path;
    std::string m_stats_file_dir;
    std::string m_stats_file;
    std::string m_stats_timeval_key;
    std::string m_gw_parser_cfg;
    std::string m_ssl_ips_file;
    uint32_t m_stats_limit;

};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif//AppStats_hpp