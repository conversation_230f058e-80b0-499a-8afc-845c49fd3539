#ifndef AppHealth_hpp
#define AppHealth_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <stdlib.h>
#include <vector>
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "dto/DTOs.hpp"
#include "rapidjson.h"
#include "document.h"

#include "AppEth.hpp"
#include "AppStats.hpp"
#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

class Health : public oatpp::web::server::api::ApiController
{
public:
    Health(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper), Eth* e = nullptr,  Stats* s = nullptr)
        : oatpp::web::server::api::ApiController(objectMapper)
    {
        eth = e;
        stats = s;
    }

    virtual ~Health(void);

public:
    std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> GetHealth(void);

private:
    Eth* eth;
    Stats* stats;
};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif//AppHealth_hpp