#include "AppStats.hpp"

Stats::~Stats(void)
{

}

StatsInfo::Wrapper Stats::string_to_dto(std::string buf)
{
    auto info = StatsInfo::createShared();
    rapidjson::Document doc;
    doc.Parse(buf.c_str());
    rapidjson::Value& value = doc;
    if(!value.IsObject())
    {
        return info;
    }

    if(value.HasMember("time_val"))
        info->time_val = value["time_val"].GetInt64();
    if(value.HasM<PERSON><PERSON>("recv_bytes_speed"))
        info->recv_bytes_speed = value["recv_bytes_speed"].GetFloat();
    if(value.HasMember("parser_http_cnt"))
        info->parser_http_cnt = value["parser_http_cnt"].GetInt64();
    if(value.HasMember("parser_http_speed"))
        info->parser_http_speed = value["parser_http_speed"].GetFloat();
    if(value.HasMember("parser_http_zip_num"))
        info->parser_http_zip_num = value["parser_http_zip_num"].GetInt64();
    if(value.HasMember("parser_http_zip_speed"))
        info->parser_http_zip_speed = value["parser_http_zip_speed"].GetFloat();
    if(value.HasMember("parser_succ_http_cnt"))
        info->parser_succ_http_cnt = value["parser_succ_http_cnt"].GetInt64();
    if(value.HasMember("up_kafka_cnt"))
        info->up_kafka_cnt = value["up_kafka_cnt"].GetInt64();
    if(value.HasMember("up_succ_kafka_cnt"))
        info->up_succ_kafka_cnt = value["up_succ_kafka_cnt"].GetInt64();
    if(value.HasMember("up_succ_kafka_speed"))
        info->up_succ_kafka_speed = value["up_succ_kafka_speed"].GetFloat();
    if(value.HasMember("up_succ_kafka_bytes_speed"))
        info->up_succ_kafka_bytes_speed = value["up_succ_kafka_bytes_speed"].GetFloat();
    if(value.HasMember("cpu_usage"))
        info->cpu_usage = value["cpu_usage"].GetFloat();
    if(value.HasMember("mem_usage"))
        info->mem_usage = value["mem_usage"].GetFloat();
    if(value.HasMember("source_flag"))
        info->source_flag = value["source_flag"].GetInt();

    // eth_info
    if(value.HasMember("eth_info"))
    {
        for(const auto& eth : value["eth_info"].GetArray())
        {
            auto num = EthInfo::createShared();
            if(eth.HasMember("device_name"))
                num->device_name = eth["device_name"].GetString();
            if(eth.HasMember("total_packets"))
                num->total_packets = eth["total_packets"].GetInt64();
            if(eth.HasMember("drop_packets"))
                num->drop_packets = eth["drop_packets"].GetInt64();
            if(eth.HasMember("eth_speed"))
            {
                if(eth["eth_speed"].IsString())
                {
                    num->eth_speed = 1000;
                }
                else
                {
                    num->eth_speed = eth["eth_speed"].GetInt64();
                }
            }
            if(eth.HasMember("card_stat"))
                num->card_stat = eth["card_stat"].GetString();
            info->eth_info->push_back(num);
        }
    }

    // filter
    if(value.HasMember("filter"))
    {
        auto filter = ParserInfo::createShared();
        // 修复：检查index字段是否存在
        if(value["filter"].HasMember("index"))
            filter->index = value["filter"]["index"].GetInt();
        if(value["filter"].HasMember("drop"))
        {
            rapidjson::Value& drop = doc["filter"]["drop"];
            if(drop.HasMember("black ip"))
                filter->drop->push_back({"black ip",value["filter"]["drop"]["black ip"].GetInt64()});
            if(drop.HasMember("global limit"))
                filter->drop->push_back({"global limit",value["filter"]["drop"]["global limit"].GetInt64()});
            if(drop.HasMember("ip except limit"))
                filter->drop->push_back({"ip except limit",value["filter"]["drop"]["ip except limit"].GetInt64()});
            if(drop.HasMember("ip limit"))
                filter->drop->push_back({"ip limit",value["filter"]["drop"]["ip limit"].GetInt64()});
            if(drop.HasMember("license expire"))
                filter->drop->push_back({"license expire",value["filter"]["drop"]["license expire"].GetInt64()});
            if(drop.HasMember("license limit"))
                filter->drop->push_back({"license limit",value["filter"]["drop"]["license limit"].GetInt64()});
            if(drop.HasMember("not ip"))
                filter->drop->push_back({"not ip",value["filter"]["drop"]["not ip"].GetInt64()});
        }
        // 修复：检查total字段是否存在
        if(value["filter"].HasMember("total"))
            filter->total = value["filter"]["total"].GetInt64();
        info->filter = filter;
    }

    // ip parser
    if(value.HasMember("ip parser"))
    {
        auto ip_parser = ParserInfo::createShared();
        // 修复：检查index字段是否存在
        if(value["ip parser"].HasMember("index"))
            ip_parser->index = value["ip parser"]["index"].GetInt();
        if(value["ip parser"].HasMember("drop"))
        {
            rapidjson::Value& drop = doc["ip parser"]["drop"];
            if(drop.HasMember("failed"))
                ip_parser->drop->push_back({"failed",value["ip parser"]["drop"]["failed"].GetInt64()});
            if(drop.HasMember("invalid ip"))
                ip_parser->drop->push_back({"invalid ip",value["ip parser"]["drop"]["invalid ip"].GetInt64()});
            if(drop.HasMember("invalid v4 gre"))
                ip_parser->drop->push_back({"invalid v4 gre",value["ip parser"]["drop"]["invalid v4 gre"].GetInt64()});
            if(drop.HasMember("invalid v6"))
                ip_parser->drop->push_back({"invalid v6",value["ip parser"]["drop"]["invalid v6"].GetInt64()});
            if(drop.HasMember("invalid v6 gre"))
                ip_parser->drop->push_back({"invalid v6 gre",value["ip parser"]["drop"]["invalid v6 gre"].GetInt64()});
            if(drop.HasMember("invalid v6 in v4"))
                ip_parser->drop->push_back({"invalid v6 in v4",value["ip parser"]["drop"]["invalid v6 in v4"].GetInt64()});
            if(drop.HasMember("invalid v6 in v6"))
                ip_parser->drop->push_back({"invalid v6 in v6",value["ip parser"]["drop"]["invalid v6 in v6"].GetInt64()});
            if(drop.HasMember("no momery"))
                ip_parser->drop->push_back({"no momery",value["ip parser"]["drop"]["no momery"].GetInt64()});
            if(drop.HasMember("not tcp"))
                ip_parser->drop->push_back({"not tcp",value["ip parser"]["drop"]["not tcp"].GetInt64()});
            if(drop.HasMember("overfull"))
                ip_parser->drop->push_back({"overfull",value["ip parser"]["drop"]["overfull"].GetInt64()});
            if(drop.HasMember("timeout"))
                ip_parser->drop->push_back({"timeout",value["ip parser"]["drop"]["timeout"].GetInt64()});
        }
        // 修复：检查total字段是否存在
        if(value["ip parser"].HasMember("total"))
            ip_parser->total = value["ip parser"]["total"].GetInt64();
        info->ip_parser = ip_parser;
    }

    // tcp parser
    if(value.HasMember("tcp parser"))
    {
        auto tcp_parser = ParserInfo::createShared();
        // 修复：检查index字段是否存在
        if(value["tcp parser"].HasMember("index"))
            tcp_parser->index = value["tcp parser"]["index"].GetInt();
        if(value["tcp parser"].HasMember("drop"))
        {
            rapidjson::Value& drop = doc["tcp parser"]["drop"];
            if(drop.HasMember("cache overfull"))
                tcp_parser->drop->push_back({"cache overfull",value["tcp parser"]["drop"]["cache overfull"].GetInt64()});
            if(drop.HasMember("close"))
                tcp_parser->drop->push_back({"close",value["tcp parser"]["drop"]["close"].GetInt64()});
            if(drop.HasMember("duplicate"))
                tcp_parser->drop->push_back({"duplicate",value["tcp parser"]["drop"]["duplicate"].GetInt64()});
            if(drop.HasMember("invalid"))
                tcp_parser->drop->push_back({"invalid",value["tcp parser"]["drop"]["invalid"].GetInt64()});
            if(drop.HasMember("no memory"))
                tcp_parser->drop->push_back({"no memory",value["tcp parser"]["drop"]["no memory"].GetInt64()});
            if(drop.HasMember("not regist"))
                tcp_parser->drop->push_back({"not regist",value["tcp parser"]["drop"]["not regist"].GetInt64()});
            if(drop.HasMember("port hit"))
                tcp_parser->drop->push_back({"port hit",value["tcp parser"]["drop"]["port hit"].GetInt64()});
            if(drop.HasMember("reset"))
                tcp_parser->drop->push_back({"reset",value["tcp parser"]["drop"]["reset"].GetInt64()});
            if(drop.HasMember("session overfull"))
                tcp_parser->drop->push_back({"session overfull",value["tcp parser"]["drop"]["session overfull"].GetInt64()});
            if(drop.HasMember("timeout"))
                tcp_parser->drop->push_back({"timeout",value["tcp parser"]["drop"]["timeout"].GetInt64()});
            if(drop.HasMember("ts check"))
                tcp_parser->drop->push_back({"ts check",value["tcp parser"]["drop"]["ts check"].GetInt64()});
        }
        // 修复：检查total字段是否存在
        if(value["tcp parser"].HasMember("total"))
            tcp_parser->total = value["tcp parser"]["total"].GetInt64();
        info->tcp_parser = tcp_parser;
    }

    // tcp stream
    if(value.HasMember("tcp stream"))
    {
        auto tcp_stream = ParserInfo::createShared();
        // 修复：检查index字段是否存在
        if(value["tcp stream"].HasMember("index"))
            tcp_stream->index = value["tcp stream"]["index"].GetInt();
        if(value["tcp stream"].HasMember("drop") && value["tcp stream"]["drop"].HasMember("lost"))
        {
            tcp_stream->drop->push_back({"lost",value["tcp stream"]["drop"]["lost"].GetInt64()});
        }
        // 修复：检查total字段是否存在
        if(value["tcp stream"].HasMember("total"))
            tcp_stream->total = value["tcp stream"]["total"].GetInt64();
        info->tcp_stream = tcp_stream;
    }

    // http
    if(value.HasMember("http"))
    {
        auto http = ParserInfo::createShared();
        // 修复：检查index字段是否存在
        if(value["http"].HasMember("index"))
            http->index = value["http"]["index"].GetInt();
        if(value["http"].HasMember("drop"))
        {
            rapidjson::Value& drop = doc["http"]["drop"];
            if(drop.HasMember("packet lost"))
                http->drop->push_back({"packet lost",value["http"]["drop"]["packet lost"].GetInt64()});
            if(drop.HasMember("payload too large"))
                http->drop->push_back({"payload too large",value["http"]["drop"]["payload too large"].GetInt64()});
            if(drop.HasMember("request"))
                http->drop->push_back({"request",value["http"]["drop"]["request"].GetInt64()});
            if(drop.HasMember("response"))
                http->drop->push_back({"response",value["http"]["drop"]["response"].GetInt64()});
            if(drop.HasMember("trunk failed"))
                http->drop->push_back({"trunk failed",value["http"]["drop"]["trunk failed"].GetInt64()});
            if(drop.HasMember("trunk too large"))
                http->drop->push_back({"trunk too large",value["http"]["drop"]["trunk too large"].GetInt64()});
            if(drop.HasMember("unzip failed"))
                http->drop->push_back({"unzip failed",value["http"]["drop"]["unzip failed"].GetInt64()});
        }
        // 修复：检查total字段是否存在
        if(value["http"].HasMember("total"))
            http->total = value["http"]["total"].GetInt64();
        info->http = http;
    }

    // kafka
    if(value.HasMember("kafka"))
    {
        auto kafka = ParserInfo::createShared();
        kafka->index = value["kafka"]["index"].GetInt();
        if(value["kafka"].HasMember("drop"))
        {
            rapidjson::Value& drop = doc["kafka"]["drop"];
            if(drop.HasMember("buffer overfull"))
                kafka->drop->push_back({"buffer overfull",value["kafka"]["drop"]["buffer overfull"].GetInt64()});
            if(drop.HasMember("cache overfull"))
                kafka->drop->push_back({"cache overfull",value["kafka"]["drop"]["cache overfull"].GetInt64()});
            if(drop.HasMember("failed"))
                kafka->drop->push_back({"failed",value["kafka"]["drop"]["failed"].GetInt64()});
            if(drop.HasMember("no buffer"))
                kafka->drop->push_back({"no buffer",value["kafka"]["drop"]["no buffer"].GetInt64()});
            if(drop.HasMember("no handler"))
                kafka->drop->push_back({"no handler",value["kafka"]["drop"]["no handler"].GetInt64()});
        }
        kafka->total = value["kafka"]["total"].GetInt64();
        info->kafka = kafka;
    }

    // ftp_parser_bytes
    if(value.HasMember("ftp_parser_bytes"))
    {
        auto ftp_parser_bytes = ParserBytesInfo::createShared();
        ftp_parser_bytes->index = value["ftp_parser_bytes"]["index"].GetInt();
        ftp_parser_bytes->total = value["ftp_parser_bytes"]["total"].GetInt64();
        info->ftp_parser_bytes = ftp_parser_bytes;
    }

    // http_parser_bytes
    if(value.HasMember("http parser bytes"))
    {
        auto http_parser_bytes = ParserBytesInfo::createShared();
        http_parser_bytes->index = value["http parser bytes"]["index"].GetInt();
        http_parser_bytes->total = value["http parser bytes"]["total"].GetInt64();
        // 修复：先检查detail字段是否存在
        if(value["http parser bytes"].HasMember("detail") && value["http parser bytes"]["detail"].HasMember("suc bytes"))
        {
            http_parser_bytes->detail->push_back({"suc bytes",value["http parser bytes"]["detail"]["suc bytes"].GetInt64()});
        }
        info->http_parser_bytes = http_parser_bytes;
    }

    // ip_bytes
    if(value.HasMember("ip_bytes"))
    {
        auto ip_bytes = ParserBytesInfo::createShared();
        ip_bytes->index = value["ip_bytes"]["index"].GetInt();
        ip_bytes->total = value["ip_bytes"]["total"].GetInt64();
        info->ip_bytes = ip_bytes;
    }

    // ssl_parser_bytes
    if(value.HasMember("ssl_parser_bytes"))
    {
        auto ssl_parser_bytes = ParserBytesInfo::createShared();
        ssl_parser_bytes->index = value["ssl_parser_bytes"]["index"].GetInt();
        ssl_parser_bytes->total = value["ssl_parser_bytes"]["total"].GetInt64();
        info->ssl_parser_bytes = ssl_parser_bytes;
    }

    // tcp_single_stream
    if(value.HasMember("tcp_single_stream"))
    {
        // 修复：添加字段存在性检查，确保安全访问
        if(value["tcp_single_stream"].HasMember("total"))
            info->tcp_single_stream->push_back({"total",value["tcp_single_stream"]["total"].GetInt64()});
        if(value["tcp_single_stream"].HasMember("only_client"))
            info->tcp_single_stream->push_back({"only_client",value["tcp_single_stream"]["only_client"].GetInt64()});
        if(value["tcp_single_stream"].HasMember("only_server"))
            info->tcp_single_stream->push_back({"only_server",value["tcp_single_stream"]["only_server"].GetInt64()});
        if(value["tcp_single_stream"].HasMember("normal"))
            info->tcp_single_stream->push_back({"normal",value["tcp_single_stream"]["normal"].GetInt64()});
    }

    return info;
}

std::string Stats::get_last_stat_info()
{
    std::ifstream readFile;
    readFile.open(m_stats_file,ios::in);
    std::string buf,forward,head;
    if(readFile.is_open())
    {
        while(std::getline(readFile,buf))
        {
            head = forward; // Get the penultimate row of data
            forward = buf;
        }
        readFile.close();
        return head;
    }
    else
    {
        return "";
    }
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GwhwMonitoringInfo(void)
{
    auto msg = MonitorMsg::createShared();    
    // 获取cpu占用率
    FILE *fp = popen("top -n1 -b | grep gw_parser | awk '{print $9}'","r"); 
    char buffer[16] = "0";
    std::string cpu = "";
    if(fp)
    {
        fgets(buffer,sizeof(buffer),fp);
        buffer[strlen(buffer)-1] = '\0';
        cpu = buffer;
        pclose(fp);
    }

    fp = popen("cat /proc/cpuinfo | grep 'processor' | wc -l","r"); //cpu_num
    if(fp)
    {
        fgets(buffer,sizeof(buffer),fp);
        buffer[strlen(buffer)-1] = '\0';
        std::string cpu_num = buffer;
        if(cpu != "")
            sprintf(buffer, "%.1f", std::stof(cpu)/std::stoi(cpu_num));
        else
            sprintf(buffer, "%.1f", 0.0);
        msg->data->push_back({"cpu",buffer}); 
        memset(buffer,0,sizeof(buffer));
        pclose(fp);
    }

    // 获取内存占用率
    fp = popen("top -n1 -b | grep gw_parser | awk '{print $10}'","r"); 
    if(fp)
    {
        fgets(buffer,sizeof(buffer),fp);
        buffer[strlen(buffer)-1] = '\0';
        std::string mem = buffer;
        if(mem == "")
            sprintf(buffer, "%.1f", 0.0);
        msg->data->push_back({"memory",buffer});  
        memset(buffer,0,sizeof(buffer));
        pclose(fp);
    }

    // 获取流量
    fp = popen(("cat " + m_gwhw_log_path + " | grep -w 'stats qps' -A15| grep  'ip bytes:' |tail -n 1 |awk '{ print $(NF -1)} '").c_str(),"r");
    if(fp)
    {
        fgets(buffer,sizeof(buffer),fp);
        buffer[strlen(buffer)-1] = '\0';
        msg->data->push_back({"flow",buffer}); 
        msg->data->push_back({"time",std::to_string(time(NULL))});  
        pclose(fp);
    }

    return createDtoResponse(Status::CODE_200, msg);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GetStats(Int64 startTime)
{
    auto dto = StatsMsg::createShared();
    std::ifstream readFile;
    readFile.open(m_stats_file,ios::in);
    std::string buf;
    rapidjson::Document doc;
    if(readFile.is_open())
    {
        if(startTime == 0)
        {
            while(std::getline(readFile,buf))
            {
                try
                {
                    doc.Parse(buf.c_str());
                    if(!doc.HasParseError())
                    {
                        dto->data->push_back(string_to_dto(buf));
                    }
                    if(dto->data->size() > m_stats_limit)
                        break;
                }
                catch(const std::exception& e)
                {
                    LOG_ERROR("get stats fail, %s",e.what());
                    break;
                }
            }  
        }
        else
        {
            while(std::getline(readFile,buf))
            {
                try
                {
                    doc.Parse(buf.c_str());
                    rapidjson::Value& value = doc;
                    long long time_val = 0;
                    if(!doc.HasParseError() && value.HasMember("time_val"))
                    {
                        time_val = value["time_val"].GetInt64();
                    }
                    if(time_val >= startTime)
                    { 
                        dto->data->push_back(string_to_dto(buf));
                        if(dto->data->size() > m_stats_limit)
                            break;
                    } 
                }
                catch(const std::exception& e)
                {
                    LOG_ERROR("get stats fail, %s",e.what());
                    break;
                }
            }  
        }
        readFile.close();
    }
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GetSourceFlag(void)
{
    auto dto = FlagMsg::createShared();
    std::string buf = get_last_stat_info();
    rapidjson::Document doc;
    doc.Parse(buf.c_str());
    if(buf == "" || doc.HasParseError())
    {
        dto->msg = "err";
        dto->err = -1;
        return createDtoResponse(Status::CODE_200, dto);
    }
    rapidjson::Value& value = doc;
    int source_flag = 0;
    if(value.HasMember("source_flag"))
    {
        source_flag = value["source_flag"].GetInt();
    }

    // 需要再判断绑定的网口
    bool has_lo = false;
    bool has_other = false;
    if(value.HasMember("eth_info") && value["eth_info"].IsArray())
    {
        rapidjson::Value& eth_infos = value["eth_info"];
        for (rapidjson::SizeType i = 0; i < eth_infos.Size(); i++) {
            rapidjson::Value& eth_info = eth_infos[i];
            if (eth_info.HasMember("device_name") && eth_info["device_name"].IsString()) {
                string dev_name = eth_info["device_name"].GetString();
                if (dev_name == "lo")
                    has_lo = true;
                else
                    has_other = true;
            }
        }
    }

    if (source_flag & 1)
        dto->data->push_back("AGENT+Mirror(dpdk)");
    else if (source_flag & 8)
    {
        dto->data->push_back("AGENT+Mirror(libpcap)");
    }
    if (source_flag & 2)
        dto->data->push_back("FILE");
    if (source_flag & 4)
    {
        if (has_lo && !has_other)
        {
            dto->data->push_back("AGENT");
        }
        else if (has_lo && has_other)
        {
            dto->data->push_back("AGENT+Mirror(pf_ring)");
        }
        else if (!has_lo)
        {
            dto->data->push_back("Mirror(pf_ring)");
        }
    }

    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GetLastStats(void)
{
    auto dto = StatMsg::createShared();
    std::string statInfo = get_last_stat_info();
    if(statInfo == "")
    {
        dto->msg = "err";
        dto->err = 1;
        return createDtoResponse(Status::CODE_200, dto);
    }
  
    dto->data = string_to_dto(statInfo);
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GetErrorLog(void)
{
    auto dto = ErrLog::createShared();
    ifstream ifs(m_gwhw_err_path);
    std::vector<std::string> lines;
    if(ifs.is_open())
    {
        std::string line;
        while(getline(ifs,line))
        {
            if(line.find("[ERROR]") != std::string::npos)
                lines.push_back(line);
        }
        int log_size = 0;
        for(auto it = lines.rbegin();it != lines.rend();it++)
        {
            // 错误日志留存最大10MB
            if(log_size + (*it).length() > 10000000)
            {
                break;
            }
            log_size += (*it).length();
            dto->data->push_front(*it);
        }
        ifs.close();
    }
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Stats::GetEncryptedTraffic(void)
{
    auto dto = SslMsg::createShared();
    std::string statInfo = get_last_stat_info();
    rapidjson::Document doc;
    doc.Parse(statInfo.c_str());
    if(statInfo == "" || doc.HasParseError())
    {
        dto->msg = "err";
        dto->err = 1;
        return createDtoResponse(Status::CODE_200, dto);
    }
    rapidjson::Value& value = doc;

    uint64_t m_ftp_cnt = 0, m_http_cnt = 0, m_ssl_cnt = 0, m_total_cnt = 0, m_ssl_parser_bits = 0;

    auto ssl = SslInfo::createShared();
    // 修复：添加子字段存在性检查
    if(value.HasMember("ftp_parser_bytes") && value["ftp_parser_bytes"].HasMember("total"))
        m_ftp_cnt = value["ftp_parser_bytes"]["total"].GetInt64() * 8;
    ssl->proportion->push_back({"ftp_cnt", m_ftp_cnt});
    if(value.HasMember("http parser bytes") && value["http parser bytes"].HasMember("total"))
        m_http_cnt = value["http parser bytes"]["total"].GetInt64() * 8;
    ssl->proportion->push_back({"http_cnt", m_http_cnt});
    if(value.HasMember("ssl_parser_bytes") && value["ssl_parser_bytes"].HasMember("total"))
        m_ssl_cnt = value["ssl_parser_bytes"]["total"].GetInt64() * 8;
    ssl->proportion->push_back({"ssl_cnt", m_ssl_cnt});
    if(value.HasMember("ip_bytes") && value["ip_bytes"].HasMember("total"))
        m_total_cnt = value["ip_bytes"]["total"].GetInt64() * 8;
    ssl->proportion->push_back({"total_cnt", m_total_cnt});
    if(value.HasMember("ssl_parser_bytes") && value["ssl_parser_bytes"].HasMember("total"))
        m_ssl_parser_bits = value["ssl_parser_bytes"]["total"].GetInt64() * 8;
    ssl->ssl_parser_bits = m_ssl_parser_bits;
    ssl->timestamp = time(NULL) * 1000;

    std::ifstream readFile;
    readFile.open(m_ssl_ips_file,ios::in);
    std::string buf = "";
    if(readFile.is_open())
    {
        while(std::getline(readFile,buf))
        {
            auto ips = SslIps::createShared();
            std::string ip(buf.begin(), buf.begin() + buf.find(","));
            buf = buf.substr(buf.find(",") + 1);
            std::string suite(buf.begin(), buf.begin() + buf.find(","));
            buf = buf.substr(buf.find(",") + 1);
            std::string version(buf.begin(), buf.begin() + buf.length());
            ips->suite = suite;
            ips->version = version;
            ssl->ssl_ips->push_back({ip,ips});
        }
        readFile.close();
    }

    dto->data = ssl;
    return createDtoResponse(Status::CODE_200, dto);
}
