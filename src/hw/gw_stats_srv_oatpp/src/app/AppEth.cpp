#include <sys/types.h>
#include <dirent.h>
#include <sstream>
#include <chrono>
#include <thread>
#include "AppEth.hpp"

std::map<std::string, std::string> Eth::eth_speed = {};
std::mutex Eth::mx;

void Eth::system_cmd(std::string cmd, int timestamp)
{
    std::chrono::seconds timerDuration(timestamp);
    std::this_thread::sleep_for(timerDuration);
    std::system(cmd.c_str());
}

void Eth::find_pci(std::string ethname)
{
    // 使用dmesg命令通过网卡名查找对应的PCI设备ID
    std::string cmd = "dmesg | grep -E \"" + ethname + ":\" | grep -o \"[0-9a-f]\\{4\\}:[0-9a-f]\\{2\\}:[0-9a-f]\\{2\\}\\.[0-9a-f]\\{1\\}\" | tail -1";
    FILE* pipe = popen(cmd.c_str(), "r");
    if(pipe)
    {
        char buffer[128];
        std::string pci_id = "";
        while(!feof(pipe))
        {
            if(fgets(buffer, 128, pipe) != NULL)
            {
                pci_id = buffer;
                // 移除末尾的换行符
                if(!pci_id.empty() && pci_id[pci_id.length()-1] == '\n')
                    pci_id.erase(pci_id.length()-1);
                
                if(!pci_id.empty())
                    eth_pci_map[ethname] = pci_id;
            }
        }
        pclose(pipe);
    }
}

Eth::~Eth(void)
{

}

void Eth::get_eth_basic_info(void)
{
    // 确保列表为空，避免重复添加
    all_eth.clear();
    virtual_eth.clear();
    physics_eth.clear();
    eth_pci_map.clear();
    
    std::ifstream readFile;
    readFile.open(m_all_eth_file,ios::in);
    std::string buf;
    std::string::size_type index;
    if(readFile.is_open())
    {
        while(std::getline(readFile,buf))
        {
            if((index = buf.find(':')) == std::string::npos)
            {
                continue;
            }
            std::string eth(buf.begin(),buf.begin() + index);
            eth.erase(0,eth.find_first_not_of(' '));  // remove opening space
            all_eth.push_back(eth);
        }
        readFile.close();
    }

    // get virtual eth
    DIR *dirp = opendir(m_virtual_eth_dir.c_str());
    if(dirp == nullptr)
    {
        return;
    }
    struct dirent *pd;
    while((pd = readdir(dirp)) != nullptr)
    {
        if(strncmp(".",pd->d_name,1) == 0 || strncmp("..",pd->d_name,2) == 0)
            continue;
        virtual_eth.push_back(pd->d_name);
    }
    closedir(dirp);

    // get physics eth
    for(auto eth : all_eth)
    {
        if(std::find(std::begin(virtual_eth), std::end(virtual_eth), eth) == std::end(virtual_eth))
        {
            physics_eth.push_back(eth);
        }
    }
    physics_eth.push_back("lo");

    // 获取网卡名和PCI设备ID的映射关系
    for(auto eth_name : physics_eth)
    {
        // 跳过lo口，lo口没有PCI信息
        if(eth_name == "lo")
            continue;

        find_pci(eth_name);
    }
}

void Eth::get_eth_speed(std::string ethname)
{
    while(1)
    {
        int sleeptime = 5;
        uint64_t RX_before_pack = 0;
        uint64_t TX_before_pack = 0;
        uint64_t RX_after_pack = 0;
        uint64_t TX_after_pack = 0;
        std::string cmd = "ifconfig " + ethname;
        FILE *pipe = popen(cmd.c_str(), "r");
        if(pipe)
        {
            char buffer[1024];
            while (!feof(pipe)) 
            {
                if (fgets(buffer, 1024, pipe) != NULL)
                {
                    std::string line = buffer;
                    if(line.find("RX packets") != std::string::npos)
                    {
                        std::string bytes(line.begin() + line.find("bytes") + 6, line.begin() + line.find(" ("));
                        RX_before_pack = stoull(bytes);
                    }
                    if(line.find("TX packets") != std::string::npos)
                    {
                        std::string bytes(line.begin() + line.find("bytes") + 6, line.begin() + line.find(" ("));
                        TX_before_pack = stoull(bytes);
                    }
                }
            }
            pclose(pipe);
        }
        std::this_thread::sleep_for(std::chrono::seconds(sleeptime));
        pipe = popen(cmd.c_str(), "r");
        if(pipe)
        {
            char buffer[1024];
            while (!feof(pipe)) 
            {
                if (fgets(buffer, 1024, pipe) != NULL)
                {
                    std::string line = buffer;
                    if(line.find("RX packets") != std::string::npos)
                    {
                        std::string bytes(line.begin() + line.find("bytes") + 6, line.begin() + line.find(" ("));
                        RX_after_pack = stoull(bytes);
                    }
                    if(line.find("TX packets") != std::string::npos)
                    {
                        std::string bytes(line.begin() + line.find("bytes") + 6, line.begin() + line.find(" ("));
                        TX_after_pack = stoull(bytes);
                    }
                }
            }
            pclose(pipe);
        }

        // if rx&&tx before pack == 0, filter
        if (RX_before_pack == 0)
            continue;

        if(RX_after_pack > RX_before_pack || TX_after_pack > TX_before_pack)
        {
            uint64_t RX_speed = (RX_after_pack - RX_before_pack) / sleeptime;
            uint64_t TX_speed = (TX_after_pack - TX_before_pack) / sleeptime;
            //char rx[10],tx[10];
            //sprintf(rx,"%.2f", RX_speed);
            //sprintf(tx,"%.2f", TX_speed);
            std::string speed = std::to_string(RX_speed) + "," + std::to_string(TX_speed);
            mx.lock();
            eth_speed[ethname] = speed;
            mx.unlock();
        }
    }
}

int Eth::str_count(char *str, char *substr)
{
    char * ans = strstr(str, substr);
    int cnt = 0;
    int len = strlen(substr);
    while(ans)
    {
        cnt++;
        ans = strstr(ans + len , substr);
    }
    return cnt;
}

void Eth::delete_space(std::string &s) 
{
    if (s.empty()) 
    {
        return ;
    }
    s.erase(0,s.find_first_not_of("\t"));
    s.erase(0,s.find_first_not_of(" "));
    s.erase(s.find_last_of("\n"));
}

EthInfoDetail::Wrapper Eth::get_eth_info(std::string ethname)
{
    auto dto = EthInfoDetail::createShared();
    dto->eth_name = ethname;
    dto->speed_unit = "B/s";

    std::string cmd = "/opt/apigw/gwhw/tools/ethtool " + ethname;
    FILE* pipe = popen(cmd.c_str(), "r");
    if (pipe) 
    {
        char buffer[1024];
        bool ethtool_flag = false;
        std::vector<std::string> eth_speed;
        while (!feof(pipe)) 
        {
            if (fgets(buffer, 1024, pipe) != NULL)
            {
                std::string line = buffer;
                if(line.find("Supported link modes:") != std::string::npos)
                {
                    ethtool_flag = true;
                    std::string speed(line.begin() + line.find(":") + 1, line.end());
                    delete_space(speed);
                    while(speed.find(" ") != std::string::npos)
                    {
                        std::string eth(speed.begin(), speed.begin() + speed.find(" "));
                        speed = speed.substr(speed.find(" ") + 1);
                        eth_speed.push_back(eth);
                    }
                }
                else if((ethtool_flag == true) && (line.find(":") == std::string::npos))
                {
                    delete_space(line);
                    while(line.find(" ") != std::string::npos)
                    {
                        std::string eth(line.begin(), line.begin() + line.find(" "));
                        line = line.substr(line.find(" ") + 1);
                        eth_speed.push_back(eth);
                    }
                }
                else if((ethtool_flag == true) && (line.find(":") != std::string::npos))
                {
                    break;
                }
            }
        }

        if(eth_speed.size() != 0)
        {
            dto->eth_speed = *(eth_speed.rbegin());
        }
        else
        {
            dto->eth_speed = "UNKOWN!";
        }
        pclose(pipe);
    }

    cmd = "ifconfig " + ethname;
    pipe = popen(cmd.c_str(), "r");
    std::string ifconfig_read = "";
    if(pipe)
    {
        char buffer[1024];
        while (!feof(pipe)) 
        {
            if (fgets(buffer, 1024, pipe) != NULL)
            {
                ifconfig_read += buffer;
            }
        }
        pclose(pipe);
    }
    if((ifconfig_read.find("MASTER") != std::string::npos) || (ifconfig_read.find("SLAVE") != std::string::npos))
    {
        dto->mirrorable = 0;
    }
    else
    {
        dto->mirrorable = 1;
    }

    if(ifconfig_read.find("UP") != std::string::npos)
    {
        dto->status = "up";
    }
    else if(ifconfig_read.find("DOWN") != std::string::npos)
    {
        dto->status = "down";
    }
    // 获取管理口
    if(str_count((char *)(ifconfig_read.c_str()), const_cast<char *>("inet")) >= 2)
    {
        std::string ipv4(ifconfig_read.begin() + ifconfig_read.find("inet") + 5, ifconfig_read.begin() + ifconfig_read.find(" netmask") - 1);
        dto->ip = ipv4;
    }
    else
    {
        dto->ip = "";
    }
    // 获取镜像口
    dto->mirror = 0;
    if(ifconfig_read.find("RUNNING") != std::string::npos)
    {
        if(str_count((char *)(ifconfig_read.c_str()), const_cast<char *>("inet")) == 0)
        {
            dto->mirror = 1;
        }
        else if(str_count((char *)(ifconfig_read.c_str()), const_cast<char *>("inet")) == 1 && str_count((char *)(ifconfig_read.c_str()), const_cast<char *>("inet6")) == 1)
        {
            dto->mirror = 1;
        }
    }

    if(eth_speed[ethname] != "")
    {
        std::string rx_speed(eth_speed[ethname].begin(), eth_speed[ethname].begin() + eth_speed[ethname].find(","));
        std::string tx_speed(eth_speed[ethname].begin() + eth_speed[ethname].find(",") + 1, eth_speed[ethname].end());
        dto->rx_speed = rx_speed;
        dto->tx_speed = tx_speed;
    }
    else
    {
        dto->rx_speed = dto->tx_speed = "";
    }
    
    if(dto->rx_speed->length() != 0 || dto->tx_speed->length() != 0)
    {
        dto->flow = 1;
    }
    else
    {
        dto->flow = 0;
    }

    return dto;
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Eth::GetEthInformation(Boolean health)
{
    auto dto = EthMsg::createShared();
    auto eth_info = EthInformation::createShared();

    if(0 == mode_name.size())
    {
        if(health = true)
        {
            ethinfo = eth_info;
            return createResponse(Status::CODE_200, "");
        }
        dto->msg = "source_mode error: source_mode shouldn't be None";
        dto->err = 100;
        return createDtoResponse(Status::CODE_200, dto);
    }

    if(1 == mode_name.size() && mode_name[0] == "agent")
    {
        if(health == true)
        {
            ethinfo = eth_info;
            return createResponse(Status::CODE_200, "");
        }
        eth_info->source_mode->push_back(mode_name[0]);
        dto->data = eth_info;
        return createDtoResponse(Status::CODE_200, dto);
    }

    for(size_t i = 0; i < mode_name.size(); i++)
        eth_info->source_mode->push_back(mode_name[i]);

    if(std::find(mode_name.begin(),mode_name.end(), "nic") != std::end(mode_name) || std::find(mode_name.begin(),mode_name.end(), "pcap") != std::end(mode_name))
    {
        std::ifstream file(m_gw_parser_cfg);
        std::string file_json((std::istreambuf_iterator<char>(file)),std::istreambuf_iterator<char>());
        rapidjson::Document gw_parser_conf_doc;
        gw_parser_conf_doc.Parse(file_json.c_str());
        rapidjson::Value& gw_parser_conf = gw_parser_conf_doc;
        file.close();

        std::vector<std::string> nic_device_name;
        // lo口默认绑定，且不能被解绑
        nic_device_name.push_back("lo");
        std::string all_device_name = gw_parser_conf["parser"]["nic_device_name"].GetString();
        while(all_device_name.find(",") != std::string::npos)
        {
            std::string eth(all_device_name.begin(), all_device_name.begin() + all_device_name.find(","));
            all_device_name = all_device_name.substr(all_device_name.find(",") + 1);
            nic_device_name.push_back(eth);
        }
        nic_device_name.push_back(all_device_name);

        for(auto eth_name = physics_eth.begin(); eth_name != physics_eth.end(); ++eth_name)
        {
            auto detail = EthInfoDetail::createShared();
            detail = get_eth_info(*eth_name);
            if(strcmp((*eth_name).c_str(),"lo") == 0)
            {
                detail->eth_name = "agentPort";
            }
            if(std::find(nic_device_name.begin(),nic_device_name.end(),*eth_name) != std::end(nic_device_name))
            {
                eth_info->bind->push_back(detail);
            }
            else
            {
                eth_info->unbind->push_back(detail);
            }
        }

        if ((std::find(mode_name.begin(),mode_name.end(), "dpdk") == std::end(mode_name)))
        {
            if(health == true)
            {
                ethinfo = eth_info;
                return createResponse(Status::CODE_200, "");
            }
            dto->data = eth_info;
            return createDtoResponse(Status::CODE_200, dto);
        }
    }

    if(std::find(mode_name.begin(),mode_name.end(), "dpdk") != std::end(mode_name))
    {
        // 重新获取网卡信息，确保PCI映射是最新的
        refresh_eth_basic_info();
        
        std::ifstream file(m_dpdk_white_list);
        std::string buf = "";
        std::vector<std::string> dpdk_bind;
        if(file.is_open())
        {
            while(std::getline(file,buf))
            {
                // 跳过注释行和空行
                if(buf.find("#") != std::string::npos || buf.empty())
                    continue;
                // 去除行首行尾空白字符
                buf.erase(0, buf.find_first_not_of(" \t\r\n"));
                buf.erase(buf.find_last_not_of(" \t\r\n") + 1);
                // 如果非空则添加到数组
                if(!buf.empty()) {
                    dpdk_bind.push_back(buf);
                }
            }
            file.close();
        }
        // bind
        for(auto eth_name : dpdk_bind)
        {
            auto detail = EthInfoDetail::createShared();
            detail->eth_name = eth_name;
            detail->speed_unit = "B/s";
            detail->ip = "";
            detail->mirror = 0;     // DPDK模式下不支持镜像
            detail->mirrorable = 0; // DPDK模式下不支持镜像
            
            // 从eth_pci_map中获取该网卡的PCI ID
            std::string pci_id = "";
            if(eth_pci_map.find(eth_name) != eth_pci_map.end()) {
                pci_id = eth_pci_map[eth_name];
            }
            
            if(!pci_id.empty())
            {
                // 通过PCI ID从hw.log找到port id
                std::string cmd = "tac /opt/apigw/gwhw/logs/hw.log* | grep -m1 -E \"name: " + pci_id + "\" -B50";
                FILE* pipe = popen(cmd.c_str(), "r");
                std::string portid_info = "";
                if(pipe)
                {
                    char buffer[4096];
                    while(!feof(pipe))
                    {
                        if(fgets(buffer, 4096, pipe) != NULL)
                        {
                            portid_info += buffer;
                        }
                    }
                    pclose(pipe);
                }
                
                // 从日志内容中解析port id
                std::string port_id = "";
                
                // 首先尝试直接匹配"port X name: PCI_ID"格式，这是最准确的方式
                std::string pattern = "port [0-9]+ name: " + pci_id;
                std::string direct_cmd = "tac /opt/apigw/gwhw/logs/hw.log* | grep -m1 -E \"" + pattern + "\" | head -1";
                FILE* direct_pipe = popen(direct_cmd.c_str(), "r");
                bool direct_match_success = false;
                
                if(direct_pipe)
                {
                    char direct_buffer[1024];
                    std::string direct_match = "";
                    if(fgets(direct_buffer, 1024, direct_pipe) != NULL)
                    {
                        direct_match = direct_buffer;
                        // 移除末尾的换行符
                        if(!direct_match.empty() && direct_match[direct_match.length()-1] == '\n')
                            direct_match.erase(direct_match.length()-1);
                        
                        // 从"port X name: PCI_ID"中提取"port X"
                        if(direct_match.find("port ") != std::string::npos && direct_match.find(" name:") != std::string::npos)
                        {
                            size_t port_pos = direct_match.find("port ");
                            size_t name_pos = direct_match.find(" name:");
                            if(port_pos != std::string::npos && name_pos != std::string::npos && port_pos < name_pos)
                            {
                                port_id = direct_match.substr(port_pos, name_pos - port_pos);
                                direct_match_success = true;
                            }
                        }
                    }
                    pclose(direct_pipe);
                }
                
                // 如果直接匹配失败，使用原来的backlog检索方法，但改进为从后向前搜索
                if(!direct_match_success && !portid_info.empty())
                {
                    // 先查找具体的设备信息行，格式通常是"port X name: PCI_ID"
                    std::string device_pattern = "port [0-9]+ name: " + pci_id;
                    size_t last_pos = portid_info.rfind(pci_id);
                    
                    if(last_pos != std::string::npos)
                    {
                        // 向前搜索找到这行的开始（通常是"port "开头）
                        size_t line_start = portid_info.rfind("\n", last_pos);
                        if(line_start == std::string::npos) {
                            line_start = 0;
                        } else {
                            line_start++; // 跳过换行符
                        }
                        
                        std::string device_line = portid_info.substr(line_start, last_pos + pci_id.length() - line_start);
                        
                        // 在这行中找到"port X"部分
                        if(device_line.find("port ") == 0 && device_line.find(" name:") != std::string::npos)
                        {
                            size_t name_pos = device_line.find(" name:");
                            port_id = device_line.substr(0, name_pos);
                        }
                    }
                    else
                    {
                        // 使用原始搜索方法作为备选
                        size_t pos = portid_info.find("port ");
                        if(pos != std::string::npos)
                        {
                            // 检查此处的"port"是否为"port conf:"
                            std::string check_conf = portid_info.substr(pos, 10);
                            if(check_conf.find("port conf") != std::string::npos)
                            {
                                // 继续往后搜索"port "
                                pos = portid_info.find("port ", pos + 10);
                            }
                            
                            if(pos != std::string::npos)
                            {
                                std::string tmp = portid_info.substr(pos, 20); // 增加长度以包含更多信息
                                
                                // 检查是否包含数字
                                bool has_digit = false;
                                for(size_t i = 5; i < tmp.length(); i++) {
                                    if(isdigit(tmp[i])) {
                                        has_digit = true;
                                        break;
                                    }
                                }
                                
                                if(has_digit)
                                {
                                    size_t space_pos = tmp.find(" name");
                                    if(space_pos != std::string::npos)
                                    {
                                        port_id = tmp.substr(0, space_pos);
                                    }
                                    else
                                    {
                                        // 尝试解析出端口号
                                        size_t digit_start = 0;
                                        size_t digit_end = 0;
                                        for(size_t i = 5; i < tmp.length(); i++) {
                                            if(isdigit(tmp[i]) && digit_start == 0) {
                                                digit_start = i;
                                            } else if(digit_start > 0 && !isdigit(tmp[i])) {
                                                digit_end = i;
                                                break;
                                            }
                                        }
                                        
                                        if(digit_start > 0) {
                                            if(digit_end == 0) {
                                                digit_end = tmp.length();
                                            }
                                            port_id = "port " + tmp.substr(digit_start, digit_end - digit_start);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                if(!port_id.empty())
                {
                    // 通过port id解析出数字
                    int port_num = -1;
                    int scan_result = sscanf(port_id.c_str(), "port %d", &port_num);
                    
                    if(scan_result == 1 && port_num >= 0)
                    {
                        // 从hw.log获取网卡统计信息
                        int col_num = port_num + 3;
                        
                        // 获取速率
                        cmd = "tail -200 /opt/apigw/gwhw/logs/hw.log | grep Speed | head -n1 | awk '{print $" + std::to_string(col_num) + "}'";
                        pipe = popen(cmd.c_str(), "r");
                        if(pipe)
                        {
                            char buffer[128];
                            std::string speed = "";
                            while(!feof(pipe))
                            {
                                if(fgets(buffer, 128, pipe) != NULL)
                                {
                                    speed = buffer;
                                    // 移除末尾的换行符
                                    if(!speed.empty() && speed[speed.length()-1] == '\n')
                                        speed.erase(speed.length()-1);
                                    
                                    detail->eth_speed = String(speed + "baseT/Full");
                                }
                            }
                            pclose(pipe);
                        }
                        
                        // 获取状态
                        cmd = "tail -200 /opt/apigw/gwhw/logs/hw.log | grep Staus | head -n1 | awk '{print $" + std::to_string(col_num) + "}'";
                        pipe = popen(cmd.c_str(), "r");
                        if(pipe)
                        {
                            char buffer[128];
                            std::string status = "";
                            while(!feof(pipe))
                            {
                                if(fgets(buffer, 128, pipe) != NULL)
                                {
                                    status = buffer;
                                    // 移除末尾的换行符
                                    if(!status.empty() && status[status.length()-1] == '\n')
                                        status.erase(status.length()-1);
                                    
                                    std::transform(status.begin(), status.end(), status.begin(), ::tolower);
                                    detail->status = String(status);
                                }
                            }
                            pclose(pipe);
                        }
                        
                        // 解析收包字节速率
                        cmd = "tail -200 /opt/apigw/gwhw/logs/hw.log | grep 'Rate(bps):' | head -n1 | awk '{print $" + std::to_string(col_num) + "}'";
                        pipe = popen(cmd.c_str(), "r");
                        if(pipe)
                        {
                            char buffer[128];
                            std::string rate_bytes = "";
                            while(!feof(pipe))
                            {
                                if(fgets(buffer, 128, pipe) != NULL)
                                {
                                    rate_bytes = buffer;
                                    // 移除末尾的换行符
                                    if(!rate_bytes.empty() && rate_bytes[rate_bytes.length()-1] == '\n')
                                        rate_bytes.erase(rate_bytes.length()-1);
                                    
                                    detail->rx_speed = String(std::to_string(std::stoll(rate_bytes) / 8));
                                }
                            }
                            pclose(pipe);
                        }
                        
                        detail->tx_speed = ""; // DPDK 没有发包速率
                        
                        if(!detail->rx_speed->empty())
                        {
                            detail->flow = 1;
                            LOG_DEBUG("[AppEth] 网卡[%s]流量状态: 有流量", eth_name.c_str());
                        }
                        else
                        {
                            detail->flow = 0;
                            LOG_DEBUG("[AppEth] 网卡[%s]流量状态: 无流量", eth_name.c_str());
                        }
                    }
                }
            }
            
            eth_info->bind->push_back(detail);
        }

        // unbind
        eth_info->unbind->clear();
        for(auto eth_name = physics_eth.begin(); eth_name != physics_eth.end(); ++eth_name)
        {
            bool is_bound = false;
            // 检查是否在bind列表中
            for(auto it = eth_info->bind->begin(); it != eth_info->bind->end(); ++it) {
                if((*eth_name).compare((*it)->eth_name) == 0 || strcmp((*eth_name).c_str(), "lo") == 0) {
                    is_bound = true;
                    break;
                }
            }
            if(is_bound) {
                continue;
            }

            auto detail = EthInfoDetail::createShared();
            detail = get_eth_info(*eth_name);
            eth_info->unbind->push_back(detail);
        }
    }

    if(health == true)
    {
        ethinfo = eth_info;
        return createResponse(Status::CODE_200, "");
    }
    dto->data = eth_info;
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Eth::BindEth(String content)
{
    auto dto = SettingMsg::createShared();

    if(0 == mode_name.size())
    {
        dto->error_msg = "source_mode error: source_mode shouldn't be None";
        dto->success = false;
        return createDtoResponse(Status::CODE_200, dto);
    }

    if(1 == mode_name.size() && mode_name[0] == "agent")
    {
        dto->error_msg = "The mode agent has no method to bind";
        dto->success = false;
        return createDtoResponse(Status::CODE_200, dto);
    }

    try {
        dto->error_msg = "get json content fail";
        rapidjson::Document doc;
        doc.Parse(content->c_str());

        if (std::find(mode_name.begin(),mode_name.end(), "dpdk") != std::end(mode_name))
        {
            dto->error_msg = "clean huge memory fail";
            system("bash /opt/apigw/gwhw/tools/dpdk_unbind.sh 1> /dev/null");

            std::vector<std::string> dpdk_white_list; // 原白名单
            std::vector<std::string> unbind_eths; // 需要解绑的网卡

            // 读取原白名单
            std::ifstream read_white_list(m_dpdk_white_list);
            if(read_white_list.is_open()) {
                std::string line;
                while(std::getline(read_white_list, line)) {
                    // 跳过空行和注释行
                    if(line.empty() || line[0] == '#') continue;
                    // 去除行首行尾空白字符
                    line.erase(0, line.find_first_not_of(" \t\r\n"));
                    line.erase(line.find_last_not_of(" \t\r\n") + 1);
                    if(!line.empty()) dpdk_white_list.push_back(line);
                }
                read_white_list.close();
            }

            // 解析用户请求，分离出需要绑定和解绑的网卡
            for (auto it = doc.MemberBegin(); it != doc.MemberEnd(); it++) {
                std::string name = it->name.GetString();
                if (1 == it->value.GetInt()) {
                    if(std::find(dpdk_white_list.begin(), dpdk_white_list.end(), name) == dpdk_white_list.end()) {
                        dpdk_white_list.push_back(name);
                    }
                } else if (0 == it->value.GetInt()) {
                    auto it = std::find(dpdk_white_list.begin(), dpdk_white_list.end(), name);
                    if(it != dpdk_white_list.end()) {
                        dpdk_white_list.erase(it);
                        unbind_eths.push_back(name);
                    }
                }
            }

            // 写回白名单
            std::ofstream write_white_list(m_dpdk_white_list);
            if (!write_white_list.is_open()) {
                dto->success = false;
                dto->error_msg = "open dpdk white list file fail";
                return createDtoResponse(Status::CODE_200, dto);
            }
            for (const auto& name : dpdk_white_list) {
                write_white_list << name << std::endl;
            }
            write_white_list.close();

            // 执行绑定脚本，获取绑定失败的网卡名
            std::vector<std::string> fail_bind_eths;
            FILE* pipe = popen("bash /opt/apigw/gwhw/tools/bind_eth.sh dpdk_bind", "r");
            if (pipe) {
                char buffer[4096];
                std::string result = "";
                while (fgets(buffer, sizeof(buffer), pipe) != NULL) {
                    result += buffer;
                }
                pclose(pipe);
                // 解析脚本输出，所有绑定失败的网卡名在一行，用空格分隔
                std::istringstream iss(result);
                std::string eth_name;
                while (iss >> eth_name) {
                    if (!eth_name.empty()) fail_bind_eths.push_back(eth_name);
                }
            }

            // 如果有绑定失败的网卡名，从白名单中删除并写回
            if (!fail_bind_eths.empty()) {
                for(const auto& eth_name : fail_bind_eths) {
                    auto it = std::find(dpdk_white_list.begin(), dpdk_white_list.end(), eth_name);
                    if(it != dpdk_white_list.end()) {
                        dpdk_white_list.erase(it);
                    }
                }
                std::ofstream write_final_white_list(m_dpdk_white_list);
                if (!write_final_white_list.is_open()) {
                    dto->success = false;
                    dto->error_msg = "open dpdk white list file fail (final)";
                    return createDtoResponse(Status::CODE_200, dto);
                }
                for (const auto& name : dpdk_white_list) {
                    write_final_white_list << name << std::endl;
                }
                write_final_white_list.close();
            }

            // 将解绑的网卡添加到physics_eth中
            for(const auto& eth_name : unbind_eths)
            {
                if(std::find(physics_eth.begin(), physics_eth.end(), eth_name) == physics_eth.end())
                {
                    physics_eth.push_back(eth_name);
                }
            }

            // 绑定/解绑DPDK后更新网卡和PCI设备ID的映射关系
            refresh_eth_basic_info();
        }
        else if(std::find(mode_name.begin(),mode_name.end(), "nic") != std::end(mode_name) || std::find(mode_name.begin(),mode_name.end(), "pcap") != std::end(mode_name))
        {
            dto->error_msg = "open file=/opt/data/apigw/gwhw/gw_parser.conf fail";
            std::ifstream file(m_gw_parser_cfg);
            std::string file_json((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            rapidjson::Document gw_parser_conf_doc;
            gw_parser_conf_doc.Parse(file_json.c_str());
            rapidjson::Value& gw_parser_conf = gw_parser_conf_doc;
            file.close();

            dto->error_msg = "get eth from content fail";
            std::vector<std::string> nic_device_name;
            std::string all_device_name = gw_parser_conf["parser"]["nic_device_name"].GetString();
            while (all_device_name.find(",") != std::string::npos) {
                std::string eth(all_device_name.begin(), all_device_name.begin() + all_device_name.find(","));
                all_device_name = all_device_name.substr(all_device_name.find(",") + 1);
                nic_device_name.push_back(eth);
            }
            nic_device_name.push_back(all_device_name);
            for (auto it = doc.MemberBegin(); it != doc.MemberEnd(); it++) {
                if (1 == it->value.GetInt()) {
                    nic_device_name.push_back(it->name.GetString());
                } else if (0 == it->value.GetInt()) {
                    auto deleteitr = nic_device_name.begin();
                    if ((deleteitr = find(nic_device_name.begin(), nic_device_name.end(), it->name.GetString())) !=
                        nic_device_name.end()) {
                        nic_device_name.erase(deleteitr);
                    }
                }
            }
            //  去重
            set<std::string> setVec(nic_device_name.begin(), nic_device_name.end());
            nic_device_name.assign(setVec.begin(), setVec.end());
            all_device_name = "";
            for (auto it = nic_device_name.begin(); it != nic_device_name.end(); it++) {
                all_device_name = all_device_name + *it + ",";
            }
            all_device_name.erase(all_device_name.length() - 1);

            dto->error_msg = "change gw_parser.conf nic fail";
            FILE* pipe = popen("grep -n nic_device_name /opt/data/apigw/gwhw/gw_parser.conf | cut -d : -f 1", "r");
            std::string line_num = "";
            if (pipe) {
                char buffer[128];
                while (!feof(pipe)) {
                    if (fgets(buffer, 128, pipe) != NULL) {
                        strtok(buffer, "\n");
                        line_num = buffer;
                    }
                }
                pclose(pipe);
            }
            std::string new_bind_eth = "\\\"nic_device_name\\\":\\\"" + all_device_name + "\\\",";
            std::string cmd = "sed -i \"" + line_num + "c \\    " + new_bind_eth + "\" /opt/data/apigw/gwhw/gw_parser.conf";

            if (std::system(cmd.c_str()) != 0) {
                dto->success = false;
                dto->error_msg = "change gw_parser.conf nic fail";
                return createDtoResponse(Status::CODE_200, dto);
            }

            dto->error_msg = "apply eth change fail";
            std::thread(&Eth::system_cmd, this, "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser", 1).detach();
        }
    } catch (const std::exception& e) {
        dto->success = false;
        return createDtoResponse(Status::CODE_200, dto);
    }

    dto->error_msg = "success";
    return createDtoResponse(Status::CODE_200, dto);
}

void Eth::refresh_eth_basic_info(void)
{
    // 清空PCI映射关系
    eth_pci_map.clear();

    // 获取所有物理网卡的PCI映射关系
    for(auto eth_name : physics_eth)
    {
        // 跳过lo口，lo口没有PCI信息
        if(eth_name == "lo")
            continue;
            
        find_pci(eth_name);
    }

    // 获取当前DPDK绑定的网卡列表
    std::set<std::string> dpdk_bound_eths;
    std::ifstream white_list(m_dpdk_white_list);
    if(white_list.is_open())
    {
        std::string eth_name;
        while(std::getline(white_list, eth_name))
        {
            // 跳过注释行和空行
            if(eth_name.empty() || eth_name[0] == '#')
                continue;
            // 去除行首行尾空白字符
            eth_name.erase(0, eth_name.find_first_not_of(" \t\r\n"));
            eth_name.erase(eth_name.find_last_not_of(" \t\r\n") + 1);
            if(!eth_name.empty())
            {
                dpdk_bound_eths.insert(eth_name);
            }
        }
        white_list.close();
    }

    // 建立绑定网卡的PCI映射关系
    for(const auto& eth_name : dpdk_bound_eths)
    {
        // 如果该网卡已经在eth_pci_map中有映射关系,则跳过
        if(eth_pci_map.find(eth_name) != eth_pci_map.end())
            continue;

        find_pci(eth_name);
    }
}