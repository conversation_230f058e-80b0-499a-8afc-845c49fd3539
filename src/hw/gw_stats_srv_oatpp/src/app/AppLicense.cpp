# include "AppLicense.hpp"

License::~License(void)
{

}

std::string License::get_version_info()
{
    std::ifstream readFile;
    readFile.open(m_version_file_dir,ios::in);
    std::string version = "";
    if(readFile.is_open())
    {
        getline(readFile,version);
    }
    readFile.close();
    return version;
}

/*
int License::init_licutils()
{
    const char * license_file_path = "/opt/licutils/license.lic";
    int i_ret = 0;
    st_pro_info_t st_product_info;
    memset(&st_product_info, 0, sizeof(st_pro_info_t));
    strcpy(st_product_info.a_pro_version, "1.6.15");
    strcpy(st_product_info.a_pro_id, "2");
    strcpy(st_product_info.a_pro_serialid, "gw010502");
    i_ret = init_license(license_file_path, &st_product_info);
    if (i_ret != 0)
    {
        LOG_ERROR("init license file(%s) failed(%d)\n", license_file_path, i_ret);
    }
    return i_ret;
}
*/


std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::GetBasicInfo(void)
{
    auto dto = BasicInfo::createShared();
    std::string version = get_version_info();
    if(version != "")
    {
        dto->data->push_back({"productId",m_product_id});
        dto->data->push_back({"productName","网关"});
        dto->data->push_back({"productSerial",m_product_sn});
        dto->data->push_back({"oldVersion",""});
        dto->data->push_back({"oldDesc",""});
        dto->data->push_back({"version",version});
        dto->data->push_back({"desc","1.5.1"});
        dto->data->push_back({"time","0"});
    }
    else
    {
        dto->success = "false";
        dto->errorCode = -1;
    }
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::GetLicenseInfo(void)
{
    auto dto = BasicInfo::createShared();
    int i_ret = init_licutils();
    int state = 0; // 0 未授权; 1 授权，未过期; 2 过期
    int errcode = 0;
    char start_time[16] = {0};
    char end_time[16] = {0};
    char limit_rate[16] = {0};
    if(i_ret == 0)
    {
        try
        {
            i_ret = get_product_base_info(m_product_id.c_str(), "start_time", start_time, sizeof(start_time) - 1);
            if (i_ret != 0)
            {
                LOG_ERROR("get start_time info failed(%d)\n", i_ret);
            }
            i_ret = get_product_base_info(m_product_id.c_str(), "end_time", end_time, sizeof(end_time) - 1);
            if (i_ret != 0)
            {
                LOG_ERROR("get end_time info failed(%d)\n", i_ret);
            }

            if(check_expired(m_product_id.c_str(), nullptr))
            {
                state = 2;
            }
            else
            {
                state = 1;
            }

            i_ret = get_product_base_info(m_product_id.c_str(), "flow_rate", limit_rate, sizeof(limit_rate) - 1);
            if (i_ret == 0)
            {
                LOG_INFO("limit rate = %s\n", limit_rate);
            }
            else
            {
                LOG_ERROR("get limit rate info failed(%d)\n", i_ret);
            }
        }
        catch(const std::exception& e)
        {
            LOG_WARN("get license detail fail, %s", e.what());
        }
        release_lic_info();
    }
    else if(i_ret != -201)
    {
        errcode = i_ret;
    }

    dto->data->push_back({"productId",m_product_id});
    dto->data->push_back({"productName","网关"});
    dto->data->push_back({"product_serial",m_product_sn});
    dto->data->push_back({"version",get_version_info()});
    dto->data->push_back({"state",to_string(state)});
    dto->data->push_back({"start_time",start_time});
    dto->data->push_back({"end_time",end_time});
    dto->data->push_back({"errorCode",to_string(errcode)});
    dto->data->push_back({"flow_rate",limit_rate});

    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::GetMachineCode(void)
{
    auto dto = UploadMsg::createShared();
    char machine_code[1024] = {0};
    get_unique_code(machine_code,sizeof(machine_code));
    dto->data = machine_code;
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::UploadLicense(std::shared_ptr<IncomingRequest> request)
{
    auto dto = UploadMsg::createShared(); 
    oatpp::web::mime::multipart::PartList multipart(request->getHeaders());
    oatpp::web::mime::multipart::Reader multipartReader(&multipart);
    multipartReader.setDefaultPartReader(oatpp::web::mime::multipart::createTemporaryFilePartReader("/tmp"));
    request->transferBody(&multipartReader);
    auto part = multipart.getNamedPart("multipartFile");

    //std::rename(part->getPayload()->getLocation()->c_str(), (m_license_file_dir + m_license_file_name).c_str());
    std::string cmd = "mv " + part->getPayload()->getLocation() + " " + (m_license_file_dir + m_license_file_name).c_str();
    system(cmd.c_str());

    cmd = "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser";
    system(cmd.c_str());
    
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::UploadPem(std::shared_ptr<IncomingRequest> request)
{
    auto dto = UploadMsg::createShared(); 
    oatpp::web::mime::multipart::PartList multipart(request->getHeaders());
    oatpp::web::mime::multipart::Reader multipartReader(&multipart);
    multipartReader.setDefaultPartReader(oatpp::web::mime::multipart::createTemporaryFilePartReader("/tmp"));
    request->transferBody(&multipartReader);
    auto part = multipart.getNamedPart("multipartFile");

    // std::rename(part->getPayload()->getLocation()->c_str(), (m_pem_dir + part->getFilename()->c_str()).c_str());
    std::string cmd = "mv " + part->getPayload()->getLocation() + " " + m_pem_dir + part->getFilename();
    system(cmd.c_str());
    
    return createDtoResponse(Status::CODE_200, dto);
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> License::DeletePem(String filename)
{
    auto dto = UploadMsg::createShared(); 
    rapidjson::Document doc;
    doc.Parse(filename->c_str());
    rapidjson::Value& value = doc;

    ifstream file(m_pem_dir + value["name"].GetString());
    if(file.is_open())
    {
        if(system(("rm -f " + m_pem_dir + value["name"].GetString()).c_str()) != 0)
        {
            dto->success = "false";
            dto->errorCode = -4;
            dto->data = "delete fail!";
        }
    }
    else
    {
        dto->success = "false";
        dto->errorCode = -3;
        dto->data = "not exit!";
    }

    return createDtoResponse(Status::CODE_200, dto);
}