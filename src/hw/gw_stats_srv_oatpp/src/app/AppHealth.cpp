#include "AppHealth.hpp"

Health::~Health(void)
{

}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> Health::GetHealth(void)
{
    auto health = HealthInfo::createShared();
    std::string buf = stats->get_last_stat_info();
    if(buf == "")
    {
      health->msg = "监控信息获取失败";
      health->err = 50001;
      return createDtoResponse(Status::CODE_200, health);
    }
    rapidjson::Document doc;
    doc.Parse(buf.c_str());
    rapidjson::Value& val = doc;
    auto detail = HealthDetail::createShared();
    detail->info->push_back({"timestamp",val["time_val"].GetInt64()});
    float http_session_qps = val["parser_http_speed"].GetFloat();
    float upload_msg = val["up_succ_kafka_speed"].GetFloat();
    health->data = detail;

    auto ethinfo = EthInformation::createShared();
    eth->GetEthInformation(true);
    ethinfo = eth->GetEthInfo();

    if(ethinfo->bind->size() != 0 || ethinfo->source_mode->size() != 0 || ethinfo->unbind->size() != 0)
    {
        std::string msg = "网口{";
        bool all_no_flow_flag = true;
        for(auto it = ethinfo->bind->begin(); it != ethinfo->bind->end(); ++ it)
        {
            auto detail = EthInfoDetail::createShared();
            detail = *it;
            if(detail->flow == 0)
            {
                msg = msg + detail->eth_name + ",";
            }
            else
            {
                all_no_flow_flag = false;
            }
        }

        if(all_no_flow_flag)
        {
            health->msg = "所有网口都无流量";
            health->err = 50002;
            return createDtoResponse(Status::CODE_200, health);
        }

        if(msg != "网口{")
        {
            msg.erase(msg.size() - 1);
            msg += "}无流量进入";
            health->msg = msg;
            health->err = 50003;
            return createDtoResponse(Status::CODE_200, health);
        }
    }

    if(http_session_qps < 0.01)
    {
        health->msg = "无http流量";
        health->err = 50004;
    }
    if(upload_msg < 0.01)
    {
        health->msg = "无kafka流量";
        health->err = 50005;
    }

    return createDtoResponse(Status::CODE_200, health);
}