#include "Authorization.hpp"

int init_licutils()
{
    const char * license_file_path = "/opt/licutils/license.lic";
    int i_ret = 0;
    st_pro_info_t st_product_info;
    memset(&st_product_info, 0, sizeof(st_pro_info_t));
    strcpy(st_product_info.a_pro_version, "1");
    strcpy(st_product_info.a_pro_id, "2");
    strcpy(st_product_info.a_pro_serialid, "gw010502");
    if(access(license_file_path,0))
    {
        LOG_ERROR("license file(%s) not exits\n", license_file_path);
        return -1;
    }
    i_ret = init_license(license_file_path, &st_product_info);
    if (i_ret != 0)
    {
        LOG_ERROR("init license file(%s) failed(%d)\n", license_file_path, i_ret);
    }
    return i_ret;
}
