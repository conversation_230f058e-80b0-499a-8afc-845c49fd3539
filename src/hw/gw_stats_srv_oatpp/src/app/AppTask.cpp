#include "AppTask.hpp"

AppTask::~AppTask(void)
{

}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> AppTask::getTaskStatus()
{
    // 检测网关进程是否存在
    auto dto = TaskMsg::createShared();
    std::string command = "ps aux|grep -v grep|grep -q 'gw_parser'";
    int result = std::system(command.c_str());
    if (result == 0) {
        dto->success = "true";
        dto->errCode = 200;
        dto->msg = "start";
        return createDtoResponse(Status::CODE_200, dto);
    } else {
        dto->success = "true";
        dto->errCode = 200;
        dto->msg = "stop";
        return createDtoResponse(Status::CODE_200, dto);
    }
}

std::shared_ptr<oatpp::web::server::api::ApiController::OutgoingResponse> AppTask::setTaskStatus(String content)
{
    // 根据标志启动停止网关进程
    auto dto = TaskMsg::createShared();
    try {
        std::string taskFlag = "3";
        std::map<std::string, std::string> formdata = parseUrlEncodedData(content);
        if (formdata.find("taskFlag") != formdata.end()) {
            taskFlag = formdata["taskFlag"];
        }
        // 开启任务
        if (taskFlag == "1") {
            std::string command =
                "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser";
            std::system(command.c_str());
            dto->success = "true";
            dto->errCode = 200;
            dto->msg = "task start";
        } else if (taskFlag == "2")  // 关闭任务
        {
            std::string command =
                "/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser";
            std::system(command.c_str());
            dto->success = "true";
            dto->errCode = 200;
            dto->msg = "task stop";
        } else {
            dto->success = "false";
            dto->errCode = -1;
            dto->msg = "null";
        }
    } catch (const std::exception& e) {
        dto->success = "false";
        dto->errCode = -1;
        dto->msg = "null";
    }

    return createDtoResponse(Status::CODE_200, dto);
}
