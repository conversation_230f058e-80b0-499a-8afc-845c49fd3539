#ifndef GwStatService_hpp
#define GwStatService_hpp

#include <iostream>
#include <fstream>
#include <string>
#include <map>
#include <stdlib.h>
#include "dto/DTOs.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "rapidjson.h"
#include "document.h"

#include "AppStats.hpp"
#include "AppLicense.hpp"
#include "AppSettings.hpp"
#include "AppEth.hpp"
#include "AppHealth.hpp"
#include "AppTask.hpp"

// 新增：包含日志头文件以使用API上下文功能
#include "log/Log.hpp"

using namespace std;
using namespace rapidjson;
using namespace oatpp::web::server::handler;

#include OATPP_CODEGEN_BEGIN(ApiController) //<-- Begin Codegen

/**
 * GwStatService Api Controller.
 */
class GwStatService : public oatpp::web::server::api::ApiController {
public:
  /**
   * Constructor with object mapper.
   * @param objectMapper - default object mapper used to serialize/deserialize DTOs.
   */
    GwStatService(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper))
      : oatpp::web::server::api::ApiController(objectMapper)
    {
        stats = new Stats(objectMapper);
        license = new License(objectMapper);
        settings = new Settings(objectMapper);
        eth = new Eth(objectMapper);
        health = new Health(objectMapper, eth, stats);
        task = new AppTask(objectMapper);
        setDefaultAuthorizationHandler(std::make_shared<DigestAuthorizationHandler>("Authentication Required"));
    }

    virtual ~GwStatService(void);

public:
    // 获取基本监控信息
    ENDPOINT("GET", "/monitoring_info", getMonitoringInfo) {
        API_ACCESS_LOGGER("GET", "/monitoring_info");
        return stats->GwhwMonitoringInfo();
    }

    // 获取状态信息
    // 读取 /opt/stats/目录下文件
    ENDPOINT("GET", "/stats", getStats, QUERY(Int64,startTime)) {
        API_ACCESS_LOGGER("GET", "/stats");
        //OATPP_ASSERT_HTTP(authObject->compareResponse("GET"), Status::CODE_401, "Unauthorized Access");
        return stats->GetStats(startTime);
    }

    // 查看 source_flag，现 capture_mode
    ENDPOINT("GET", "/source_flag", getSourceFlag) {
        API_ACCESS_LOGGER("GET", "/source_flag");
        return stats->GetSourceFlag();
    }

    ENDPOINT("POST", "/source_flag", getSourceFlagPost) {
        API_ACCESS_LOGGER("POST", "/source_flag");
        return stats->GetSourceFlag();
    }

    // 获取最新一条状态信息
    ENDPOINT("GET", "/last_stats", getLastStats) {
        API_ACCESS_LOGGER("GET", "/last_stats");
        //OATPP_ASSERT_HTTP(authObject->compareResponse("GET"), Status::CODE_401, "Unauthorized Access");
        return stats->GetLastStats();
    }

    // 获取错误日志
    ENDPOINT("GET", "/hw-stats/get_errlog", getErrorLog) {
        API_ACCESS_LOGGER("GET", "/hw-stats/get_errlog");
        return stats->GetErrorLog();
    }

    // 获取加密流量信息
    ENDPOINT("GET", "/hw-stats/encrypted_traffic", getEncryptedTraffic) {
        API_ACCESS_LOGGER("GET", "/hw-stats/encrypted_traffic");
        return stats->GetEncryptedTraffic();
    }

    // 获取网关基本信息
    ENDPOINT("GET", "/hw-admin/local/getVersionConfig.do", getVersionConfig) {
        API_ACCESS_LOGGER("GET", "/hw-admin/local/getVersionConfig.do");
        return license->GetBasicInfo();
    }

    ENDPOINT("POST", "/hw-admin/local/getVersionConfig.do", getVersionConfigPost) {
        API_ACCESS_LOGGER("POST", "/hw-admin/local/getVersionConfig.do");
        return license->GetBasicInfo();
    }

    // 获取授权信息
    ENDPOINT("GET", "/hw-admin/license/licenseDetail.do", getLicenseDetail) {
        API_ACCESS_LOGGER("GET", "/hw-admin/license/licenseDetail.do");
        return license->GetLicenseInfo();
    }

    ENDPOINT("POST", "/hw-admin/license/licenseDetail.do", getLicenseDetailPost) {
        API_ACCESS_LOGGER("POST", "/hw-admin/license/licenseDetail.do");
        return license->GetLicenseInfo();
    }

    // 获取机器码
    ENDPOINT("GET", "/hw-admin/license/machineCode.do", getMachineCode) {
        API_ACCESS_LOGGER("GET", "/hw-admin/license/machineCode.do");
        return license->GetMachineCode();
    }

    ENDPOINT("POST", "/hw-admin/license/machineCode.do", getMachineCodePost) {
        API_ACCESS_LOGGER("POST", "/hw-admin/license/machineCode.do");
        return license->GetMachineCode();
    }

    // 上传授权文件
    ENDPOINT("POST", "/hw-admin/license/uploadLicense.do", getUploadLicense, REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        API_ACCESS_LOGGER("POST", "/hw-admin/license/uploadLicense.do");
        return license->UploadLicense(request);
    }

    // 导入证书
    ENDPOINT("POST", "/hw-license/upload_pem", uploadPem, REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        API_ACCESS_LOGGER("POST", "/hw-license/upload_pem");
        return license->UploadPem(request);
    }

    // 删除证书
    ENDPOINT("POST", "/hw-license/delete_pem", deletePem, BODY_STRING(String, filename)) {
        API_ACCESS_LOGGER("POST", "/hw-license/delete_pem");
        return license->DeletePem(filename);
    }

    // settings 有关设置的两个接口
    ENDPOINT("POST", "/domain_to_ip", getDomainToIp, BODY_STRING(String, content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/domain_to_ip");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->DomainToIp(content);
    }

    ENDPOINT("POST", "/drop_http_file_event", getDropHttpFileEvent, BODY_STRING(String,content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/drop_http_file_event");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->DropHttpFileEvent(content);
    }

    // 设置http过滤的url
    ENDPOINT("POST", "/hw-filter/interactive_http_url_filter", setHttpUrlFilter, BODY_STRING(String,content)) {
        API_ACCESS_LOGGER("POST", "/hw-filter/interactive_http_url_filter");
        return settings->SetHttpUrlFilter(content);
    }

    // 清空过滤条件
    ENDPOINT("GET", "/hw-filter/clean_interactive_http_url_filter", cleanHttpUrlFilter) {
        API_ACCESS_LOGGER("GET", "/hw-filter/clean_interactive_http_url_filter");
        return settings->CleanHttpUrlFilter();
    }

    // 旁路阻断
    ENDPOINT("POST", "/gw-hw/controlStrategy", controlStrategy, BODY_STRING(String,content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gw-hw/controlStrategy");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->SetControlStrategy(content);
    }

    // 启用网关
    ENDPOINT("POST", "/gwhw/start_gwhw", startGwhw, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/start_gwhw");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->StartGwhw();
    }

    // 停用网关
    ENDPOINT("POST", "/gwhw/stop_gwhw", stopGwhw, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/stop_gwhw");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->StopGwhw();
    }

    // 重启网关
    ENDPOINT("POST", "/gwhw/restart_gwhw", restartGwhw, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/restart_gwhw");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->RestartGwhw();
    }

    // 卸载网关
    ENDPOINT("POST", "/gwhw/uninstall_gwhw", uninstallGwhw, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/uninstall_gwhw");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->UninstallGwhw();
    }

    // 升级网关
    ENDPOINT("POST", "/gwhw/update_gwhw", updateGwhw, REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        API_ACCESS_LOGGER("POST", "/gwhw/update_gwhw");
        //OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->UpdateGwhw(request);
    }

    // 状态检查
    ENDPOINT("POST", "/gwhw/status_check", statusCheck, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/status_check");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->StatusCheck();
    }

    // 网口绑定
    ENDPOINT("POST", "/gwhw/bind_eth", bindEth, BODY_STRING(String,content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/gwhw/bind_eth");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return eth->BindEth(content);
    }

    // 获取网卡信息
    // curl -X GET --digest -c ttt -u qzgw:conf1711 127.0.0.1:9876/eth/eth_information
    ENDPOINT("GET", "/eth/eth_information", getEthInformation, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("GET", "/eth/eth_information");
        OATPP_ASSERT_HTTP(authObject->compareResponse("GET"), Status::CODE_401, "Unauthorized Access");
        return eth->GetEthInformation();
    }

    ENDPOINT("GET", "/health/mdr",getHealth, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("GET", "/health/mdr");
        OATPP_ASSERT_HTTP(authObject->compareResponse("GET"), Status::CODE_401, "Unauthorized Access");
        return health->GetHealth();
    }

    // 通知kafka sasl 参数
    ENDPOINT("POST", "/kafka_sasl_param", KafkaSaslParam, BODY_STRING(String, content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/kafka_sasl_param");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return settings->setKafkaSaslParam(content);
    }

    ENDPOINT("GET", "/task_status", getTaskStatus, AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("GET", "/task_status");
        OATPP_ASSERT_HTTP(authObject->compareResponse("GET"), Status::CODE_401, "Unauthorized Access");
        return task->getTaskStatus();
    }

    ENDPOINT("POST", "/task_start_stop", setTaskStatus, BODY_STRING(String, content), AUTHORIZATION(std::shared_ptr<DefaultDigestAuthorizationObject>, authObject)) {
        API_ACCESS_LOGGER("POST", "/task_start_stop");
        OATPP_ASSERT_HTTP(authObject->compareResponse("POST"), Status::CODE_401, "Unauthorized Access");
        return task->setTaskStatus(content);
    }

private:
    Stats *stats;
    License *license;
    Settings *settings;
    Eth *eth;
    Health *health;
    AppTask *task;

};

#include OATPP_CODEGEN_END(ApiController) //<-- End Codegen

#endif /* GwStatService_hpp */
