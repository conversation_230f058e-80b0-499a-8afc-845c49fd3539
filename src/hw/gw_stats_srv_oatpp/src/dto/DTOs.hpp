#ifndef DTOs_hpp
#define DTOs_hpp

#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/Types.hpp"

#include OATPP_CODEGEN_BEGIN(DTO)

/**
 *  Data Transfer Object. Object containing fields only.
 *  Used in API for serialization/deserialization and validation
 */
class MonitorMsg : public oatpp::DTO {
    DTO_INIT(MonitorMsg, DTO)

    DTO_FIELD(String, msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(Fields<String>, data) = {}; // Map<String,String>
};

class FlagMsg : public oatpp::DTO {
    DTO_INIT(FlagMsg, DTO)

    DTO_FIELD(String, msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(List<String>, data) = {}; // list<String>
};

class EthInfo : public oatpp::DTO {
    DTO_INIT(EthInfo, DTO)

    DTO_FIELD(String, device_name);
    DTO_FIELD(Int64, total_packets);
    DTO_FIELD(Int64, drop_packets);
    DTO_FIELD(Int64, eth_speed);
    DTO_FIELD(String, card_stat);
};

class ParserInfo : public oatpp::DTO {
    DTO_INIT(ParserInfo, DTO)

    DTO_FIELD(Int32, index);
    DTO_FIELD(Fields<Int64>, drop) = {};
    DTO_FIELD(Int64, total);
};

class ParserBytesInfo : public oatpp::DTO {
    DTO_INIT(ParserBytesInfo, DTO)

    DTO_FIELD(Int32, index);
    DTO_FIELD(Int64, total);
    DTO_FIELD(Fields<Int64>, detail) = {};
};

class StatsInfo : public oatpp::DTO {
    DTO_INIT(StatsInfo, DTO)

    DTO_FIELD(Int64, time_val);
    DTO_FIELD(Float64, recv_bytes_speed);
    DTO_FIELD(Int64, parser_http_cnt);
    DTO_FIELD(Float64, parser_http_speed);
    DTO_FIELD(Int64, parser_http_zip_num);
    DTO_FIELD(Float64, parser_http_zip_speed);
    DTO_FIELD(Int64, parser_succ_http_cnt);
    DTO_FIELD(Int64, up_kafka_cnt);
    DTO_FIELD(Int64, up_succ_kafka_cnt);
    DTO_FIELD(Float64, up_succ_kafka_speed);
    DTO_FIELD(Float64, up_succ_kafka_bytes_speed);
    DTO_FIELD(Float64, cpu_usage);
    DTO_FIELD(Float64, mem_usage);
    DTO_FIELD(Int16, source_flag);
    DTO_FIELD(List<Object<EthInfo>>, eth_info) = {};
    DTO_FIELD(Object<ParserInfo>, filter);
    DTO_FIELD(Object<ParserInfo>, ip_parser, "ip parser");
    DTO_FIELD(Object<ParserInfo>, tcp_parser, "tcp parser");
    DTO_FIELD(Object<ParserInfo>, tcp_stream, "tcp stream");
    DTO_FIELD(Object<ParserInfo>, http);
    DTO_FIELD(Object<ParserInfo>, kafka);
    DTO_FIELD(Object<ParserBytesInfo>, ftp_parser_bytes);
    DTO_FIELD(Object<ParserBytesInfo>, http_parser_bytes, "http parser bytes");
    DTO_FIELD(Object<ParserBytesInfo>, ip_bytes);
    DTO_FIELD(Object<ParserBytesInfo>, ssl_parser_bytes);
    DTO_FIELD(Fields<Int64>, tcp_single_stream) = {};
};

class StatsMsg : public oatpp::DTO {
    DTO_INIT(StatsMsg, DTO)

    DTO_FIELD(String, msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(List<Object<StatsInfo>>, data) = {}; // list<Object<StatsInfo>>
};

class StatMsg : public oatpp::DTO {
    DTO_INIT(StatMsg, DTO)

    DTO_FIELD(Object<StatsInfo>, data);
    DTO_FIELD(String, msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
};

class SslIps : public oatpp::DTO {
    DTO_INIT(SslIps, DTO)

    DTO_FIELD(String, suite);
    DTO_FIELD(String, version);
};

class SslInfo : public oatpp::DTO {
    DTO_INIT(SslInfo, DTO)

    DTO_FIELD(Fields<Int64>, proportion) = {};
    DTO_FIELD(Fields<Object<SslIps>>, ssl_ips) = {};
    DTO_FIELD(Int64, ssl_parser_bits);
    DTO_FIELD(Int64, timestamp);
};

class SslMsg : public oatpp::DTO {
    DTO_INIT(SslMsg, DTO)

    DTO_FIELD(Object<SslInfo>, data) = {};
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(String, msg) = "ok";
};

class ErrLog : public oatpp::DTO {
    DTO_INIT(ErrLog, DTO);

    DTO_FIELD(String, success) = "true";
    DTO_FIELD(Int32, errorCode) = 0;
    DTO_FIELD(List<String>, data) = {};
};

class BasicInfo : public oatpp::DTO {
    DTO_INIT(BasicInfo, DTO)

    DTO_FIELD(String, success) = "true";
    DTO_FIELD(Int32, errorCode) = 0;
    DTO_FIELD(Fields<String>, data) = {}; // Map<String,String>
};

class UploadMsg : public oatpp::DTO {
    DTO_INIT(UploadMsg, DTO)

    DTO_FIELD(String, success) = "true";
    DTO_FIELD(Int32, errorCode) = 0;
    DTO_FIELD(String, data) = "success";
};

class SettingMsg : public oatpp::DTO {
    DTO_INIT(SettingMsg, DTO)

    DTO_FIELD(String, error_msg) = "success";
    DTO_FIELD(Boolean, success) = true;
};

class SendMsg : public oatpp::DTO {
    DTO_INIT(SendMsg, DTO)

    DTO_FIELD(String, success) = "true";
    DTO_FIELD(Int32, errorCode) = 0;
    DTO_FIELD(String, msg) = "";
};

class TaskMsg : public oatpp::DTO {
    DTO_INIT(TaskMsg, DTO)

    DTO_FIELD(String, success) = "true";
    DTO_FIELD(Int32, errCode) = 0;
    DTO_FIELD(String, msg) = "";
};

class EthInfoDetail : public oatpp::DTO {
    DTO_INIT(EthInfoDetail, DTO)

    DTO_FIELD(String, eth_name);
    DTO_FIELD(String, eth_speed);
    DTO_FIELD(Int32, flow);
    DTO_FIELD(String, ip);
    DTO_FIELD(Int32, mirror);
    DTO_FIELD(Int32, mirrorable);
    DTO_FIELD(String, rx_speed);
    DTO_FIELD(String, speed_unit);
    DTO_FIELD(String, status);
    DTO_FIELD(String, tx_speed);
};

class EthInformation : public oatpp::DTO {
    DTO_INIT(EthInformation, DTO)

    DTO_FIELD(List<Object<EthInfoDetail>>, bind) = {};
    DTO_FIELD(List<String>, source_mode) = {};
    DTO_FIELD(List<Object<EthInfoDetail>>, unbind) = {};
};

class EthMsg : public oatpp::DTO {
    DTO_INIT(EthMsg, DTO)

    DTO_FIELD(String, msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(Object<EthInformation>, data);
};

class HealthDetail : public oatpp::DTO {
    DTO_INIT(HealthDetail, DTO)

    DTO_FIELD(Fields<Int64>, info) = {};
    DTO_FIELD(Fields<Int64>, action) = {};
};

class HealthInfo : public oatpp::DTO{
    DTO_INIT(HealthInfo,DTO)

    DTO_FIELD(String,msg) = "ok";
    DTO_FIELD(Int32, err) = 0;
    DTO_FIELD(Object<HealthDetail>, data);
};

#include OATPP_CODEGEN_END(DTO)

#endif /* DTOs_hpp */
