#ifndef AppComponent_hpp
#define AppComponent_hpp

#include <fstream>
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/monitor/ConnectionMonitor.hpp"
#include "oatpp/network/monitor/ConnectionMaxAgeChecker.hpp"
#include "oatpp/network/monitor/ConnectionInactivityChecker.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "rapidjson.h"
#include "document.h"

/**
 *  Class which creates and holds Application components and registers components in oatpp::base::Environment
 *  Order of components initialization is from top to bottom
 */
class AppComponent {
public:
  
  /**
   *  Create ConnectionProvider component which listens on the port
   */
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, serverConnectionProvider)([] {
    auto connectionProvider = oatpp::network::tcp::server::ConnectionProvider::createShared({"0.0.0.0", 9876, oatpp::network::Address::IP_4});
    auto monitor = std::make_shared<oatpp::network::monitor::ConnectionMonitor>(connectionProvider);

    int timeout = 10;
    ifstream ifs("gw_stats_srv.conf");
    if(ifs.is_open())
    {
        std::string content( (istreambuf_iterator<char>(ifs)),(istreambuf_iterator<char>()) );
        rapidjson::Document doc;
        doc.Parse(content.c_str());
        rapidjson::Value& value = doc;
        if(value.HasMember("connection_timeout"))
        {
            timeout = value["connection_timeout"].GetInt();
        }
        ifs.close();
    }

    /* close all connections that stay opened for more than 10 seconds */
    
    monitor->addMetricsChecker(
      std::make_shared<oatpp::network::monitor::ConnectionMaxAgeChecker>(
          std::chrono::seconds(timeout)
        )
    );
    
    /* close all connections that have had no successful reads and writes for longer than 5 seconds */
    /*
    monitor->addMetricsChecker(
      std::make_shared<oatpp::network::monitor::ConnectionInactivityChecker>(
          std::chrono::seconds(120),
          std::chrono::seconds(120)
        )
    );
    */

    return monitor;
  }());
  
  /**
   *  Create Router component
   */
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, httpRouter)([] {
    return oatpp::web::server::HttpRouter::createShared();
  }());
  
  /**
   *  Create ConnectionHandler component which uses Router component to route requests
   */
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ConnectionHandler>, serverConnectionHandler)([] {
    OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router); // get Router component
    return oatpp::web::server::HttpConnectionHandler::createShared(router);
  }());
  
  /**
   *  Create ObjectMapper component to serialize/deserialize DTOs in Contoller's API
   */
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::data::mapping::ObjectMapper>, apiObjectMapper)([] {
    /* create serializer and deserializer configurations */
    auto serializeConfig = oatpp::parser::json::mapping::Serializer::Config::createShared();
    auto deserializeConfig = oatpp::parser::json::mapping::Deserializer::Config::createShared();

    /* enable beautifier */
    serializeConfig->useBeautifier = true;

    return oatpp::parser::json::mapping::ObjectMapper::createShared(serializeConfig, deserializeConfig);
  }());

};

#endif /* AppComponent_hpp */
