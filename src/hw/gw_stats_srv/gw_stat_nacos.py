#!/usr/bin/python
#coding:utf-8

import logging
import nacos
import time
import datetime
import json
import os

from app.app_function import send_command
from app.app_function import history_pcap_dir_syn,Singleton
from app.app_license import license

class stat_nacos:
    __metaclass__ = Singleton

    def __init__(self): 
        # nacos conf
        self.nacos_enable=False
        self.nacos_server_addrs="127.0.0.1:8448"
        self.nacos_namespace="public"
        self.nacos_dataid="handler.pcapdir.json"
        self.nacos_group="handler"
        self.nacos_server_task_addrs=""
        self.nacos_dataid_task=""
        self.nacos_group_task=""
        self.gwhw_started=False
        self.start_risk = "start_risk"
        self.stop_risk = "stop_risk"
        self.hispcap = history_pcap_dir_syn()
        self.license_obj = license()
                
        if os.path.exists("gw_stat_srv.conf"):
            try:
                conf_content = open("gw_stat_srv.conf").read()
                js_cf = json.loads(conf_content)
                if "nacos_conf" in js_cf:
                    js_nacos_conf = js_cf["nacos_conf"]
                    if "enable" in js_nacos_conf:
                        self.nacos_enable = js_nacos_conf["enable"]
                    if "server_addrs" in js_nacos_conf and js_nacos_conf["server_addrs"] != '':
                        self.nacos_server_addrs = js_nacos_conf["server_addrs"]
                    if "dataid" in js_nacos_conf:
                        self.nacos_dataid = js_nacos_conf["dataid"]
                    if "group" in js_nacos_conf:
                        self.nacos_group = js_nacos_conf["group"]
                    if "dataid_task" in js_nacos_conf:
                        self.nacos_dataid_task = js_nacos_conf["dataid_task"]
                    if "group_task" in js_nacos_conf:
                        self.nacos_group_task = js_nacos_conf["group_task"]
                    if "namespace" in js_nacos_conf:
                        self.nacos_namespace = js_nacos_conf["namespace"]
            except Exception as e:
                logging.error(e)
                
    def start(self):
        logging.info("[nacos_client_start] %s",(self.nacos_enable))
        
        if self.nacos_enable:
  
            nacos_client = nacos.NacosClient(self.nacos_server_addrs)#, namespace=self.nacos_namespace)
            
            if self.nacos_dataid != "" and self.nacos_group != "":
                nacos_client.add_config_watcher(self.nacos_dataid, self.nacos_group, self.cb_nacos_conf)
                
            if self.nacos_dataid_task != "" and self.nacos_group_task != "":
                logging.info("%s,%s" % (self.nacos_dataid_task,self.nacos_group_task))
                v = nacos_client.get_config(self.nacos_dataid_task,self.nacos_group_task)
                
                if None == v or self.license_obj.license_check() == False:#没搞懂
                    self.gwhw_started = True
                
                nacos_client.add_config_watcher(self.nacos_dataid_task,self.nacos_group_task, self.cb_nacos_task_conf)
                start_time = datetime.datetime.now()
                
                while self.gwhw_started == False:
                    time.sleep(1)
                    logging.info("wait gwhw start")
                    if (datetime.datetime.now() - start_time).total_seconds() > 120:
                        break
                    
    def cb_nacos_task_conf(self, conf_js):
        logging.info(conf_js)
        if False == self.gwhw_started:
            try:
                conf_s = conf_js["content"]
                if conf_s == None or conf_s == "":
                    self.gwhw_started = True
                    return
                js_cf = json.loads(conf_s)
                self.sync_task(js_cf)
                self.gwhw_started = True
            except Exception as e:
                logging.error(e)
                    
    def sync_task(self, conf_dic):
        try:
            # logging.debug("nacos task conf: (%s)" % conf_v)
            # conf_dic = json.loads(conf_v)
            logging.debug(conf_dic)
            taskid = conf_dic['taskId']
            taskFlag = conf_dic['taskFlag']
            targetType = conf_dic['targetType']
            saveHistoryFlag = conf_dic['saveHistoryFlag']
        except Exception as e:
            logging.error(e)
            taskFlag = 0
        logging.info("task conf: (taskid: %s, taskFlag: %d, targetType: %d, saveHistoryFlag: %d)" % (taskid, taskFlag, targetType, saveHistoryFlag))
        if taskFlag == 0:
            return
        logging.info(self.start_task(taskFlag, taskid, targetType, saveHistoryFlag))
        
       
    def start_task(self, taskFlag, taskid, target_type, save_history_flag):
        save_history_dir = ""
        parser_dir = ""

        # if not taskid or len(taskid) == 0:
        #     return jsonify(success="false", errCode=-102, msg="taskId not exist")

        # if not target_type or len(target_type) == 0:
        #     return jsonify(success="false", errCode=-103, msg="targetType not exist")
        # if int(target_type) != 1 and int(target_type) != 2 and int(target_type) != 3:
        #     return jsonify(success="false", errCode=-104, msg="invaild targetType param")

        # if not save_history_flag or len(save_history_flag) == 0:
        #     return jsonify(success="false", errCode=-105, msg="saveHistoryFlag not exist")

        if int(save_history_flag) == 1:
            if int(target_type) == 1 or int(target_type) == 2 or int(target_type) == 4:  # 流量镜像和agent模式才需要转储流量
                save_history_dir = self.hispcap.get() + '/' + taskid
                print(save_history_dir)
                if not os.path.exists(save_history_dir):
                    os.makedirs(save_history_dir)

                command = "%s|%d|%d|%s" % (self.start_risk, target_type, save_history_flag, save_history_dir)
            else:
                save_history_flag = "0"
                command = "%s|%d%d" % (self.start_risk, target_type, save_history_flag)
        else:
            command = "%s|%d|%d" % (self.start_risk, target_type, save_history_flag)

        if taskFlag == 2:
            command = self.stop_risk

        return send_command(command)
    
    # nacos 配置更新回调函数
    def cb_nacos_conf(self, conf_js):
        conf_s = conf_js["content"]
        js_cf = json.loads(conf_s)
        self.hispcap.set(js_cf["history_pcap_dir"])
        