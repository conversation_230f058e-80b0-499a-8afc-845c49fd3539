#!/usr/bin/python
#coding:utf-8

import subprocess
import os
import json
import time
import logging

import psutil as pt

from flask import jsonify,request
from app.app_function import <PERSON><PERSON>,get_req_param

class BackwardsReader():
    """Read a file line by line, backwards"""
    BLKSIZE = 4096

    def __init__(self, file):
        self.file = file
        self.buf = ""
        self.file.seek(0, 2)
        self.trailing_newline = 0
        lastchar = self.file.read(1)
        if lastchar == "\n":
            self.trailing_newline = 1
            self.file.seek(0, 2)

    def readline(self):
        while 1:
            newline_pos = str.rfind(self.buf, "\n")
            pos = self.file.tell()
            if newline_pos != -1:
                # Found a newline
                line = self.buf[newline_pos+1:]
                self.buf = self.buf[:newline_pos]
                if pos != 0 or newline_pos != 0 or self.trailing_newline:
                    line += "\n"
                return line
            else:
                if pos == 0:
                    # Start-of-file
                    return ""
                else:
                    # Need to fill buffer
                    toread = min(self.BLKSIZE, pos)
                    self.file.seek(-toread, 1)
                    self.buf = str(self.file.read(toread).decode('UTF-8') + self.buf)
                    self.file.seek(-toread, 1)
                    if pos - toread == 0:
                        self.buf = "\n" + self.buf


class stats:
    __metaclass__ = Singleton
    
    def __init__(self): 
        self.gwhw_log_path = "/opt/apigw/gwhw/logs/hw.log"
        self.gwhw_err_path = "/opt/apigw/gwhw/logs/hw.err"
        # 状态文件目录
        self.stats_file_dir = "/opt/stats/"
        # 状态文件
        self.stats_file = "/opt/stats/stats.file"
        # 加密信息文件
        self.ssl_ips_file = "/opt/stats/ssl_ips.file"
        # 统计字段文件
        self.proportion_file = "/opt/stats/proportion.file"
        self.proportion_total_file = "/opt/stats/proportion_total.file"
        # 状态时间戳字段
        self.stats_timeval_key = "time_val"
        # gw_parser配置文件路径
        # gw_parser_cfg = "/opt/apigw/gwhw/gw_parser.conf"
        self.gw_parser_cfg = "/opt/data/apigw/gwhw/gw_parser.conf"
        # 返回stats信息的条数限制
        self.stats_limit = 5000
        
        if os.path.isfile(self.gw_parser_cfg):
            file = open(self.gw_parser_cfg, "rb")
            fileJson = json.load(file)
            stats_path = fileJson["parser"]["stats_dir"]
            stats_name = fileJson["parser"]["stats_file"]
            self.stats_file_dir = stats_path
            self.stats_file = self.stats_file_dir+stats_name

        if os.path.exists("gw_stat_srv.conf"):
            try:
                conf_content = open("gw_stat_srv.conf").read()
                js_cf = json.loads(conf_content)
                if js_cf["stats_limit"]:
                    self.stats_limit = js_cf["stats_limit"]
            except Exception as e:
                logging.error(e)

    
    #refresh_time 刷新时间
    #IP 
    #GWHW，gwhw status 0 进程不存在 1 进程正常 -1 进程异常
    #通过进程名"gw_parser"获取占用CPU 内存 
    #总的流量
    ##json变量初始化
    #monitoring_info = { }
    
    def gwhw_monitoring_info(self):
    #刷新 刷新时间
        monitoring_info = {}
        monitoring_info["cpu"] = None
        monitoring_info["memory"] = None
        monitoring_info["flow"] = None
        monitoring_info["time"] = round( time.time() , 0)
        #stat更新 
        #monitoring_info["aaaa"]=1
        pids = pt.process_iter()

        for pid in pids :
            try:
                if pid.name() == "gw_parser":
                    #cpu更新
                    pid_cpu_percent = pid.cpu_percent(interval=0.1)
                    if pid_cpu_percent > 0:
                        #monitoring_info["cpu_num"] = cpu_count() 
                        monitoring_info["cpu"] = pid_cpu_percent /pt.cpu_count()
                        monitoring_info["cpu"] = float( '%.1f' % monitoring_info["cpu"])
                    #usage_men更新 
                    if pid.memory_percent() > 0:
                        monitoring_info["memory"] = float ('%.1f' % pid.memory_percent() )
                    #flow 更新
                    while True:
                        flow_from_cmd  = os.popen( "cat " + self.gwhw_log_path + " | grep -w 'stats qps' -A15| grep  'ip bytes:' |tail -n 1 |awk '{ print $(NF -1)} '" )
                        monitoring_info["flow"] = float( flow_from_cmd.read()[:-2] ) 
                        #monitoring_info["flow"] = float( "0.10/n"[:-2] ) 
                        if  0 == os.system( "cat " + self.gwhw_log_path + " | grep -w 'stats qps' -A15| grep  'ip bytes:' |tail -n 1 |awk '{ print $(NF -1)} '" ): 
                            break
                    break
            except:
                pass

        #monitoring_info["gwhw_info"] = self.gwhw_info_refresh()
        return jsonify( msg="ok" , err = 0 , data = monitoring_info )

    def get_pid_by_name(self):
        pids = pt.process_iter()
        # parser_pid = 0
        parser_pid = 0
        parser_create_time = 0.0
        for pid in pids:
            try:
                if pid.name() == "gw_parser":
                    parser_pid = pid.pid
                    parser_create_time = pid.create_time()
                    break
            except:
                pass
        return parser_pid, parser_create_time

    # 解析状态文件
    def parser_stat_file(self,starttime):
        file_data = []
        # 获取/opt/stats目录下所有的文件
        file_list=os.listdir(self.stats_file_dir)   
        # 按照文件的ctime进行排序
        file_list.sort(key=lambda fn:os.path.getctime(self.stats_file_dir + fn))

        if starttime ==0:
            for file_list_param in file_list:
                filename=self.stats_file_dir + file_list_param
                for line in open(filename, "rU"):
                    try:
                        data_dict=json.loads(line)
                        file_data.append(data_dict)
                        if len(file_data) > self.stats_limit:
                            return file_data
                    except ValueError as e:
                        logging.error(e)
                        break
        else:
            for file_list_param in file_list:
                filename=self.stats_file_dir+file_list_param
                for line in open(filename, "rU"):
                    try:  
                        data_dict=json.loads(line)
                    except ValueError as e:
                        logging.error(e)
                        break

                    time_val = int(data_dict[self.stats_timeval_key])
                    if time_val >= starttime:
                        file_data.append(data_dict)
                        if len(file_data) > self.stats_limit:
                            return file_data
        return file_data


    def get_last_stat_info(self):
        if os.path.isfile(self.stats_file):
            with open(self.stats_file, 'rU') as f:
                lines = f.readlines()
                line_count=len(lines)
                if line_count > 1:
                    last_line = lines[-2] #取倒数第二行，最后一行的数据可能不完整
                    data_dict = json.loads(last_line)
                    return data_dict
                else:
                    return None
        else:
            return None
    
    
    # 获取状态信息
    # 读取 /opt/stats/目录下文件
    # POST param: startTime
    def api_get_stats(self):
        stat_data=None
        starttime = get_req_param(request, "startTime")
        logging.info(starttime)
        if not starttime or len(starttime) == 0:
            return jsonify(msg="not starttime param", err=1, data=None)
        
        stat_data=self.parser_stat_file(int(starttime))
    
        return jsonify(msg="ok", err=0, data=stat_data)


    # 查看 source_flag，现 capture_mode
    def api_source_flag(self):
        data = []
        last_stat_info = None
        last_stat_info = self.get_last_stat_info()
        if not last_stat_info or not last_stat_info["source_flag"]:
            return jsonify(msg="err", err=-1, data=None)
        source_flag = last_stat_info["source_flag"]
        if (source_flag & 1) != 0:
            data.append('DPDK')
        if (source_flag & 2) != 0:
            data.append('AGENT')
        if (source_flag & 4) != 0:
            data.append('NIC')
        return jsonify(msg="ok", err=0, data=data)


    # 获取最新一条状态信息
    # 读取 /opt/stats/stats.file
    def api_get_last_stats(self):
        last_stat_info = None
        last_stat_info = self.get_last_stat_info()
        return jsonify(msg="ok", err=0, data=last_stat_info)


    # 原r_refresh
    # 更新并获取信息
    def gwhw_info_refresh(self):
        gwhw_info_refresh = {
                    'process_id':None,
                    'process_createtime':None,
                    'load_dpdk':None,
                    'netcard_mask':None,
                    'dpdk_work_thread':None,
                    'mbuf_size':None,
                    'mount_size':None,
                    'load_file':None,
                    'file_work_thread':None,
                    'pcap_dir':None,
                    'tcp_streams':None,
                    'session_num':None,
                    'load_http':None,
                    'load_ftp':None,
                    'load_mail':None,
                    'load_ssl':None,
                    'upload_file':None,
                    'new_event_format':None,
                    'load_kafka':None,
                    'http_ruled':None,
                    'http_unknown_ruled':None,
                    'ftp_host':None,
                    'mail_host':None,
                    'minio':None,
                    'nacos_endpoint':None,
                    'nacos_dataid':None,
                    'nacos_group':None,
                    'total_packets':None,
                    'mbuf_miss_packets':None,
                    'pcap_file_num':None,
                    'pcap_succ_file_num':None,
                    'black_ip':None,
                    'license_expire':None,
                    'global_limit':None,
                    'not_ip':None,
                    'recv_speed':None,
                    'not_tcp':None,
                    'port_hit':None,
                    'cache_overfull':None,
                    'session_overfull':None,
                    'parser_http_cnt':None,
                    'parser_succ_http_cnt':None,
                    'parser_http_bytes':None,
                    'upload_kafka_cnt':None,
                    'upload_succ_kafka_cnt':None,
                    'eth_info':None,
                    'upload_msg': None
        }    
        pid, create_time = self.get_pid_by_name()
        gwhw_info_refresh["process_id"] = pid
        gwhw_info_refresh["process_createtime"] = create_time
        
        file = open(self.gw_parser_cfg, "rb")
        fileJson = json.load(file)
        source_path = fileJson["plugin"]["source_path"]
        parser_path = fileJson["plugin"]["parser_path"]
        upload_path = fileJson["plugin"]["upload_path"]

        is_load_dpdk = 0
        is_load_file = 0
        is_load_http = 0
        is_load_ftp = 0
        is_load_mail = 0
        is_load_ssl = 0
        is_load_kafka = 0
        http_ruled_hosts = ""
        http_unknown_ruled_hosts = ""
        ftp_hosts = ""
        mail_hosts = ""

        dpdk_path = 0
        file_path = 0
        kafka_path = 0
        http_path = 0
        ftp_path = 0
        mail_path = 0
        ssl_path = 0
        
        for source in source_path.split(':'):
            if source == "source/dpdk_source/" or source == "source/dpdk_source":
                dpdk_path = 1
            elif source == "source/file_source/" or source == "source/file_source":
                file_path = 1
        
        for parser in parser_path.split(':') :
            if parser == "parser/http_parser/" or parser == "parser/http_parser":
                http_path = 1
            elif parser == "parser/ftp_parser/" or parser == "parser/ftp_parser":
                ftp_path = 1
            elif parser == "parser/mail_parser/" or parser == "parser/mail_parser":
                mail_path = 1
            elif parser == "parser/ssl_parser/" or parser == "parser/ssl_parser":
                ssl_path = 1
        
        for upload in upload_path.split(':'):
            if upload == "upload/kafka_upload/" or upload == "upload/kafka_upload":
                kafka_path = 1
        
        load_files = fileJson["plugin"]["load_files"]
        for load in load_files.split(':'):
            if load == "dpdk_source.so" and dpdk_path == 1:
                is_load_dpdk = 1
            elif load == "file_source.so" and file_path == 1:
                is_load_file = 1
            elif load == "http_parser.so" and http_path == 1:
                is_load_http = 10
            elif load == "ftp_parser.so" and ftp_path == 1:
                is_load_ftp = 1
            elif load == "mail_parser.so" and mail_path == 1:
                is_load_mail = 1
            elif load == "ssl_parser.so" and ssl_path == 1:
                is_load_ssl = 1
            elif load == "kafka_upload.so" and kafka_path == 1:
                is_load_kafka = 1
                
        gwhw_info_refresh["load_dpdk"] = is_load_dpdk
        
        dpdk_args = fileJson["cap"]["args"]
        for dpdk_arg in dpdk_args:
            if not dpdk_arg:
                continue
            if "0x" in dpdk_arg:
                gwhw_info_refresh["netcard_mask"] = int(dpdk_arg, 16)
            if "--work-thread" in dpdk_arg:
                gwhw_info_refresh["dpdk_work_thread"] = dpdk_arg.split("=")[1]
            if "--mbuf-per-pool" in dpdk_arg:
                gwhw_info_refresh["mbuf_size"] = dpdk_arg.split("=")[1]
        try:
            # mount_size.set(len(os.listdir("/mnt/huge")))
            outs = ""
            errs = ""

            proc = subprocess.Popen("cat /opt/apigw/gwhw/tools/dpdk_alloc_GB",
                                    shell=True,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE)
            outs, errs = proc.communicate()
            if len(outs) > 1:
                hugepage = outs[0:len(outs) - 1]
                gwhw_info_refresh["mount_size"] = int(hugepage)
            else:
                gwhw_info_refresh["mount_size"] = 0
        except:
            gwhw_info_refresh["mount_size"] = 1
        
        gwhw_info_refresh["load_file"] = is_load_file
        gwhw_info_refresh["file_work_thread"] = fileJson["parser"]["pcap_tcp_work_thread"]
        gwhw_info_refresh["pcap_dir"] = {fileJson["parser"]["pcap_dir"]:1}
        gwhw_info_refresh["tcp_streams"] = fileJson["parser"]["tcp_streams"]
        gwhw_info_refresh["session_num"] = fileJson["parser"]["session_max_num"]
        gwhw_info_refresh["load_http"] = is_load_http
        gwhw_info_refresh["load_ftp"] = is_load_ftp
        gwhw_info_refresh["load_mail"] = is_load_mail
        gwhw_info_refresh["load_ssl"] = is_load_ssl
        
        try:
            gwhw_info_refresh["upload_file"] = fileJson["parser"]["upload_file"]
        except:
            gwhw_info_refresh["upload_file"] = 0
        
        try:
            gwhw_info_refresh["new_event_format"] = fileJson["parser"]["use_new_event_format"]
        except:
            gwhw_info_refresh["new_event_format"] = 0
        
        gwhw_info_refresh["load_kafka"] = is_load_kafka
        
        try:
            http_ruled_list = fileJson["parser"]["kafka"]["brokers_list"]["http_ruled"]
        except:
            http_ruled_list = []
        
        try:
            http_unknown_ruled_list = fileJson["parser"]["kafka"]["brokers_list"]["http_unknown_rule"]
        except:
            http_unknown_ruled_list = []

        try:
            ftp_list = fileJson["parser"]["kafka"]["brokers_list"]["ftp"]
        except:
            ftp_list = []
        
        try:
            mail_list = fileJson["parser"]["kafka"]["brokers_list"]["mail"]
        except:
            mail_list = []
            
        for http_ruled_host in http_ruled_list:
            host = http_ruled_host["host"]
            if http_ruled_hosts == "":
                http_ruled_hosts = http_ruled_hosts + host
            else:
                http_ruled_hosts = http_ruled_hosts + "," + host
        
        if http_ruled_hosts == "":
            gwhw_info_refresh["http_ruled"] = {"":0}
        else:
            gwhw_info_refresh["http_ruled"] = {http_ruled_hosts:1}
            
        for http_unknown_ruled_host in http_unknown_ruled_list:
            host = http_unknown_ruled_host["host"]
            if http_unknown_ruled_hosts == "":
                http_unknown_ruled_hosts = http_unknown_ruled_hosts + host
            else:
                http_unknown_ruled_hosts = http_unknown_ruled_hosts + "," + host
                
        if http_unknown_ruled_hosts == "":
            gwhw_info_refresh["http_unknown_ruled"] = {"":0}
        else:
            gwhw_info_refresh["http_unknown_ruled"] = {http_unknown_ruled_hosts:1}
        
        for ftp_host_info in ftp_list:
            host = ftp_host_info["host"]
            if ftp_hosts == "":
                ftp_hosts = ftp_hosts + host
            else:
                ftp_hosts = ftp_hosts + "," + host
        
        if ftp_hosts == "":
            gwhw_info_refresh["ftp_host"] = {"":0}
        else:
            gwhw_info_refresh["ftp_host"] = {ftp_hosts:1}
            
        for mail_host_info in mail_list:
            host = mail_host_info["host"]
            if mail_hosts == "":
                mail_hosts = mail_hosts + host
            else:
                mail_hosts = mail_hosts + "," + host
        
        if mail_hosts == "":
            gwhw_info_refresh["mail_host"] = {"":0}
        else:
            gwhw_info_refresh["mail_host"] = {mail_hosts:1}
        
        try:
            gwhw_info_refresh["minio"] = {fileJson["minio"]["endpoint"]:1}
        except:
            gwhw_info_refresh["minio"] = {"":0}
        
        try:
            nacos_list = fileJson["nacos"]["nacos_list"]
        except:
            nacos_list = []

        nacos_point = ""
        nacos_group_info = ""
        nacos_dataid_info = ""
        for nacos_info in nacos_list:
            try:
                nacos_point = nacos_info["nacos_server_addr"]
            except:
                nacos_point = ""

            try:
                nacos_group_dataids = nacos_info["nacos_dataid_group"]
            except:
                nacos_group_dataids = []
            
            nacos_group_info = ""
            nacos_dataid_info = ""

            for nacos_group_dataid in nacos_group_dataids:
                if nacos_group_info == "":
                    nacos_group_info = nacos_group_dataid["group"]
                else:
                    nacos_group_info = nacos_group_info + "," + nacos_group_dataid["group"]

                if nacos_dataid_info == "":
                    nacos_dataid_info = nacos_group_dataid["data_id"]
                else:
                    nacos_dataid_info = nacos_dataid_info + "," + nacos_group_dataid["data_id"]
        
        if nacos_point == "":
            gwhw_info_refresh["nacos_endpoint"] = {"":0}
        else:
            gwhw_info_refresh["nacos_endpoint"] = {nacos_point:1}
            
        if nacos_group_info == "":
            gwhw_info_refresh["nacos_group"] = {"":0}
        else:
            gwhw_info_refresh["nacos_group"] = {nacos_group_info:1}

        if nacos_dataid_info == "":
            gwhw_info_refresh["nacos_dataid"] = {"":0}
        else:
            gwhw_info_refresh["nacos_dataid"] = {nacos_dataid_info:1}
        
        last_stat_info = None
        last_stat_info = self.get_last_stat_info()
        if not last_stat_info:
            return gwhw_info_refresh
        
        source_flag = last_stat_info["source_flag"]
        if (source_flag & 1) != 0 :
            packets_num = 0
            miss_packets = 0
            index = 0
            for eth in last_stat_info["eth_info"]:
                packets_num = packets_num + eth["total_packets"] 
                miss_packets = miss_packets + eth["drop_packets"]
                gwhw_info_refresh["eth_info"] = {
                    index:{
                        "speed":eth["eth_speed"],
                        "total_packets":eth["total_packets"],
                        "err_packets":eth["err_packets"],
                        "err_bytes":eth["err_bytes"],
                        "drop_packets":eth["drop_packets"],
                        "drop_bytes":eth["drop_bytes"],
                        "total_bytes":eth["total_bytes"]
                    }
                }
                if "UP" == eth["card_stat"]:
                    gwhw_info_refresh["eth_info"] = {
                        index:{
                            "card_stat":1
                        }
                    }
                else:
                    gwhw_info_refresh["eth_info"] = {
                        index:{
                            "card_stat":0
                        }
                    }
                index = index + 1
            
            
            gwhw_info_refresh["total_packets"] = packets_num
            gwhw_info_refresh["mbuf_miss_packets"] = miss_packets
        else:
            gwhw_info_refresh["total_packets"] = 0
            gwhw_info_refresh["mbuf_miss_packets"] = 0
        
        if (source_flag & 2) != 0:
            parser_file_num = last_stat_info["pcap_probe_info"]["pcap_file_num"]
            parser_succ_file_num = last_stat_info["pcap_probe_info"]["pcap_succ_file_num"]
            gwhw_info_refresh["pcap_file_num"] = parser_file_num
            gwhw_info_refresh["pcap_succ_file_num"] = parser_succ_file_num
        else:
            gwhw_info_refresh["pcap_file_num"] = 0
            gwhw_info_refresh["pcap_succ_file_num"] = 0
        
        gwhw_info_refresh["recv_speed"] = last_stat_info["recv_bytes_speed"]
        gwhw_info_refresh["black_ip"] = last_stat_info["filter"]["drop"]["black ip"]
        gwhw_info_refresh["license_expire"] = last_stat_info["filter"]["drop"]["license expire"]
        gwhw_info_refresh["global_limit"] = last_stat_info["filter"]["drop"]["global limit"]
        gwhw_info_refresh["not_ip"] = last_stat_info["filter"]["drop"]["not ip"]
        gwhw_info_refresh["not_tcp"] = last_stat_info["ip parser"]["drop"]["not tcp"]
        gwhw_info_refresh["port_hit"] = last_stat_info["tcp parser"]["drop"]["port hit"]
        gwhw_info_refresh["cache_overfull"] = last_stat_info["tcp parser"]["drop"]["cache overfull"]
        gwhw_info_refresh["session_overfull"] = last_stat_info["tcp parser"]["drop"]["session overfull"]
        gwhw_info_refresh["parser_http_cnt"] = last_stat_info["parser_http_cnt"]
        gwhw_info_refresh["parser_succ_http_cnt"] = last_stat_info["parser_succ_http_cnt"]
        gwhw_info_refresh["parser_http_bytes"] = {
            "total":last_stat_info['http parser bytes']['total'],
            "succ":last_stat_info['http parser bytes']['detail']['suc bytes']
        }
        
        if last_stat_info['http parser bytes']['total'] == None or last_stat_info['http parser bytes']['detail']['suc bytes'] ==  None or last_stat_info['http parser bytes']['total'] < last_stat_info['http parser bytes']['detail']['suc bytes']:
            gwhw_info_refresh["parser_http_bytes"] = {"failt":0}
        else:
            gwhw_info_refresh["parser_http_bytes"] = {"failt":last_stat_info['http parser bytes']['total'] - last_stat_info['http parser bytes']['detail']['suc bytes']}
            
        gwhw_info_refresh["upload_kafka_cnt"] = last_stat_info["up_kafka_cnt"]
        gwhw_info_refresh["upload_succ_kafka_cnt"] = last_stat_info["up_succ_kafka_cnt"]
        gwhw_info_refresh["upload_msg"] = last_stat_info["up_succ_kafka_speed"]

        return gwhw_info_refresh
    
    def api_get_errlog(self):
        log = []
        log_size = 0
        br = BackwardsReader(open(self.gwhw_err_path,"rb"))
        while(1):
            # 错误日志留存最大10MB
            raw = br.readline()
            if((log_size + len(raw) > 10000000) | (raw == "")):
                break
            log_size += len(raw)
            if raw == "\n":
                continue
            if ("[ERROR]" in raw):
                log.append(raw)
        log.reverse()
        return jsonify(success="true", errorCode=0, data=log)
    
    def api_get_encrypted_traffic(self):
        data = {}
        data["timestamp"] = int(time.time() * 1000)
        data["proportion"] = {}
        data["proportion"]["total_cnt"] = "0"
        data["ssl_parser_bits"] = "0"
        data["proportion"]["ssl_cnt"] = "0"
        data["proportion"]["http_cnt"] = "0"
        data["proportion"]["ftp_cnt"] = "0"

        last_stat_info = None
        last_stat_info = self.get_last_stat_info()
        if last_stat_info :
            data["proportion"]["ssl_cnt"] = last_stat_info["ssl_parser_bytes"]["total"] * 8
            data["proportion"]["http_cnt"] = last_stat_info["http parser bytes"]["total"] * 8
            data["proportion"]["ftp_cnt"] = last_stat_info["ftp_parser_bytes"]["total"] * 8
            data["proportion"]["total_cnt"] = last_stat_info["ip_bytes"]["total"] * 8
            data["ssl_parser_bits"] = last_stat_info["ssl_parser_bytes"]["total"] * 8
        
        data["ssl_ips"] = {}
        if os.path.exists(self.ssl_ips_file):
            with open(self.ssl_ips_file,'r+') as file:
                for line in file:
                    ips = line.split(",")
                    data["ssl_ips"][ips[0]] = {}
                    data["ssl_ips"][ips[0]]["suite"] = ips[1]
                    data["ssl_ips"][ips[0]]["version"] = ips[2].strip()

        return jsonify(msg="ok", err=0, data=data)