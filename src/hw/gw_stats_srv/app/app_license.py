#!/usr/bin/python
#coding:utf-8

import licutils
import sys
import logging
import os
import json
import threading
from collections import OrderedDict

from flask import jsonify,request

from app.app_function import Singleton

class license:
    __metaclass__ = Singleton
    
    def __init__(self): 
        # gw_parser配置文件路径
        self.gw_parser_cfg = "/opt/data/apigw/gwhw/gw_parser.conf"

        # 版本信息文件路径
        self.version_file_dir = "/opt/apigw/gwhw/version.txt"
        self.product_sn = "gw010502"
        self.product_id = "2"
        # license文件目录
        self.license_file_dir = "/opt/licutils/"
        self.license_file_name = "license.lic"

        if os.path.isfile(self.gw_parser_cfg):
            with open(self.gw_parser_cfg,"rb") as file_obj:
                fileJson = json.load(file_obj,object_pairs_hook=OrderedDict)
        # pem文件目录
        self.pem_dir = fileJson["parser"]["pem_dir"]

        if not os.path.exists(self.pem_dir):
            os.makedirs(self.pem_dir)
        

    def get_version_info(self):
        file = open(self.version_file_dir, "r")
        version = file.readline().strip("\n")
        file.close()
        return version

    def api_get_basic_info(self):
        version = self.get_version_info()
        if (version != ""):
            return jsonify(success="true", errorCode=0, data={\
                "productId":self.product_id,\
                "productName":"网关",\
                "productSerial":self.product_sn,\
                "oldVersion":"",\
                "oldDesc":"",\
                "version":version,\
                "desc":"1.5.1",\
                "time":0\
                })
        else:
            return jsonify(success="false", errorCode=-1, data=None)
    

    def licutils_init(self):
        license_file_path = self.license_file_dir + self.license_file_name
        version = self.get_version_info().split('.', 1)[0]
        r = licutils.init_license(license_file_path,version, self.product_id, self.product_sn)
        if r:
            print >> sys.stderr, "licutils_init error: ", r
            logging.error("licutils_init error: %d" % r)
            return r
        return r

    def api_get_license_info(self):
        license_file_path = self.license_file_dir + self.license_file_name
        version = self.get_version_info()
        r = self.licutils_init()
        err = 0
        start_time = ""
        end_time = ""
        state = 0 # 0 未授权; 1 授权，未过期; 2 过期
        if r == 0:
            try:
                start_time = licutils.get_product_base_info(self.product_id, "start_time")
                end_time = licutils.get_product_base_info(self.product_id, "end_time")
                if licutils.check_expired(self.product_id, None):
                    state = 2
                else:
                    state = 1
            except RuntimeError as e:
                logging.warning(e)
                err = e.message.split()[4]
            licutils.release_license_info()
        elif r != -201: # 不存在授权文件
            err = r
        return jsonify(success="true", errorCode=0, data={\
            "productId":"2",\
            "productName":"网关",\
            "version":version,\
            "product_serial":self.product_sn,\
            "start_time":start_time,\
            "end_time":end_time,\
            "state":state,\
            "errorCode":err})


    def api_get_machineCode(self):
        return jsonify(success="true", errorCode=0, data=licutils.get_unique_code())

    def gwhw_server_restart(self):
        try:
            os.system("/usr/bin/supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser")
        except OSError as e:
            logging.error(e)
           
    def api_upload_license(self):
        if request.method == 'POST':
            try:
                f = request.files['multipartFile']
                logging.debug(self.license_file_dir + self.license_file_name)
                logging.debug(f.filename)
                if f and f.filename != '':
                    f.save(self.license_file_dir + self.license_file_name)
                    logging.info("upload ok")
                    gwhw_server_restart_thread = threading.Thread(target=self.gwhw_server_restart,args=())
                    gwhw_server_restart_thread.start() 
                    return jsonify(success="true", errorCode=0, data="success")
                return jsonify(success="false", msg="errorCode:-502", errorCode=0)
            except:
                logging.info("upload err try")
                return jsonify(success="false", msg="errorCode:-502", errorCode=0)
        else:
            logging.info("upload err not post")
    
    
    def license_check(self):
        state = False
        license_file_path = self.license_file_dir + self.license_file_name
        version = self.get_version_info()
        r = self.licutils_init()
        if r == 0:
            try:
                start_time = licutils.get_product_base_info(self.product_id, "start_time")
                end_time = licutils.get_product_base_info(self.product_id, "end_time")
                if licutils.check_expired(self.product_id, None):
                    pass
                else:
                    state = True
            except RuntimeError as e:
                logging.warning(e)
                err = e.message.split()[4]
            licutils.release_license_info()
        return state
    
    def api_upload_pem(self):
        if request.method == 'POST':
            try:
                f = request.files['multipartFile']
                logging.debug(self.pem_dir + f.filename)
                logging.debug(f.filename)
                message = ""
                if f and f.filename != '':
                    if ".pem" in f.filename:
                        if os.path.exists(self.pem_dir + f.filename):
                            message = "already exists, overwritten!"
                            f.save(self.pem_dir + f.filename)
                        else:
                            f.save(self.pem_dir + f.filename)
                            message = "upload success"
                        logging.info("upload ok")
                    else:
                        return jsonify(success="false", msg="unsupported format!", errorCode=-1)
                    gwhw_server_restart_thread = threading.Thread(target=self.gwhw_server_restart,args=())
                    gwhw_server_restart_thread.start()
                    return jsonify(success="true", errorCode=0, msg=message)
                else:
                    return jsonify(success="false", msg="upload error,filename is null!", errorCode=-2)
            except Exception as e:
                logging.error(e)
                return jsonify(success="false", msg="upload error", errorCode=-2)
        else:
            logging.info("upload err not post")
            
    def api_delete_pem(self):
        content = request.get_json(force = True)
        if None == content:
            return jsonify(success="false", errorCode=-1, msg="do not get json content")
        if None == content.get("name"):
            return jsonify(success="false", errorCode=-2, msg="do not get name")
        name = content["name"]
        if not os.path.exists(self.pem_dir + name):
            return jsonify(success="false", errorCode=-3, msg="not exit!")
        ret = os.system("rm -f " + self.pem_dir + name)
        if ret != 0:
            return jsonify(success="false", errorCode=-4, msg="delete fail!")
        return jsonify(success="true", errorCode=0, msg="delete success")