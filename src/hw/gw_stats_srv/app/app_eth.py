#!/usr/bin/python
#coding:utf-8


import os
import logging
import threading
import time
import json
import subprocess
import Queue
from itertools import islice
from collections import OrderedDict

from flask import request,jsonify
from app.app_function import Singleton
from imp import reload

import sys
reload(sys) 
sys.setdefaultencoding('utf-8')

class eth:
    __metaclass__ = Singleton
    
    def __init__(self): 
        self.all_eth_file = "/proc/net/dev"
        self.virtual_eth_dir = "/sys/devices/virtual/net/"
        # gw_parser配置文件路径
        self.gw_parser_cfg = "/opt/data/apigw/gwhw/gw_parser.conf"
        self.dpdk_black_list = "/opt/data/apigw/gwhw/dpdk_black_list.txt"
        
        self.mode_name = list()
        
        self.all_eth = list()
        self.virtual_eth = list()
        self.physics_eth = list()
        
        if os.path.isfile(self.gw_parser_cfg):
            with open(self.gw_parser_cfg,"rb") as file_obj:
                fileJson = json.load(file_obj,object_pairs_hook=OrderedDict)
        
        # 获得当前安装模式
        source_path = fileJson["plugin"]["source_path"]
        if source_path.count("file_source") == 1:
            self.mode_name.append("agent")
        if  source_path.count("nic_source") == 1:
            self.mode_name.append("nic")
        elif source_path.count("dpdk_source") == 1:
            self.mode_name.append("dpdk")
        if source_path.count("pcap_source") == 1:
            self.mode_name.append("pcap")
                    
    def api_eth_information(self, health=False):
        if os.path.isfile(self.gw_parser_cfg):
            with open(self.gw_parser_cfg,"rb") as file_obj:
                fileJson = json.load(file_obj,object_pairs_hook=OrderedDict)
        self.all_eth = list()
        self.virtual_eth = list()
        self.physics_eth = list()

        self.get_eth_basic_info()
        
        if self.mode_name == None:
            if health == True:
                return None
            return jsonify( msg="source_mode error: source_mode shouldn't be None" , err = 100 , data = None)
        
        if len(self.mode_name) == 1 and "agent" in self.mode_name:
            data = {}
            data["source_mode"] = self.mode_name
            if health == True:
                return None
            return jsonify( msg="ok" , err = 0 , data = data)
            
        if "nic" in self.mode_name:
            eth_information = {}
            eth_information["bind"] = None
            eth_information["unbind"] = None
            eth_information["source_mode"] = self.mode_name
            bind = list()
            unbind = list()
            
            nic_bind_list = fileJson["parser"]["nic_device_name"].split(",")
            
            bind_que = Queue.Queue()
            bind_thread_list = list()
            unbind_que = Queue.Queue()
            unbind_thread_list = list()
            
            for eth_name in self.physics_eth:
                is_bind =False
                for list_item in nic_bind_list:
                    if list_item == eth_name:
                        bind_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(bind_que,eth_name)))
                        is_bind = True
                        break
                if is_bind:
                    continue
                unbind_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(unbind_que,eth_name)))
            
            for t in bind_thread_list:
                t.start()
            for t in unbind_thread_list:
                t.start()       
            for t in bind_thread_list:
                t.join()
            for t in unbind_thread_list:
                t.join()
            
            while not bind_que.empty():
                result = bind_que.get()
                if result:
                    bind.append(result)
            while not unbind_que.empty():
                result = unbind_que.get()
                if result:
                    unbind.append(result)
            
            eth_information["bind"] = bind
            eth_information["unbind"] = unbind
            if health == True:
                return eth_information
            return jsonify( msg="ok" , err = 0 , data = eth_information)
                
        if "dpdk" in self.mode_name:
            eth_information = {}
            eth_information["bind"] = None
            eth_information["unbind"] = None
            eth_information["source_mode"] = self.mode_name
            bind = list()
            dpdk_bind_list = list()
            unbind = list()
            dpdk_unbind_list = list()
            dpdk_bind_thread_list = list()
            dpdk_unbind_thread_list = list()
            dpdk_bind_que = Queue.Queue()
            dpdk_unbind_que = Queue.Queue()
            
            with open(self.dpdk_black_list, 'w') as dpdk_black_list_file:
                lines = dpdk_black_list_file.readlines()
                for line in lines:
                    if line.find("#") != -1:
                        continue
                    else:
                        dpdk_unbind_list.append(line)
            
            dpdk_bind_list = [x for x in self.physics_eth if x not in dpdk_unbind_list]
            
            for eth_name in dpdk_unbind_list:
                dpdk_unbind_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(dpdk_unbind_que,eth_name)))
            for eth_name in dpdk_bind_list:
                dpdk_bind_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(dpdk_bind_que,eth_name)))
                
            for t in dpdk_unbind_thread_list:
                t.start()
            for t in dpdk_bind_thread_list:
                t.start()
            for t in dpdk_unbind_thread_list:
                t.join()
            for t in dpdk_bind_thread_list:
                t.join()
                
            while not dpdk_unbind_que.empty():
                result = dpdk_unbind_que.get()
                if result:
                    unbind.append(result)
            while not dpdk_bind_que.empty():
                result = dpdk_bind_que.get()
                if result:
                    bind.append(result)
            
            eth_information["bind"] = bind
            eth_information["unbind"] = unbind
            if health == True:
                return eth_information
            return jsonify( msg="ok" , err = 0 , data = eth_information)
        
        

    def get_eth_basic_info(self):
        with open(self.all_eth_file) as file_obj:
            for line in islice(file_obj,2,None):
                spliter = line.split(":")[0]
                self.all_eth.append(spliter.strip())
                
        self.virtual_eth = os.listdir(self.virtual_eth_dir)
        self.physics_eth = [x for x in self.all_eth if x not in self.virtual_eth]
    
    
    def get_eth_speed(self,name):
        # sleeptime 是为了计算流量
        sleeptime = 5
        ifconfig_result = os.popen("ifconfig %s"% name)
        lines = ifconfig_result.readlines()
        
        for line in lines:
            if line.count("RX packets") == 1:
                spliter = line.split("bytes")[1]
                strnum = spliter.split("(")[0].strip()
                RX_before_pack = int(strnum)
            if line.count("TX packets") == 1:
                spliter = line.split("bytes")[1]
                strnum = spliter.split("(")[0].strip()
                TX_before_pack = int(strnum)
        
        time.sleep(sleeptime)
        
        ifconfig_result = os.popen("ifconfig %s"% name)
        lines = ifconfig_result.readlines()
        for line in lines:
            if line.count("RX packets") == 1:
                spliter = line.split("bytes")[1]
                strnum = spliter.split("(")[0].strip()
                RX_after_pack = int(strnum)
            if line.count("TX packets") == 1:
                spliter = line.split("bytes")[1]
                strnum = spliter.split("(")[0].strip()
                TX_after_pack = int(strnum)
        
        if(RX_after_pack <= RX_before_pack and TX_after_pack <= TX_before_pack):
            return None,None
        else:
            # 返回 MB单位
            RX_speed = 8.0 * float(RX_after_pack - RX_before_pack) / float(sleeptime*1024*1024)
            TX_speed = 8.0 * float(TX_after_pack - TX_before_pack) / float(sleeptime*1024*1024)
            RX_speed = format(RX_speed, '.2f')
            TX_speed = format(TX_speed, '.2f')
        return RX_speed,TX_speed

    def os_unbind(self):
        logging.info("entering os_unbind")
        subprocess.check_call("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf stop gw_parser",shell=True)
        subprocess.check_call("sh /opt/apigw/gwhw/stats_srv/dpdk_unbind.sh 1> /dev/null",shell=True)
        res = os.popen('mount | grep "/mnt/huge" | wc -l').read()
        if int(res) > 0:
            subprocess.check_call('\
                                    supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf stop gw_parser &&  \
                                    sudo umount /mnt/huge && \
                                    sudo rm -rf /mnt/huge    &&\
                                    sudo su -c "echo 0 > /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages"    &&\
                                    sudo su -c "echo 0 > /sys/devices/system/node/node0/hugepages/hugepages-2048kB/nr_hugepages"    \
                                        ',shell=True)
            if os.path.exists("/sys/devices/system/node/node1/hugepages/hugepages-1048576kB/"):
                subprocess.check_call('sudo su -c "echo 0 > /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/nr_hugepages"',shell=True)
            if os.path.exists("/sys/devices/system/node/node1/hugepages/hugepages-2048kB/"):
                subprocess.check_call('sudo su -c "echo 0 > /sys/devices/system/node/node1/hugepages/hugepages-2048kB/nr_hugepages"',shell=True)
        logging.info("leaving os_unbind")
    
    def api_eth_bind(self):
        logging.info("entering api_eth_bind")
        if self.mode_name == None:
            return jsonify( msg="mode_name error: mode_name shouldn't be None" , err = 400 , data = None)
        if len(self.mode_name) == 1 and "agent" in self.mode_name:
            return jsonify( msg="The mode agent has no method to bind" , err = 100 , data = None)
        
        eth_dict = request.get_json(force = True)
        if not eth_dict:
            return jsonify( msg="please input params" , err = 500 , data = None)
        self.get_eth_basic_info()
        for todo_eth_name in eth_dict:
            eth_exist_flag = False
            for eth_name in self.all_eth:
                if eth_name == todo_eth_name:
                    eth_exist_flag = True
                    break
            if not eth_exist_flag:
                return jsonify( msg="please check your input, eth_name " + todo_eth_name + " invalid" , err = 200 , data = None)
        
        
        # 重新绑定之前解绑
        
        self.os_unbind()
        
        if os.path.isfile(self.gw_parser_cfg):
            with open(self.gw_parser_cfg,"rb") as file_obj:
                fileJson = json.load(file_obj,object_pairs_hook=OrderedDict)

        if "nic" in self.mode_name:
            
            nic_device_name = fileJson["parser"]["nic_device_name"].split(",")
            for todo_eth_name in eth_dict:
                if eth_dict[todo_eth_name] == 1:
                    nic_device_name.append(todo_eth_name)
                elif eth_dict[todo_eth_name] == 0:
                    if todo_eth_name in nic_device_name:
                        nic_device_name.remove(todo_eth_name)
            # 去重
            nic_device_name = list(set(nic_device_name))

            line_num = os.popen("grep -n nic_device_name /opt/data/apigw/gwhw/gw_parser.conf | cut -d : -f 1").readline().rstrip("\n")
            new_bind_eth = "\\\"nic_device_name\\\":\\\"%s\\\"," % (",".join(nic_device_name))
            cmd = "sed -i \"%sc \    %s\" /opt/data/apigw/gwhw/gw_parser.conf" % (line_num, new_bind_eth)
            logging.info("rum cmd=%s" % cmd)
            if os.system(cmd) != 0:
                return jsonify( msg="change gw_parser.conf nic fail" , err = 600 , data = None)

            nic_device_name_list = list()
            nic_thread_list = list()
            nic_que = Queue.Queue()
            for nic_device_name_item in nic_device_name:
                nic_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(nic_que,nic_device_name_item)))

            for t in nic_thread_list:
                t.start()
            for t in nic_thread_list:
                t.join()
            while not nic_que.empty():
                result = nic_que.get()
                if result:
                    nic_device_name_list.append(result)
            
            subprocess.check_call("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf start gw_parser",shell=True)
            return jsonify( msg="ok" , err = 0 , data = {"nic_bind_list":nic_device_name_list})
        
        if "dpdk" in self.mode_name:
            logging.info("rebind dpdk start")
            
            available_dpdk_eth = list()
            for todo_eth_name in eth_dict:
                    if eth_dict[todo_eth_name] == 0:
                        available_dpdk_eth.append(todo_eth_name)
                        
            with open(self.dpdk_black_list,"w") as dpdk_black_list_writer:
                for item in  available_dpdk_eth:
                    dpdk_black_list_writer.write(item)
                    dpdk_black_list_writer.write("\n")
                    
            logging.info("doing init_dpdk.sh")
            subprocess.check_call("sh /opt/apigw/gwhw/stats_srv/dpdk_init.sh",shell=True)
            logging.info("init_dpdk.sh done")
            
            dpdk_bind_list = list()
            dpdk_bind_eth = [x for x in self.physics_eth if x not in available_dpdk_eth]
            dpdk_thread_list = list()
            dpdk_que = Queue.Queue()
            
            for dpdk_bind_eth_item in dpdk_bind_eth:
                dpdk_thread_list.append(threading.Thread(target=lambda q, arg1: q.put(self.get_eth_info(arg1)),args=(dpdk_que,dpdk_bind_eth_item)))

            for t in dpdk_thread_list:
                t.start()
            for t in dpdk_thread_list:
                t.join()
            while not dpdk_que.empty():
                result = dpdk_que.get()
                if result:
                    dpdk_bind_list.append(result)
            subprocess.check_call("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf start gw_parser",shell=True)
            return jsonify( msg="ok" , err = 0 , data = {"dpdk_bind_list":dpdk_bind_list})
    
    
    def get_eth_info(self,eth_name):
        eth_info = {}
        eth_info["eth_name"] = eth_name
        eth_info["mirror"] = None
        eth_info["flow"] = None
        eth_info["ip"] = None
        eth_info["rx_speed"] = None
        eth_info["tx_speed"] = None
        eth_info["status"] = None
        eth_info["eth_speed"] = None
        eth_info["mirrorable"] = None
        eth_info["speed_unit"] = "Mb/s"
        
        ethtool_readlines = os.popen("ethtool %s"% eth_name).readlines()
        ethtool_flag = False
        eth_speed_list = list()
        for line in ethtool_readlines:
            if line.find("Supported link modes:") != -1:
                ethtool_flag = True
                speed = line.split(":")[1].split()
                for item in speed:
                    eth_speed_list.append(item.strip())
            elif ethtool_flag == True and line.find(":") == -1:
                speed = line.split()
                for item in speed:
                    eth_speed_list.append(item.strip())
                continue
            elif ethtool_flag == True and line.find(":") != -1:
                break
        if eth_speed_list:
            eth_info["eth_speed"] = eth_speed_list[-1]
        else:
            eth_info["eth_speed"] = "UNKOWN!"
        
        
        ifconfig_read = os.popen("ifconfig %s"% eth_name).read()
        
        eth_info["mirrorable"] = 1
        if (ifconfig_read.find("MASTER") >= 0 or ifconfig_read.find("SLAVE") >= 0):
            eth_info["mirrorable"] = 0
        
        if ifconfig_read.count("UP") > 0:
            eth_info["status"]  = "up"
        if ifconfig_read.count("DOWN") > 0:
            eth_info["status"]  = "down"
            
        # 获得管理口
        if ifconfig_read.count("inet") >= 2:
            ipv4 = ifconfig_read.split("netmask")[0].split("inet")[1].strip()
            eth_info["ip"] = ipv4
            
        # 获得镜像口
        eth_info["mirror"] = 0
        if ifconfig_read.count("RUNNING") == 1:
            if ifconfig_read.count("inet") == 0:
                eth_info["mirror"] = 1
            elif ifconfig_read.count("inet") == 1 and ifconfig_read.count("inet6") == 1:
                eth_info["mirror"] = 1
        
        eth_info["rx_speed"], eth_info["tx_speed"] = self.get_eth_speed(eth_name)

        if eth_info["rx_speed"] and eth_info["tx_speed"]:
            eth_info["flow"] = 1
        else:
            eth_info["flow"] = 0
            
        return eth_info
    
    def api_get_mode(self):
        if len(self.mode_name) == 0:
            return jsonify( msg="the mode is unknown" , err = 300 , data = None)
        data = {}
        data["source_mode"] = self.mode_name
        return jsonify( msg="ok" , err = 0 , data = data)
        