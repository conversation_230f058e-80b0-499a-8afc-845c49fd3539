#!/usr/bin/python
#coding:utf-8

import socket
import logging
import os
import json
import threading

SOCKET_SERVER = "127.0.0.1"
SOCKET_PORT = 23

class Singleton(type):
    _instances = {}
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

def get_req_param(request, field):
    if request.method == 'POST':
        data = request.form.get(field)
    else:
        data = request.args.get(field)
    return data

def send_command(command):
    
    sock = socket.socket()
    try:
        sock.connect((SOCKET_SERVER, SOCKET_PORT))
    except:
        return b'{"success"="false", "errCode"=-108, "msg"="connect gw 23 port failed"}'#jsonify(success="false", errCode=-108, msg="connect gw 23 port failed")
    sock.send(command)
    response = b''
    chunk = sock.recv(1024)
    # while chunk:  # 循环接收数据，因为一次接收不完整
    #     response += chunk
    #     chunk = sock.recv(1024)
    response += chunk
    # print(response.decode())
    logging.info(response.decode())

    sock.close()
    return response

class history_pcap_dir_syn:
    __metaclass__ = Singleton

    def __init__(self): 
        self.history_pcap_dir = "/home/<USER>/risk-eval-core/pcap/history/"
        self.history_pcap_dir_lock = threading.Lock()
        if os.path.exists("gw_stat_srv.conf"):
            try:
                conf_content = open("gw_stat_srv.conf").read()
                js_cf = json.loads(conf_content)
                if js_cf["history_default_dir"] and js_cf["history_default_dir"] != "":
                    self.history_pcap_dir = js_cf["history_default_dir"]
            except Exception as e:
                logging.error(e)
    
    def set(self,dir):
        with self.history_pcap_dir_lock:
            self.history_pcap_dir = dir
            print(self.history_pcap_dir)
        
    def get(self):
        with self.history_pcap_dir_lock:
            dir = self.history_pcap_dir
        return dir