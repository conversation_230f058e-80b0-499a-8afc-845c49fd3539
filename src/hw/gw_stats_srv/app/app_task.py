#!/usr/bin/python
#coding:utf-8


import logging
import os
import threading
import shutil
import time
import json

from flask import request,jsonify
from app.app_function import send_command, get_req_param
from app.app_function import history_pcap_dir_syn,Singleton

class task:
    __metaclass__ = Singleton
    
    def __init__(self): 
        self.start_risk = "start_risk"
        self.stop_risk = "stop_risk"
        self.pcap_dir = "/opt/pcap/task/"
        self.gw_parser_cfg = "/opt/data/apigw/gwhw/gw_parser.conf"
        self.hispcap = history_pcap_dir_syn()
        
        if os.path.isfile(self.gw_parser_cfg):
            file = open(self.gw_parser_cfg, "rb")
            fileJson = json.load(file)
            self.pcap_dir = fileJson['parser']['pcap_dir']
                    
    def api_risk_status(self):
        return send_command("task_status")

    # 数据出境 网关任务起停接口
    def api_task_start_stop(self):
        # 任务启停标志 1 开启任务 2 停止任务
        taskflag = get_req_param(request, "taskFlag")
        if not taskflag or len(taskflag) == 0:
            return jsonify(success="false", errCode=-100, msg="taskFlag not exist")

        if int(taskflag) != 1 and int(taskflag) != 2:
            logging.debug(taskflag)
            return jsonify(success="false", errCode=-101, msg="invaild taskFlag param")

        taskid = get_req_param(request, "taskId")
        if not taskid or len(taskid) == 0:
            return jsonify(success="false", errCode=-102, msg="taskId not exist")

        target_type = get_req_param(request, "targetType")
        if not target_type or len(target_type) == 0:
            return jsonify(success="false", errCode=-103, msg="targetType not exist")
        if int(target_type) != 1 and int(target_type) != 2 and int(target_type) != 3 and int(target_type) != 4:
            return jsonify(success="false", errCode=-104, msg="invaild targetType param")

        save_history_flag = get_req_param(request, "saveHistoryFlag")
        if not save_history_flag or len(save_history_flag) == 0:
            return jsonify(success="false", errCode=-105, msg="saveHistoryFlag not exist")

        command = ""
        if int(taskflag) == 1:  # 开启任务
            # 先调用清理
            self.pcap_clean()

            save_history_dir = ""
            parser_dir = ""

            if int(save_history_flag) == 1:
                if int(target_type) != 1 and int(target_type) != 2 and int(target_type) != 4:  # 流量镜像和agent模式才需要转储流量
                    return jsonify(success="false", errCode=-110, msg="该模式不支持历史流量保存")
                save_history_dir = self.hispcap.get() + '/' + taskid
                logging.info("task_start_stop task_start history dir: {0}".format(save_history_dir))
                if not os.path.exists(save_history_dir):
                    os.makedirs(save_history_dir)
                command = self.start_risk + '|' + target_type + '|' + save_history_flag + '|' + save_history_dir
            else:
                command = self.start_risk + '|' + target_type + '|' + save_history_flag

            if int(target_type) == 3:
                parser_dir = get_req_param(request, "dirPaths")
                if not parser_dir or len(parser_dir) == 0:
                    return jsonify(success="false", errCode=-106, msg="dirPath param not exist")

                logging.info("task_start_stop replay history parser_dir: {0}".format(parser_dir))

                try:
                    parser_dir_list = eval(parser_dir)
                except:
                    return jsonify(success="false", errCode=-109, msg="dirpath format error")

                for dirpath in parser_dir_list:
                    if not os.path.exists(dirpath):
                        return jsonify(success="false", errCode=-107, msg="dirPath not exist")

                    logging.info("task_start_stop replay history dirpath: {0}".format(dirpath))
                    self.pcap_cp_asyn(dirpath)

            logging.info("task_start_stop task_start command: {0}".format(command))
            if command:
                return send_command(command)
            else:
                return jsonify(success="false", errCode=-108, msg="command is null")

        else:  # 停止任务
            command = self.stop_risk
            logging.info("task_stop")
            return send_command(command)


    def get_gwhw_status(self):
        return send_command("gwhw_status")


    def api_cache_clean(self):
        errCode = self.pcap_clean()
        if errCode != 0:
            return jsonify(success="false", errCode=errCode)
        
        return send_command("cache_clean")# 清理 gwhw 任务队列

    # 清理pcap
    def pcap_clean(self):
        return os.system("rm -rf " + self.pcap_dir + "/*.pcap")
    
    # 从指定目录拷贝 pcap 到解析目录 syn/asyn
    def pcap_cp(self,dirPath):
        #os.system("cp " + dirPath + "/*.pcap " + pcap_dir)
        file_list = os.listdir(dirPath)
        if not file_list:
            return
        else:
            file_list = sorted(file_list,  key=lambda x: os.path.getmtime(os.path.join(dirPath, x)))
            for file in file_list:
                src_name = os.path.join(dirPath, file)
                if os.path.splitext(src_name)[1] == ".pcap":
                    dst_name = os.path.join(self.pcap_dir, file)
                    shutil.copyfile(src_name, dst_name)
                    time.sleep(0.1)
                    
    def pcap_cp_asyn(self,dirPath):
        try:
            pcap_cp_thread = threading.Thread(target=self.pcap_cp,args=(dirPath,))
            pcap_cp_thread.start() 
            return True
        except Exception as e:
            print(e)
            print("Error: unable to start thread")
            return False
