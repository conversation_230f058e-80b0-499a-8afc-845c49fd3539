#!/usr/bin/python
#coding:utf-8


import os
import json
import logging
import threading
import shutil

from flask import request,jsonify
from app.app_function import Singleton,send_command

class settings:
    __metaclass__ = Singleton
    
    def __init__(self): 
        # gw_parser配置文件路径
        # gw_parser_cfg = "/opt/apigw/gwhw/gw_parser.conf"
        self.gw_parser_cfg = "/opt/data/apigw/gwhw/gw_parser.conf"

        # http_url_filter文件路径
        self.http_url_filter = "/opt/urlbase/url_filter_dynamic.file"

        
    def set_domain_ip_to_file(self,domain, ip):
        cmd = "cat -n /etc/hosts | grep %s | awk '{print $1}'" % domain
        # print("cmd=" + cmd)
        output = os.popen(cmd, "r").readlines()
        express = ""
        for line_num in output:
            line_num = int(line_num)
            express += "-e '%dd' " % line_num

        if len(express) > 0:
            cmd = "sed -i %s /etc/hosts" % express
            # print("cmd=" + cmd)
            os.system(cmd)

        cmd = "echo \"%s %s\" >> /etc/hosts" % (ip, domain)
        # print("cmd=" + cmd)
        os.system(cmd)

        # print("===1===")
        # print(os.popen("cat /etc/hosts", "r").readlines())
        # print("===2===")

    def set_domain_to_ip(self):
        err_msg = ""
        try:
            err_msg = "get post json data fail"
            content = request.get_json(force = True)

            err_msg = "open config file [%s] fail" % self.gw_parser_cfg
            f = open(self.gw_parser_cfg, "r")
            gw_parser_conf = json.load(f)
            f.close()

            err_msg = "open config file gw_stat_srv.conf fail"
            f = open("gw_stat_srv.conf", "r")
            gw_stat_srv_conf = json.load(f)
            f.close()

            err_msg = "get minio domain fail"
            minio_domain = gw_parser_conf["minio"]["endpoint"]
            index = minio_domain.rfind(":")
            if -1 == index:
                index = None
            minio_domain = minio_domain[ : index]

            err_msg = "get kafka domain fail"
            kafka_domain = gw_parser_conf["parser"]["kafka"]["brokers_list"]["http_ruled"][0]["host"]
            index = kafka_domain.rfind(":")
            if -1 == index:
                index = None
            kafka_domain = kafka_domain[ : index]

            err_msg = "get nacos domain fail"
            nacos_domain = gw_stat_srv_conf["nacos_conf"]["server_addrs"]
            index = nacos_domain.rfind(":")
            if -1 == index:
                index = None
            nacos_domain = nacos_domain[ : index]

            err_msg = "set minio domain fail"
            self.set_domain_ip_to_file(minio_domain, content["ip"])

            err_msg = "set kafka domain fail"
            self.set_domain_ip_to_file(kafka_domain, content["ip"])

            err_msg = "set nacos domain fail"
            self.set_domain_ip_to_file(nacos_domain, content["ip"])

        except Exception as e:
            logging.error(e)
            return jsonify(success = False, error_msg = err_msg)

        threading.Timer(1, lambda : os.system("systemctl restart gwhw")).start()
        return jsonify(success = True, error_msg = "success")

    def set_drop_http_file_event(self):
        content = request.get_json(force = True)
        if content["is_drop"]:
            return send_command("drop_http_file_event")
        else:
            return send_command("save_http_file_event")

    def set_control_strategy(self):
        content = request.get_json(force = True)
        if None == content:
            return jsonify(success="false", errCode=-1, msg="do not get json content")
        if None == content.get("parameter"):
            return jsonify(success="false", errCode=-1, msg="do not get parameter")
        if 1 == content["parameter"]:
            if None == content.get("id") or None == content.get("controlName") or  None == content.get("controlType") or None == content.get("controlContents"):
                return jsonify(success="false", errCode=-1, msg="lost some parameter")
            command = "add_control_strategy|" + content["id"] + "|" + content["controlName"] + "|" + content["controlType"] + "|" + json.dumps(content["controlContents"])
        elif 2 == content["parameter"]:
            if None == content.get("id"):
                return jsonify(success="false", errCode=-1, msg="lost some parameter")
            command = "del_control_strategy|" + content["id"]
        else:
            return jsonify(success="false", errCode=-1, msg="unknown parameter")
        return send_command(command)

    def set_interactive_http_url_filter(self):
        content = request.get_json(force = True)
        if None == content:
            return jsonify(success="false", errCode=-1, msg="do not get json content")
        if None == content.get("urls"):
            return jsonify(success="false", errCode=-2, msg="do not get urls")
        urls = content["urls"]
        url = ""
        for param in urls:
            url = url + param + '\n'
        with open(self.http_url_filter,"w+") as file_object:
            file_object.write(url)
        return jsonify(success="true", errCode=0, msg="set http url filter success")

    def set_clean_interactive_http_url_filter(self):
        if os.path.exists(self.http_url_filter):
            with open(self.http_url_filter,"r+") as file:
                file.truncate(0)
            return jsonify(success="true", errCode=0, msg="clean http url filter success")
        else:
            return jsonify(success="false", errCode=-1, msg="file not exist")
    
    def set_toggle_mode(self):
        content = request.get_json(force = True)
        if None == content:
            return jsonify(success="false", errCode=-1, msg="do not get json content")
        if None == content.get("mode"):
            return jsonify(success="false", errCode=-2, msg="do not get mode")
        modes = content["mode"].split(',')
        loadFilesCmd = os.popen("grep load_files " + self.gw_parser_cfg + " | tail -1 | cut -d \" -f 4 | sed -e s/file_source.so//g -e s/::/:/g -e s/nic_source.so//g -e s/::/:/g -e s/dpdk_source.so//g -e s/::/:/g -e s/pcap_source.so//g -e s/::/:/g")
        loadOtherFiles = loadFilesCmd.read()
        loadFiles = "\"load_files\": "
        sourcePath = "\"source_path\": "

        if modes[0] == "agent":
            sourcePath = sourcePath + "\"source/file_source/"
            loadFiles = loadFiles + "\"file_source.so:"
        elif modes[0] == "nic":
            sourcePath = sourcePath + "\"source/nic_source/"
            loadFiles = loadFiles + "\"nic_source.so:"
        elif modes[0] == "pcap":
            sourcePath = sourcePath + "\"source/pcap_source/"
            loadFiles = loadFiles + "\"pcap_source.so:"
        elif modes[0] == "dpdk":
            sourcePath = sourcePath + "\"source/dpdk_source/"
            loadFiles = loadFiles + "\"dpdk_source.so:"
        else:
            return jsonify(success="false", errCode=-3, msg="mode:" + modes[0] + "is unknown")

        for index in range(1,len(modes)):
            if modes[index] == "agent":
                sourcePath = sourcePath + ":source/file_source/"
                loadFiles = loadFiles + "file_source.so:"
            elif modes[index] == "nic":
                sourcePath = sourcePath + ":source/nic_source/"
                loadFiles = loadFiles + "nic_source.so:"
            elif modes[index] == "pcap":
                sourcePath = sourcePath + ":source/pcap_source/"
                loadFiles = loadFiles + "pcap_source.so:"
            elif modes[index] == "dpdk":
                sourcePath = sourcePath + ":source/dpdk_source/"
                loadFiles = loadFiles + "dpdk_source.so:"
            else:
                return jsonify(success="false", errCode=-3, msg="mode:" + modes[index] + "is unknown")
        sourcePath = sourcePath + "\","
        loadFiles = loadFiles + loadOtherFiles + "\","

        loadFilesCmd = os.popen("echo " + loadFiles + " | sed s/::/:/g")
        loadFiles = loadFilesCmd.read()

        lineNumCmd = os.popen("grep -n source_path " + self.gw_parser_cfg + "|  tail -1 | cut -d : -f 1")
        lineNum = lineNumCmd.read()
        os.system("sed -i \"" + lineNum + "d\" " + self.gw_parser_cfg)
        os.system("sed -i \"" + lineNum + "i " + sourcePath + "\" " + self.gw_parser_cfg)
        os.system("sed -i \"" + lineNum + "s/^/    /g\" " + self.gw_parser_cfg)

        lineNumCmd = os.popen("grep -n load_files " + self.gw_parser_cfg + "|  tail -1 | cut -d : -f 1")
        lineNum = lineNumCmd.read()
        os.system("sed -i \"" + lineNum + "d\" " + self.gw_parser_cfg)
        os.system("sed -i \"" + lineNum + "i " + loadFiles + "\" " + self.gw_parser_cfg)
        os.system("sed -i \"" + lineNum + "s/^/    /g\" " + self.gw_parser_cfg)

        return jsonify(success="true", errCode=0, msg="toggle mode success")

    def start_gwhw(self):
        err_msg = ""

        try:
            err_msg = "exec start cmd fail"
            output = os.popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser").readlines()

            err_msg = output
            for line in output:
                if line.find("started"):
                    return jsonify(success = True, error_msg = "success")
        except Exception as e:
            logging.error(e)

        return jsonify(success = False, error_msg = err_msg)

    def stop_gwhw(self):
        err_msg = ""

        try:
            err_msg = "exec stop cmd fail"
            output = os.popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf stop gw_parser").readlines()

            err_msg = output
            for line in output:
                if line.find("stopped"):
                    return jsonify(success = True, error_msg = "success")
        except Exception as e:
            logging.error(e)

        return jsonify(success = False, error_msg = err_msg)

    def restart_gwhw(self):
        err_msg = ""

        try:
            err_msg = "exec restart cmd fail"
            output = os.popen("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser").readlines()

            err_msg = output
            for line in output:
                if line.find("started"):
                    return jsonify(success = True, error_msg = "success")
        except Exception as e:
            logging.error(e)

        return jsonify(success = False, error_msg = err_msg)

    def uninstall_gwhw(self):
        threading.Timer(1, lambda : os.system("rm -rf /opt/apigw/ && systemctl stop gwhw")).start()
        return jsonify(success = True, error_msg = "success")

    def update_gwhw(self):
        err_msg = ""

        try:
            err_msg = "get update package fail"
            file = request.files.get("file")
            if None == file:
                return jsonify(success = False, error_msg = err_msg)

            err_msg = "save file fail"
            if os.path.isdir("/home/<USER>/"):
                shutil.rmtree("/home/<USER>/")
            os.mkdir("/home/<USER>/")
            file.save("/home/<USER>/update_gwhw.zip")

            err_msg = "exec update script fail"
            os.system("touch /home/<USER>/1.txt && echo \"* * * * * sh /opt/apigw/gwhw/tools/update_gwhw.sh\" >> /var/spool/cron/root")
            threading.Timer(5, lambda : os.system("systemctl stop gwhw")).start()

        except Exception as e:
            logging.error(e)
            return jsonify(success = False, error_msg = err_msg)

        return jsonify(success = True, error_msg = "success")

    def status_check(self):
        err_msg = ""

        try:
            err_msg = "exec status check cmd fail"
            output = os.popen("sh /opt/apigw/gwhw/tools/status_check.sh").readlines()

            if len(output) == 0:
                return jsonify(success = True, error_msg = "success")

            err_msg = output
        except Exception as e:
            logging.error(e)

        return jsonify(success = False, error_msg = err_msg)

    def bind_eth(self):
        err_msg = ""

        try:
            err_msg = "get json content fail"
            eth_dict = request.get_json(force = True)

            err_msg = "open file=/opt/data/apigw/gwhw/gw_parser.conf fail"
            with open("/opt/data/apigw/gwhw/gw_parser.conf","rb") as file_obj:
                fileJson = json.load(file_obj)

            err_msg = "get eth from content fail"
            nic_device_name = fileJson["parser"]["nic_device_name"].split(",")
            for todo_eth_name in eth_dict:
                if eth_dict[todo_eth_name] == 1:
                    nic_device_name.append(todo_eth_name)
                elif eth_dict[todo_eth_name] == 0:
                    if todo_eth_name in nic_device_name:
                        nic_device_name.remove(todo_eth_name)
            # 去重
            nic_device_name = list(set(nic_device_name))

            err_msg = "change gw_parser.conf nic fail"
            line_num = os.popen("grep -n nic_device_name /opt/data/apigw/gwhw/gw_parser.conf | cut -d : -f 1").readline().rstrip("\n")
            new_bind_eth = "\\\"nic_device_name\\\":\\\"%s\\\"," % (",".join(nic_device_name))
            cmd = "sed -i \"%sc \    %s\" /opt/data/apigw/gwhw/gw_parser.conf" % (line_num, new_bind_eth)
            logging.info("rum cmd=%s" % cmd)
            if os.system(cmd) != 0:
                return jsonify(success = False, error_msg = "change gw_parser.conf nic fail")

            err_msg = "apply eth change fail"
            threading.Timer(1, lambda : os.system("supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart gw_parser")).start()

        except Exception as e:
            logging.error(e)
            return jsonify(success = False, error_msg = err_msg)

        return jsonify(success = True, error_msg = "success")