#!/usr/bin/python
#coding:utf-8


import logging
import os

from flask import jsonify
from app.app_function import Singleton
from app.app_eth import eth
from app.app_stats import stats

class health:
    __metaclass__ = Singleton
    
    def __init__(self):
        self.eth_obj = eth()
        self.stats_obj = stats()
    
    def api_health_information(self):
        health_information = {}
        health_information["action"] = None
        health_information["info"] = None
        
        last_stat_info = self.stats_obj.get_last_stat_info()
        if not last_stat_info:
            return jsonify(msg="监控信息获取失败", err=50001, data=health_information)
        
        http_flow_dict = {}
        http_flow_dict["timestamp"] = None

        http_session_qps = None
        upload_msg = None
        
        http_flow_dict["timestamp"] = last_stat_info["time_val"]
        http_session_qps = last_stat_info["parser_http_speed"]
        upload_msg = last_stat_info["up_succ_kafka_speed"]

        health_information["info"] = http_flow_dict

        json_info = self.eth_obj.api_eth_information(health=True)

        if json_info:
            bind_eth_information_list = json_info["bind"]
            all_no_flow_flag = True
            no_flow_list = list()
            for item in bind_eth_information_list:
                if item["flow"] == 0:
                    no_flow_list.append(item)
                else:
                    all_no_flow_flag = False
            
            if all_no_flow_flag:
                return jsonify(msg="所有网口都无流量", err=50002, data=health_information)
            
            if no_flow_list:
                nflist = list()
                for item in no_flow_list:
                    nflist.append(item["eth_name"])
                msg = "网口{" + ','.join(nflist) + "}无流量进入"
                return jsonify(msg=msg, err=50003, data=health_information)
        
        if http_session_qps < 0.01:
            return jsonify(msg="无http流量", err=50004, data=health_information)
        if upload_msg < 0.01:
            return jsonify(msg="无kafka流量", err=50005, data=health_information)
        
        return jsonify(msg="ok", err=0, data=health_information)
    