#!/usr/bin/python
# coding:utf-8

# gw_stat_srv.py
# 配置项服务
# python gw_stat_srv.py -b 0.0.0.0:9876 --reload --with-thread

import sys
import os
import logging
import time
import random


from flask import Flask
from flask_httpauth import HTT<PERSON>igestAuth
from imp import reload

from werkzeug.serving import run_simple

from app.app_license    import license
from app.app_stats  import stats
from app.app_task   import task
from app.app_settings   import settings
from app.app_eth import eth
from app.app_health import health

logging.basicConfig(format='%(asctime)s [%(levelname)s] : %(message)s', level=logging.INFO)

reload(sys)
sys.setdefaultencoding("utf-8")

app = Flask(__name__)
app.config['SECRET_KEY'] = '\xf1\xc1.V\xebB\xb6\x12\t\xc3y\x84A\xaaDj\xda\xf9\xba\xe8\xf7\xb5R-'
# 数字认证
# auth = HTTPBasicAuth()
auth = HTTPDigestAuth()

# 登录账号认证配置
users = {
    # "qzgw": "conf1711",
}



@auth.get_password
def get_pw(username):
    if username in users:
        return users.get(username)
    return None

@app.after_request
def add_header(response):
    response.headers['server'] = ''
    return response
    
app_stats = stats()
app_license = license()
app_task = task()
app_settings = settings()
app_eth = eth()
app_health = health()

# 获取基本监控信息
@app.route('/monitoring_info', methods=['GET'])
def gwhw_monitoring_info():
    return app_stats.gwhw_monitoring_info()

@app.after_request
def add_header(response):
    response.headers['server'] = ''
    return response

# 获取状态信息
# 读取 /opt/stats/目录下文件
# GET param: startTime
@app.route('/stats', methods=['GET'])
@auth.login_required
def api_get_stats():
    return app_stats.api_get_stats()

# 查看 捕获模式 capture_mode 原source_flag 
@app.route('/source_flag', methods=['GET', 'POST'])
def api_source_flag():
    return app_stats.api_source_flag()


# 获取最新一条状态信息
# 读取 /opt/stats/stats.file
@app.route('/last_stats', methods=['GET'])
@auth.login_required
def api_get_last_stats():
    return app_stats.api_get_last_stats()

# 获取错误日志
@app.route('/hw-stats/get_errlog', methods=['GET'])
def api_get_errlog():
    return app_stats.api_get_errlog()

# 获取加密流量信息
@app.route('/hw-stats/encrypted_traffic', methods=['GET'])
def api_get_encrypted_traffic():
    return app_stats.api_get_encrypted_traffic()

# 获取基本信息
@app.route('/hw-admin/local/getVersionConfig.do', methods=['GET', 'POST'])
# @auth.login_required
def api_get_basic_info():
    return app_license.api_get_basic_info()

# 获取授权信息
@app.route('/hw-admin/license/licenseDetail.do', methods=['GET', 'POST'])
# @auth.login_required
def api_get_license_info():
    return app_license.api_get_license_info()

# 获取机器码
@app.route('/hw-admin/license/machineCode.do', methods=['GET', 'POST'])
# @auth.login_required
def api_get_machineCode():
    return app_license.api_get_machineCode()

# 上传授权文件
@app.route('/hw-admin/license/uploadLicense.do', methods=['GET', 'POST'])
# @auth.login_required
def api_upload_license():
    return app_license.api_upload_license()

# 导入证书
@app.route('/hw-license/upload_pem', methods=['GET', 'POST'])
def api_upload_pem():
    return app_license.api_upload_pem()

# 删除证书
@app.route('/hw-license/delete_pem', methods=['POST'])
def api_delete_pem():
    return app_license.api_delete_pem()

@app.route('/task_status', methods=['GET'])
def api_risk_status():
    return app_task.api_risk_status()


# 数据出境 网关任务起停接口
@app.route('/task_start_stop', methods=['POST'])
@auth.login_required
def api_task_start_stop():
    return app_task.api_task_start_stop()


@app.route('/get_gwhw_status', methods=['GET', 'POST'])
@auth.login_required
def get_gwhw_status():
    return app_task.get_gwhw_status()

@app.route('/cache_clean', methods=['POST'])
@auth.login_required
def api_cache_clean():
    return app_task.api_cache_clean()

# settings 有关设置的两个接口
@app.route('/domain_to_ip', methods=['POST'])
@auth.login_required
def set_domain_to_ip():
    return app_settings.set_domain_to_ip()

@app.route('/drop_http_file_event', methods=['POST'])
@auth.login_required
def set_drop_http_file_event():
    return app_settings.set_drop_http_file_event()

# 设置http过滤的url
@app.route('/hw-filter/interactive_http_url_filter', methods=['POST'])
def set_interactive_http_url_filter():
    return app_settings.set_interactive_http_url_filter()

# 清空设置的过滤条件
@app.route('/hw-filter/clean_interactive_http_url_filter', methods=['GET'])
def set_clean_interactive_http_url_filter():
    return app_settings.set_clean_interactive_http_url_filter()

# 修改网关模式
@app.route('/hw-settings/toggle_mode', methods=['POST'])
def set_toggle_mode():
    return app_settings.set_toggle_mode()

# 获取网关模式
@app.route('/hw-eth/get_mode', methods=['GET'])
def api_get_mode():
    return app_eth.api_get_mode()

# 旁路阻断
@app.route('/gw-hw/controlStrategy', methods=['POST'])
@auth.login_required
def set_control_strategy():
    return app_settings.set_control_strategy()

# net_eth
@app.route('/eth/eth_information', methods=['GET'])
@auth.login_required
def api_eth_information():
    return app_eth.api_eth_information()

@app.route('/eth/eth_bind', methods=['POST'])
@auth.login_required
def api_eth_bind():
    return app_eth.api_eth_bind()

@app.route('/health/mdr', methods=['GET'])
@auth.login_required
def api_health_information():
    return app_health.api_health_information()

# 启用网关
@app.route('/gwhw/start_gwhw', methods=['POST'])
@auth.login_required
def start_gwhw():
    return app_settings.start_gwhw()

# 停止网关
@app.route('/gwhw/stop_gwhw', methods=['POST'])
@auth.login_required
def stop_gwhw():
    return app_settings.stop_gwhw()

# 重启网关
@app.route('/gwhw/restart_gwhw', methods=['POST'])
@auth.login_required
def restart_gwhw():
    return app_settings.restart_gwhw()

# 卸载网关
@app.route('/gwhw/uninstall_gwhw', methods=['POST'])
@auth.login_required
def uninstall_gwhw():
    return app_settings.uninstall_gwhw()

# 升级网关
@app.route('/gwhw/update_gwhw', methods=['POST'])
@auth.login_required
def update_gwhw():
    return app_settings.update_gwhw()

# 状态检查
@app.route('/gwhw/status_check', methods=['POST'])
@auth.login_required
def status_check():
    return app_settings.status_check()

@app.route('/gwhw/bind_eth', methods=['POST'])
@auth.login_required
def bind_eth():
    return app_settings.bind_eth()

# 从文件中加载配置
if os.path.exists("gw_stat_srv.conf"):
    try :
        app.config.from_json("gw_stat_srv.conf", silent=True)
        users.update(app.config.get('CONF_USERS', {}))
    except:
        print >> sys.stderr  # , traceback.format_exc()

# 从环境变量中加载配置
app.config.from_envvar('FLASKR_SETTINGS', silent=True)

application = app


def main():
    # in contrast to argparse, this works at least under Python < 2.7
    import optparse

    parser = optparse.OptionParser(
        usage='Usage: %prog [options] ')
    parser.add_option('-b', '--bind', dest='address',
                      help='The hostname:port the app should listen on.')
    parser.add_option('-d', '--debug', dest='use_debugger',
                      action='store_true', default=False,
                      help='Use Werkzeug\'s debugger.')
    parser.add_option('-r', '--reload', dest='use_reloader',
                      action='store_true', default=False,
                      help='Reload Python process if modules change.')
    parser.add_option('-t', '--with-threads', dest='use_threads',
                      action='store_true', default=False,
                      help='With multithreading.')
    options, args = parser.parse_args()

    # stat = False
    # for server_addr in NACOS_SERVER_ADDRS.split(","):
    #     try:
    #         addr = server_addr.strip().split(':')[0]
    #         port = server_addr.strip().split(':')[1]

    #         tn = telnetlib.Telnet(addr, int(port), timeout = 5)
    #         stat = True
    #         tn.close()
    #     except:
    #         stat = False
    #         break

    from gw_stat_nacos import stat_nacos
    
    gw_nacos = stat_nacos()
    
    gw_nacos.start()
    
    # sync_task()
    
    hostname, port = None, None
    if options.address:
        address = options.address.split(':')
        hostname = address[0]
        if len(address) > 1:
            port = address[1]
    run_simple(
        hostname=(hostname or '127.0.0.1'), port=int(port or 9876),
        application=application, use_reloader=options.use_reloader,
        use_debugger=options.use_debugger, threaded=options.use_threads
    )


if __name__ == '__main__':
    random.seed(time.time())
    main()
