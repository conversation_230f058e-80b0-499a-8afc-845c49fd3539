#!/bin/sh


function stop_gw()
{
  if [[ `ps -ef | grep gw_parser | grep -v grep | wc -l` -gt 0 ]];then
    sudo /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser 2>>/dev/null
  fi
}

function show_info()
{
  if [[ $# == 0 ]];then
    eth_10g=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10-Gigabit\|10G X550T" | grep " drv=ixgbe " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x520=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "Ethernet 10G" | grep "X520 Adapter" | grep " drv=ixgbe " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_350=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "I350 Gigabit" | grep " drv=igb " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_82580=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "82580 Gigabit" | grep " drv=igb " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x722_10g=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10GbE" | grep "Connection X722"  | grep " drv=i40e " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x722=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "1GbE" | grep "Connection X722"  | grep " drv=i40e " | sed "s,.* if=,," | sed "s, drv=.*,,")
  elif [[ $# == 1 ]]; then
    bus_info=$1
    eth_10g=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "10-Gigabit\|10G X550T" | grep " drv=ixgbe " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x520=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "Ethernet 10G" | grep "X520 Adapter" | grep " drv=ixgbe " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_350=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "I350 Gigabit" | grep " drv=igb " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_82580=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "82580 Gigabit" | grep " drv=igb " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x722_10g=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "10GbE" | grep "Connection X722"  | grep " drv=i40e " | sed "s,.* if=,," | sed "s, drv=.*,,")
    eth_x722=$(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "1GbE" | grep "Connection X722"  | grep " drv=i40e " | sed "s,.* if=,," | sed "s, drv=.*,,")
  fi

  for i in "${eth_10g}" "${eth_x520}" "${eth_x722_10g}" ;do
    if [[ -z "$i" ]];then
      continue
    fi

    value=`echo $i | tr "\n" " " | sed "s, $,,"`
    if [[ -z "${net_10g}" ]];then
      net_10g=$value
    else
      net_10g=${net_10g}" ${value}"
    fi
  done

  for i in "${eth_350}" "${eth_82580}" "${eth_x722}" ;do
    if [[ -z "$i" ]];then
      continue
    fi

    if [[ -z "$net_1g" ]];then
      net_1g=$i
    else
      net_1g=${net_1g}" ${i}"
    fi
  done

  eth_10g_arr=(${net_10g})
  eth_1g_arr=(${net_1g})

  for i in ${eth_10g_arr[@]} ; do
    if [[ -z "$i" ]];then
      continue
    fi

    if [[ `ip addr show ${i} | wc -l` -gt 0 ]]; then
      sudo ip link set ${i}  up  promisc on  mtu 9710
    fi
  done

  for i in ${net_1g[@]}
  do
    if [[ -z "$i" ]];then
      continue
    fi

    if [[ `ip addr show ${i} | wc -l` -gt 0 ]]; then
      sudo ip link set ${i}  up  promisc on  mtu 9216
    fi
  done

  sleep 5

  for i in ${eth_10g_arr[@]}
  do
    if [[ -z "$i" ]];then
      continue
    fi

    if [[ `ip addr show ${i} | wc -l` -gt 0 ]]; then
      ip addr show ${i}
    fi
  done

  for i in ${net_1g[@]}
  do
    if [[ -z "$i" ]];then
      continue
    fi

    if [[ `ip addr show ${i} | wc -l` -gt 0 ]]; then
      ip addr show ${i}
    fi
  done
}

function unbind_all()
{

  stop_gw
  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10-Gigabit\|10G X550T" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=ixgbe $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10-Gigabit\|10G X550T" | grep  " drv=igb_uio " | cut -f1 -d" ")
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind  --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "Ethernet 10G" | grep "X520 Adapter" | grep "drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind  --bind=ixgbe $(/opt/apigw/gwhw/tools/dpdk-devbind  --status  | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "Ethernet 10G" | grep "X520 Adapter" | grep  "drv=igb_uio " | cut -f1 -d" ")
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "I350 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "I350 Gigabit" | grep " drv=igb_uio " | cut -f1 -d" ")
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "I210 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "I210 Gigabit" | grep " drv=igb_uio " | cut -f1 -d" ")
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "82580 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "82580 Gigabit" | grep " drv=igb_uio " | cut -f1 -d" " )
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10GbE" | grep "Connection X722" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=i40e $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "10GbE" | grep "Connection X722" | grep " drv=igb_uio " | cut -f1 -d" " )
    sleep 1;
  fi

  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "1GbE" | grep "Connection X722" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=i40e $(/opt/apigw/gwhw/tools/dpdk-devbind --status | grep "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,")" | grep "1GbE" | grep "Connection X722" | grep " drv=igb_uio " | cut -f1 -d" " )
    sleep 1;
  fi

  show_info

  if [[ "$(lsmod | grep "^igb_uio" | wc -l )" -gt 0 ]]; then
    rmmod igb_uio
    rmmod uio
  fi
}

function unbind_one()
{
  bus_info=$1
  stop_gw
  if [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "10-Gigabit\|10G X550T" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=ixgbe $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind  --status | grep $bus_info | grep "Ethernet 10G" | grep "X520 Adapter" | grep "drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind  --bind=ixgbe $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "I350 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "I210 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "82580 Gigabit" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=igb $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "10GbE" | grep "Connection X722" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=i40e $bus_info
    echo -e "success\c"
  elif [[ `/opt/apigw/gwhw/tools/dpdk-devbind --status | grep $bus_info | grep "1GbE" | grep "Connection X722" | grep " drv=igb_uio " | wc -l` -gt 0 ]]; then
    sudo /opt/apigw/gwhw/tools/dpdk-devbind --bind=i40e $bus_info
    echo -e "success\c"
  else
    echo -e "fail\c"
  fi



}

if [[ $# -eq 0 ]];then
  unbind_all
elif [[ $# -eq 1 ]];then
  unbind_one $1
fi
