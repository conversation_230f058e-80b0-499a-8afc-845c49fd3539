#!/bin/sh







function version_gt() { test "$(echo "$@" | tr " " "\n" | sort -V | head -n 1)" != "$1"; }

function version_ge() { test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" == "$1"; }

function version_le() { test "$(echo "$@" | tr " " "\n" | sort -V | head -n 1)" == "$1"; }

function version_lt() { test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" != "$1"; }


DEST_PATH=/opt/apigw/gwhw/
KERNEL_VER_MIN="3.10.0-693"
KERNEL_VER_MAX="3.10.0-694"
KERNEL_VER_DEF="3.10.0-693.17.1"
BLACK_LIST_FILE='/opt/data/apigw/gwhw/dpdk_black_list.txt'
BLACK_LIST_STR=''
DPDK_ALLOC_GB_FILE="/opt/apigw/gwhw/tools/dpdk_alloc_GB"
DPDK_ALLOC_GB=$(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) / 2)

function get_ddpk_alloc_gb()
{
    if [[ -f $DPDK_ALLOC_GB_FILE ]];then
        DPDK_ALLOC_GB=`cat ${DPDK_ALLOC_GB_FILE}`
    fi
}

function init_black_list()
{
    BLACK_LIST_STR=''
    for str in `cat $BLACK_LIST_FILE | grep -v "^$\|^#"`
    do
        BLACK_LIST_STR=${BLACK_LIST_STR}"\\|$str"
    done
}

function kernel_check()
{
    if version_ge "$(uname -r | sed s,.el7.x86_64,,)" "${KERNEL_VER_MAX}"; then
        echo "Error: Kernel version is too high!"
        exit 3
    fi
    if version_lt "$(uname -r | sed s,.el7.x86_64,,)" "${KERNEL_VER_MIN}"; then
        echo "Error: Kernel version is too low!"
        exit 2
    fi



}

function bind_all()
{
    if [[ "$(lsmod | grep "^igb_uio" | wc -l )" -gt 0 ]]; then
        echo already mount igb_uio
        exit 0
    fi

    if [[ "$(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | wc -l)" -eq 0 ]]; then
        echo Intel network card not found
        exit 0
    fi


    sudo modprobe uio
    sudo insmod "${DEST_PATH}./kmod/"igb_uio.ko
    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "10-Gigabit\|10G X550T" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | wc -l` -gt 0 ]]; then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "10-Gigabit\|10G X550T" | grep  "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "Ethernet 10G" | grep "X520 Adapter" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | wc -l` -gt 0 ]]; then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "Ethernet 10G" | grep "X520 Adapter" | grep  "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "I350 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | wc -l` -gt 0 ]]; then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "I350 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "82580 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | wc -l` -gt 0 ]];then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "82580 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "10GbE" | grep "Connection X722" | grep "unused=igb_uio" | grep -v "*Active$BLACK_LIST_STR" | wc -l` -gt 0 ]];then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "10GbE" | grep "Connection X722" | grep "unused=igb_uio" | grep -v "*Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "1GbE" | grep "Connection X722" | grep "unused=igb_uio" | grep -v "*Active$BLACK_LIST_STR" | wc -l` -gt 0 ]];then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "1GbE" | grep "Connection X722" | grep "unused=igb_uio" | grep -v "*Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "I210 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | wc -l` -gt 0 ]]; then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $(python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $(/opt/apigw/gwhw/tools/lspci -d 8086::0200 | cut -f1 -d" " | sed "s,^,-e ,") | grep "I210 Gigabit" | grep "unused=igb_uio" | grep -v "Active$BLACK_LIST_STR" | cut -f1 -d" ")
    fi

    if [[ ! -d /mnt/huge ]]; then
        sudo mkdir -p /mnt/huge
    fi


    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/ ]]; then

        size=$(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) / 2)
        if [[ $DPDK_ALLOC_GB -gt $size ]];then
            alloc_size=$size
        else
            alloc_size=$DPDK_ALLOC_GB
        fi
        echo $alloc_size

        sudo su -c "echo $alloc_size > /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages"
    fi
    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-2048kB/ ]]; then
        if [[ `cat /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then

            sudo su -c "echo $(expr \( $(cat /proc/meminfo |grep 'MemTotal' |awk -F : '{print $2}' |sed 's/^[ \t]*//g' | cut -f1 -d\ ) / 1048576 + 1 \) \* 1024 / 2 / 2) > /sys/devices/system/node/node0/hugepages/hugepages-2048kB/nr_hugepages"
        fi
    fi

    if [[ -d /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/ ]]; then
        sudo su -c "echo 2 > /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/nr_hugepages"
    fi
    if [[ -d /sys/devices/system/node/node1/hugepages/hugepages-2048kB/ ]]; then
        if [[ `cat /sys/devices/system/node/node1/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then
            sudo su -c "echo 1024 > /sys/devices/system/node/node1/hugepages/hugepages-2048kB/nr_hugepages"
        fi
    fi

    for i in $(seq 2 8); do
        if [[ -d /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/ ]]; then
            sudo su -c "echo 2 > /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/nr_hugepages"
        fi
        if [[ -d /sys/devices/system/node/node${i}/hugepages/hugepages-2048kB/ ]]; then
            if [[ `cat /sys/devices/system/node/node${i}/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -eq 0 ]]; then
                sudo su -c "echo 1024 > /sys/devices/system/node/node${i}/hugepages/hugepages-2048kB/nr_hugepages"
            fi
        fi
    done


    if [[ "`mount | grep "/mnt/huge" | wc -l`" -gt 0 ]]; then

        sudo /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser
        sudo umount /mnt/huge
        sudo rm -rf /mnt/huge
        sudo mkdir -p /mnt/huge
    fi

    if [[ -d /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/ ]] && [[ `cat /sys/devices/system/node/node0/hugepages/hugepages-1048576kB/nr_hugepages 2>/dev/null || echo 0` -ne 0 ]]; then
        sudo mount -t hugetlbfs nodev /mnt/huge -o pagesize=1GB
    else
        sudo mount -t hugetlbfs nodev /mnt/huge -o pagesize=2MB
    fi
}

function bind_one()
{
    bus_info=$1
    kernel_check
    if [[ `python "${DEST_PATH}./tools/"dpdk-devbind --status | grep $bus_info | grep "unused=igb_uio" | grep -v "Active" | wc -l` -gt 0 ]]; then
        sudo python "${DEST_PATH}./tools/"dpdk-devbind --bind=igb_uio $bus_info
        echo -e "success\c"
    else
        echo -e "fail\c"
    fi
}


if [[ $# -eq 0 ]];then
    head -n0 $BLACK_LIST_FILE &>/dev/null && init_black_list
    get_ddpk_alloc_gb
    bind_all
else
    bind_one $1
fi
