#include "pfring.h"
#include "cJSON.h"
#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <vector>
#include <string>

using namespace std;

cJSON* initConfig(const char* fileName)
{
    FILE* fp = fopen(fileName, "rb");
    if (nullptr == fp)
    {
        printf("open config fail, file=%s, errno=%d, %s\n", fileName, errno, strerror(errno));
        return nullptr;
    }

    if (fseek(fp, 0, SEEK_END) < 0)
    {
        printf("fseek to end fail, file=%s, errno=%d, %s\n", fileName, errno, strerror(errno));
        return nullptr;
    }

    long size = ftell(fp);
    if (size < 0)
    {
        printf("ftell fail, file=%s, errno=%d, %s\n", fileName, errno, strerror(errno));
        return nullptr;
    }

    char* buf = (char *)malloc(sizeof(char) * (1 + size));
    if (nullptr == buf)
    {
        printf("malloc fail, file=%s\n", fileName);
        return nullptr;
    }

    if (fseek(fp, 0, SEEK_SET) < 0)
    {
        printf("fseek to begin fail, file=%s, errno=%d, %s\n", fileName, errno, strerror(errno));
        return nullptr;
    }

    if (fread(buf, size, 1, fp) != 1)
    {
        printf("get file data fail, file=%s\n", fileName);
        return nullptr;
    }
    buf[size] = '\0';
    fclose(fp);

    cJSON* configData = cJSON_Parse(buf);
    free(buf);

    return configData;
}

bool isMatchNode(const cJSON* node, const char* section)
{
    return cJSON_IsObject(node) == true &&
           nullptr != node->string &&
           strlen(node->string) == strlen(section) &&
           strcmp(node->string, section) == 0;
}

cJSON* getSectionNode(cJSON* node, const char* section)
{
    if (isMatchNode(node, section) == true) {return node;}

    cJSON* tmp = nullptr;
    if (nullptr != node->child)
    {
        if ((tmp = getSectionNode(node->child, section)) != nullptr)
        {
            return tmp;
        }
    }

    while ((node = node->next) != nullptr)
    {
        if (cJSON_IsObject(node) == false) {continue;}

        if ((tmp = getSectionNode(node, section)) != nullptr) {return tmp;}
    }

    return nullptr;
}

int main(int argc, char** argv)
{
    cJSON* configData = nullptr;

    if (argc > 1)
    {
        int opt = 0;
        while ((opt = getopt(argc, argv, "c:")) != -1)
        {
            switch (opt)
            {
            case 'c':
                configData = initConfig(optarg);
                if (nullptr == configData)
                {
                    printf("init config fail, file=%s\n", optarg);
                    return 1;
                }
                break;
            default:
                printf("opt=%d is unknown\n", opt);
                return 1;
            }
        }
    }
    else
    {
        printf("usage: %s -c config\n", argv[0]);
        return 1;
    }

    // 检查是否有nic_source.so
    cJSON* pluginNode = getSectionNode(configData, "plugin");
    if (nullptr == pluginNode)
    {
        printf("get plugin section node fail\n");
        return 1;
    }

    cJSON* loadFilesNode = cJSON_GetObjectItem(pluginNode, "load_files");
    if (nullptr == loadFilesNode)
    {
        printf("get load_files node fail\n");
        return 1;
    }

    if (cJSON_IsString(loadFilesNode) == false)
    {
        printf("load_files node is not string\n");
        return 1;
    }

    char buf[1024] = {0};
    strncpy(buf, loadFilesNode->valuestring, sizeof(buf) - 1);
    printf("get load_files=%s\n", buf);
    if (strlen(buf) == 0)
    {
        return 1;
    }

    if (strstr(buf, "nic_source.so") == NULL)
    {
        printf("do not have nic_source.so, do not check pfring\n");
        return 1;
    }

    // 获取抓包网口
    cJSON* parserNode = getSectionNode(configData, "parser");
    if (nullptr == parserNode)
    {
        printf("get parser section node fail\n");
        return 1;
    }

    cJSON* nicDeviceNameNode = cJSON_GetObjectItem(parserNode, "nic_device_name");
    if (nullptr == nicDeviceNameNode)
    {
        printf("get nic_device_name node fail\n");
        return 1;
    }

    if (cJSON_IsString(nicDeviceNameNode) == false)
    {
        printf("nic_device_name node is not string\n");
        return 1;
    }

    vector<string> tmpEth;
    strncpy(buf, nicDeviceNameNode->valuestring, sizeof(buf) - 1);
    printf("get nic_device_name=%s\n", buf);
    if (strlen(buf) == 0)
    {
        return 1;
    }


    // 整理网口
    const char* p = buf;
    const char* sign = nullptr;

    while ((sign = strchr(p, ',')) != nullptr)
    {
        tmpEth.push_back(string(p, sign - p));
        p = sign + 1;
    }
    tmpEth.push_back(p);

    vector<string> ethName;
    size_t i = 0;
    size_t j = 0;
    for (const string& eth : tmpEth)
    {
        // printf("before eth=|%s|\n", eth.c_str());
        for (i = 0, j = 0; i < eth.length(); i++)
        {
            if (' ' != eth[i])
            {
                buf[j] = eth[i];
                j++;
            }
        }
        buf[j] = '\0';
        // printf("after eth=|%s|\n", buf);

        ethName.push_back(buf);
    }

    // 检测网口
    for (const string& eth : ethName)
    {
        printf("check eth=%s\n", eth.c_str());
        if (pfring_open(eth.c_str(), 1500, PF_RING_PROMISC | PF_RING_DO_NOT_PARSE) == nullptr)
        {
            printf("open eth=%s fail\n", eth.c_str());
            if (system("sh /opt/apigw/gwhw/tools/load_driver.sh") < 0)
            {
                printf("load driver fail\n");
            }
            break;
        }
    }

    return 0;
}