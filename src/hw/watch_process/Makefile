CFLAGS = -std=c++11 -g3 -O0 -Wall -I ../gw_parser/utils/cjson/ \
						          -I /home/<USER>/src/PF_RING-8.0.0/kernel/ \
						          -I/home/<USER>/src/PF_RING-8.0.0/userland/lib \
						          -I/home/<USER>/src/PF_RING-8.0.0/userland/libpcap/ -o
LIB = -L /home/<USER>/src/PF_RING-8.0.0/userland/lib/ -lpfring  -L /home/<USER>/src/PF_RING-8.0.0/userland/libpcap/ -lpcap -pthread -ldl
CC = g++
SRC_DIR = ./watch_process.cpp ../gw_parser/utils/cjson/cJSON.c
PRG = watch_process

all:
	${CC} ${SRC_DIR} ${LIB} ${CFLAGS} ${PRG} 

clean:
	rm -rf watch_process
