#include <stdio.h>
#include <pthread.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <semaphore.h>
#include <stdint.h>
#include <signal.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>

#define MAX_THREAD_CNT                      1024                    /* 定义最大线程数量 */
#define VERSION_NUM                         2018
#define IPCKEY                              0x2000000               /* 自定义共享内存KEY */
#define DELAY_TIME                          5                       /* 休眠时间为5秒 */
#define PROCESS_PATH_LEN                    256
#define SEM_WRITE                           "sem_write"             /* 有名信号量，用于写 */
#define SEM_READ                            "sem_read"              /* 有名信号量，用于读 */
#define TIMEOUT_CNT                         10          
#define MONITORED_PROCESS_PATH              "/opt/apigw/gwhw/"      /* 被监控进程路径 */
#define MONITORED_PROCESS_NAME              "gw_parser"             /* 被监控进程名 */
#define MONITORED_PROCESS_CONF_FILE         "gw_parser.conf"        /* 被监控进程的配置文件名 */
#define SUPERVISOR_PROCESS_CONF_FILE        "supervisord_hw.conf"   /* supervisor服务配置文件 */
#define BUF_LEN                             256                     /* 命令字符串长度 */
#define MODE_FLAG                           S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH


typedef struct
{
    pthread_t pid;                  /* 线程ID */
    uint32_t u32_is_important;      /* 线程是否重要 0 表示不重要 1 表示重要 */    
    int i_reserver;                 /* 预留 */
    void *p_resrver;                /* 预留 */
}pthread_info_t;

typedef struct
{
    uint64_t u64_increase;               /* 用于确定线程是否live */
    pthread_info_t st_pthread_info;
}pthread_status_t;

typedef struct
{
    uint32_t u32_version;                            /* 版本号（与守护进程保持相同，可以用来确保信号源的正确性以及版本对应） */
    pid_t process_pid;                               /* 被监控进程的进程ID */
    uint32_t u32_pthread_num;                        /* 线程的数量 */
    pthread_status_t a_st_pthread_status[MAX_THREAD_CNT];
}pthread_watch_info_t;

/* 进程退出标志 */
uint32_t g_u32_quit_flag = 0;

pthread_watch_info_t g_st_watch_info;

uint32_t g_a_u32_thread_timeout[MAX_THREAD_CNT];

static void sig_handler(int i_sig_num)
{
    printf("Exiting on signal %d\r\n", i_sig_num);
    g_u32_quit_flag = 1;
}

/* 创建共享内存并绑定到进程空间里，成功返回0，否则返回-1 */
static int create_shm(int *p_i_shmid, pthread_watch_info_t **pp_st_watch_info)
{
    int i_shmid = 0;
    pthread_watch_info_t *p_st_watch_info = NULL;

    /* 保证是自己创建的共享内存 */
    i_shmid = shmget(IPCKEY
                   , sizeof(pthread_watch_info_t)
                   , IPC_CREAT | IPC_EXCL | MODE_FLAG);
    if (i_shmid == -1)
    {
        /* 检测到已经创建了共享内存 */
        if (errno == EEXIST)
        {
            printf ("shm is exist\r\n");
            /* 获取i_shmid，然后将其杀死 */
            i_shmid = shmget(IPCKEY, sizeof(pthread_watch_info_t), IPC_CREAT | MODE_FLAG);
            shmctl(i_shmid, IPC_RMID, 0);
        }
        return -1;
    }

    p_st_watch_info = (pthread_watch_info_t*)shmat(i_shmid, NULL, 0);
    if (p_st_watch_info == (void*)-1)
    {
        printf("shmmat failed, errno = %d\r\n", errno);
        /* 将创建的共享内存绑定到自己的进程空间失败，则删除创建的共享空间 */
        shmctl(i_shmid, IPC_RMID, 0);
    }

    *p_i_shmid = i_shmid;
    *pp_st_watch_info = p_st_watch_info;
    return 0;
}

/* 创建有名信号量，成功返回0，失败返回-1 */
/* i_flag == 0, 创建读信号量， i_flag== 1，创建写信号量 */
static int create_sem(sem_t **pp_sem_param, int i_flag)
{
    sem_t *p_sem_arg = NULL;
    if (i_flag == 0)
    {
        p_sem_arg = sem_open(SEM_READ, O_CREAT | O_EXCL, MODE_FLAG, 0);
        if (p_sem_arg == SEM_FAILED)
        {
            if (errno == EEXIST)
            {
                printf("sem read is exist\r\n");
                sem_unlink(SEM_READ);
            }
            return -1;
        }
    }
    else if (i_flag == 1)
    {
        p_sem_arg = sem_open(SEM_WRITE, O_CREAT | O_EXCL, MODE_FLAG, 1);
        if (p_sem_arg == SEM_FAILED)
        {
            if (errno == EEXIST)
            {
                printf("sem is exist\r\n");
                sem_unlink(SEM_WRITE);
            }
            return -1;
        }
    }
    
    *pp_sem_param = p_sem_arg;
    return 0;
}

/* 判断进程是否存在 */
int is_process_exist(char *p_cmd)
{
    if (NULL == p_cmd)
    {
        printf("cmd buf is null\r\n");
        return -2;
    }

    FILE *p_file = NULL;
    char a_buf[BUF_LEN];
    memset(a_buf, 0, sizeof(a_buf));
    int i_process_cnt = 0;
    p_file = popen(p_cmd, "r");
    if (p_file == NULL)
    {
        printf("popen failed(%s)\r\n", p_cmd);
        return -3;
    }

    fgets(a_buf, BUF_LEN, p_file);
    pclose(p_file);
    p_file = NULL;
    i_process_cnt = atoi(a_buf);
    if(i_process_cnt == 0)
    {
        return -1;
    }
   
    return 0;
}

static void check_watch_service_status()
{
    int i_ret = 0;
    char a_cmd[BUF_LEN];
    memset(a_cmd, 0, sizeof(a_cmd));
    char a_buf[BUF_LEN];
    memset(a_buf, 0, sizeof(a_buf));
    char *p_sub_str = NULL;
    FILE *p_file = NULL;
    sprintf(a_cmd, "service gwhw status | grep \"Active\" ");

    p_file = popen(a_cmd, "r");
    if (p_file == NULL)
    {
        printf("popen gwhw failed\r\n");
        return ;
    }
    fread(a_buf, sizeof(a_buf), 1, p_file);
    /* test */
    printf("a_buf = %s\r\n", a_buf);

    p_sub_str = strtok(a_buf, "(");
    if (p_sub_str == NULL)   /* 认为gwhw服务没有起来，开启gwhw服务 */
    {
        printf("gwhw service status is stop\r\n");
        sprintf(a_cmd, "service gwhw start");
        system(a_cmd);
    }
    else
    {
        p_sub_str = strtok(NULL, ")");
        if (p_sub_str != NULL)
        {
            if (strcmp("running", p_sub_str) == 0)
            {
                printf("gwhw service status is running\r\n");
            }
            else
            {
                printf("gwhw service status is %s\r\n", p_sub_str);
                /* gwhw服务不是running状态，需要重启gwhw服务 */
                sprintf(a_cmd, "service gwhw restart");
                system(a_cmd);
            }    
        }
        else
        {
            printf("service gwhw is error");
        }
    }
    pclose(p_file);
    p_file = NULL;
    sleep(1);
   
    /* 判断supervisor进程是否存在 */
    sprintf(a_cmd, "ps -ef | grep supervisor | grep -v grep | wc -l");
    i_ret = is_process_exist(a_cmd);
    if (i_ret < 0)
    {
        if (i_ret == -1)
        {
            printf("supervisor process is not exist\r\n");
            sprintf(a_cmd, "supervisor -c %s%s", MONITORED_PROCESS_PATH, SUPERVISOR_PROCESS_CONF_FILE);
            system(a_cmd);
        }
        else
        {
            return;
        }
    }
    sleep(1);
    
    /* 检查gw_parser */
    sprintf(a_cmd, "ps -ef | grep %s | grep -v grep | wc -l", MONITORED_PROCESS_NAME);
    i_ret = is_process_exist(a_cmd);
    if (i_ret < 0)
    {
        if (i_ret == -1)
        {
            printf("gw_parser process is not exist\r\n");
            sprintf(a_cmd,  "%s./%s -c %s%s &", MONITORED_PROCESS_PATH
                                              , MONITORED_PROCESS_NAME
                                              , MONITORED_PROCESS_PATH
                                              , MONITORED_PROCESS_CONF_FILE);
            system(a_cmd);
        }
        else
        {
            return ;
        }
    }

    return ;
}   

static void kill_restated_Monitored_process()
{
    int i_ret = 0;
    char a_buf[PROCESS_PATH_LEN];
    memset(a_buf, 0, sizeof(a_buf));
    
    sprintf(a_buf, "kill -9 %d", g_st_watch_info.process_pid);
    printf("%s\r\n", a_buf);
    i_ret = system(a_buf);
    if (i_ret != 0)
    {
        return ;
    }
    sleep(DELAY_TIME);

    /* 判断gwhw服务是否存在，判断supervisord服务是否存在 */
    check_watch_service_status();

    return ;
}

void *pthread_get_watch_info(void *p_args)
{
    int i_ret = 0;
    int i_creat_shm_succ_flag = 0;
    int i_creat_sem_read_succ_flag = 0;
    int i_creat_sem_write_succ_flag = 0;
    struct timespec st_spec_timeout;
    memset(&st_spec_timeout, 0, sizeof(struct timespec));
    int i_shmid = -1;
    pthread_watch_info_t *p_st_watch_info = NULL;
    sem_t *p_sem_read = NULL;
    sem_t *p_sem_write = NULL;
    int i = 0;

    /* 创建共享内存并绑定共享内存几有名信号量 */
    while (!g_u32_quit_flag)
    {
        if (!i_creat_shm_succ_flag)
        {
            i_ret = create_shm(&i_shmid, &p_st_watch_info);
            if (i_ret == 0)
            {
                i_creat_shm_succ_flag = 1;
                printf("create shm succ\r\n");
            }
        }

        if (!i_creat_sem_read_succ_flag)
        {
            i_ret = create_sem(&p_sem_read, 0);
            if (i_ret == 0)
            {
                i_creat_sem_read_succ_flag = 1;
                printf("create sem read succ\r\n");
            }
        }

        if (!i_creat_sem_write_succ_flag)
        {
            i_ret = create_sem(&p_sem_write, 1);
            if (i_ret == 0)
            {
                i_creat_sem_write_succ_flag = 1;
                printf("create sem write succ\r\n");
            }
        }

        if (i_creat_shm_succ_flag && i_creat_sem_read_succ_flag && i_creat_sem_write_succ_flag)
        {
            printf("shm creat succ, i_shmid = %d, watch info addr = %p\r\n", i_shmid, p_st_watch_info);
            printf("sem read and sem write create succ\r\n");
            break;
        }
        sleep(DELAY_TIME);
    }

    int timeout_cnt = 0;
    while (!g_u32_quit_flag)
    {
        sleep(DELAY_TIME);
        st_spec_timeout.tv_sec = DELAY_TIME;
        st_spec_timeout.tv_nsec = 0;
        i_ret = sem_timedwait(p_sem_read, &st_spec_timeout);
        if (i_ret != 0)
        {
            do
            {
                if (errno != ETIMEDOUT){
                    break;
                }

                timeout_cnt += 1;
                if (timeout_cnt < 100)
                {
                    break;
                }

                printf("sem timedwait time out %d times\r\n", timeout_cnt);
                timeout_cnt = 0;

                /* code */
            } while ( 0/* condition */);
            
            continue;
        }

        timeout_cnt = 0;
        /* 判断version是否对应 */
        if (p_st_watch_info->u32_version != g_st_watch_info.u32_version)
        {
            printf("version is not match!(watch info version = %u), (process version = %u)\r\n", p_st_watch_info->u32_version
                                                                                             , g_st_watch_info.u32_version);
            break;
        }
        /* test */
        printf("version = %u\r\n", p_st_watch_info->u32_version);
        printf("thread num = %u\r\n", p_st_watch_info->u32_pthread_num);
        for (i = 0; i < p_st_watch_info->u32_pthread_num; ++i)
        {
            if (i >= MAX_THREAD_CNT)
            {
                printf("array out of bouds(%d)\r\n", i);
                break;
            }

            /* 判断对应线程的状态是否更新 */
            if (p_st_watch_info->a_st_pthread_status[i].u64_increase == g_st_watch_info.a_st_pthread_status[i].u64_increase)
            {
                printf("index %d thread info is not update\r\n", i);
                g_a_u32_thread_timeout[i]++;
                if (g_a_u32_thread_timeout[i] >= TIMEOUT_CNT) /* 判断是否已经超过TIMEOUT_CNT次没有更新状态 */
                {
                    /* test */
                    printf("index = %d, timeout cnt = %u, important = %u\r\n", i, g_a_u32_thread_timeout[i],
                                p_st_watch_info->a_st_pthread_status[i].st_pthread_info.u32_is_important);
                    if (p_st_watch_info->a_st_pthread_status[i].st_pthread_info.u32_is_important == 1)
                    {
                        /* 杀死进程，重启进程 */
                        kill_restated_Monitored_process();
                        //sem_post(p_sem_write);
                        memset(&g_st_watch_info, 0, sizeof(g_st_watch_info));
                        memset(&g_a_u32_thread_timeout, 0, sizeof(g_a_u32_thread_timeout));
                        break;
                    } 
                }
            }
            else
            {
                g_a_u32_thread_timeout[i] = 0;
            }
        }

        /* 更新状态 */
        memcpy(&g_st_watch_info, p_st_watch_info, sizeof(pthread_watch_info_t));

        sem_post(p_sem_write);
    }

    /* 删除读写信号量 */
    if (p_sem_write)
    {
        i_ret = sem_close(p_sem_write);
        if (i_ret != 0)
        {
            printf("close write sem failed (%d, errno = %d)\r\n", i_ret, errno);
        }
        i_ret = sem_unlink(SEM_WRITE);
        if (i_ret != 0)
        {
            printf("unlink write sem failed (%d, errno = %d)\r\n", i_ret, errno);
        }
        printf("close sem write succ\r\n");
    }
    if (p_sem_read)
    {
        i_ret = sem_close(p_sem_read);
        if (i_ret != 0)
        {
            printf("close read sem failed(%d, errno = %d)\r\n", i_ret, errno);
        }
        i_ret = sem_unlink(SEM_READ);
        if (i_ret != 0)
        {
            printf("unlink read sem failed(%d, errno = %d)\r\n", i_ret, errno);
        }
        printf("close sem read succ\r\n");
    }

    /* 共享内存以及信号量的释放 */
    if (p_st_watch_info)
    {
        i_ret = shmdt(p_st_watch_info);
        if (i_ret != 0)
        {
            printf("shmdt failed(%d, errno = %d)\r\n", i_ret, errno);
        }
        printf("shmdt succ\r\n");
    }

    if (i_shmid != -1)
    {
        i_ret = shmctl(i_shmid, IPC_RMID, NULL);
        if (i_ret != 0)
        {
            printf("shmctl failed(%d, errno = %d)\r\n", i_ret, errno);
        }
        printf("shctl succ\r\n");
    }

    pthread_exit(NULL);
}

int main(int argc, char **argv)
{
    int i_ret = 0;

    /* 注册信号处理函数，捕捉ctrl-c, pkill信号,退出程序 */
    signal(SIGINT, sig_handler);
    signal(SIGTERM, sig_handler);
    memset(&g_st_watch_info, 0, sizeof(pthread_watch_info_t));
    g_st_watch_info.u32_version = VERSION_NUM;
    memset(&g_a_u32_thread_timeout, 0, sizeof(g_a_u32_thread_timeout));

    pthread_t pthread_key;
    i_ret = pthread_create(&pthread_key, NULL, pthread_get_watch_info, NULL);
    if (i_ret != 0)
    {
        printf("ceate pthread_get_watch_info pthread failed(%d)\r\n", i_ret);
        return -1;
    }

    pthread_join(pthread_key, NULL);

    return 0;
}
