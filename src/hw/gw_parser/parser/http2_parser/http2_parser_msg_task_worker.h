/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __HTTP_PARSER_MSG_TASK_WORKER_H__
#define __HTTP_PARSER_MSG_TASK_WORKER_H__

#include "http2_parser_task_worker.h"

struct http2_version {
  unsigned short major;
  unsigned short minor;
};

typedef struct bstring
{
  size_t length;
  const char *bstr;
} bstring_t;

// HTTP2解析 单边原始数据
typedef struct
{
  bstring_t field;
  bstring_t value;
} http2_header_item_t;

typedef struct http2_parser_half_msg
{
  size_t mem_size;
  int is_chunked;
  uint64_t content_length;

  bstring_t header;
  bstring_t body;
  ssize_t header_item_num;
  http2_header_item_t *header_item;

} http2_parser_half_msg_t;

typedef struct http2_header_info
{
  char *p_req_url;
  char a_rsp_content_type[1024];
  char a_rsp_disposition[1024];
  char a_rsp_content_range[1024];
}http2_header_info_t;

typedef struct http2_parser_ext_data
{
  // for response
  int found_gzip; // = 0
  int gzip_deep;  // =1
  char *pstr;     // = NULL;
  size_t length;  //

} http2_parser_ext_data_t;

// http2解析数据结构
typedef struct http2_parser_msg
{
  TaskWorkerData twd;
  size_t mem_size;
  
  http2_parser_half_msg_t req_msg;
  http2_parser_half_msg_t rsp_msg;

  ConnData addr;
  double pcap_ts;

  bstring_t req_hdr_cookie;
  bstring_t req_hdr_content_type;
  unsigned int method;

  struct http2_version http_req_ver;
  struct http2_version http_rsp_ver;

  unsigned int status_code;
  bstring_t rsp_hdr_content_encoding;
  bstring_t rsp_hdr_content_type;
  bstring_t rsp_hdr_content_disposition;
  bstring_t rsp_hdr_content_range;
  uint64_t rsp_content_length;

  char full_uri[1024];
  char host[512];
  char pcap_filename[256];

  int unkown_rule;

  int req_error_code;
  int rsp_error_code;

  unsigned int req_seq;
  unsigned int req_ack;

  unsigned int rsp_seq;
  unsigned int rsp_ack;

  int is_file_event;

} http2_parser_msg_t;

class CTaskWorkerMsg : public CTaskWorkerHttp2
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
};

#endif // __HTTP_PARSER_MSG_TASK_WORKER_H__
