#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "http2_parser.h"
#include "gw_logger.h"
#include "cJSON.h"
#include "simple_json.h"
#include "gw_i_upload.h"
#include "gw_common.h"
#include "utils.h"
#include "get_file_type.h"
#include "file_info_deal.h"
#include "minio_upload.h"
#include "ProtobufRawHttpEvent.pb.h"

using namespace com::quanzhi::audit_core::common::model;

#define IF_FREE(x) if ( NULL != (x) ) { cJSON_free(x); }
#define MALLOC_HEADER_LEN (10240)
#define MALLOC_SET_COOKIE_LEN (8192)

#ifndef CRLF
    #define CRLF 0x0d0a
#endif


const char *fast_template_with_flag="{\"url\":%s,\"dst_ip\":\"%s\",\"content_type\":%s,\"tm\":%d,\"analyze_flag\":%d}";
const char *fast_template="{\"url\":%s,\"dst_ip\":\"%s\",\"content_type\":%s,\"tm\":%d}";

static const char *g_p_req_method = "{\"req\":{\"method\":\"";
static const size_t g_req_method_len = 18;

static const char *g_p_req_http_version = "\",\"http_version\":\"";
static const size_t g_req_http_version_len = strlen(g_p_req_http_version);

static const char *g_p_req_remote_addr = "\",\"remote_addr\":\"";
static const size_t g_req_remote_addr_len = 17;

static const char *g_p_req_err_code =  "\",\"err_code\":";
static const size_t g_req_err_code_len = 13;

static const char *g_p_req_url = ",\"url\":";
static const size_t g_req_url_len = 7;

static const char *g_p_req_body = ",\"body\":";
static const size_t g_req_body_len = 8;

static const char *g_p_req_header =  ",\"header\":{";
static const size_t g_req_header_len = 11;

static const char *g_p_rsp_status = "}},\"rsp\":{\"status\":";
static const size_t g_rsp_status_len = 19;

static const char *g_p_rsp_http_version = ",\"http_version\":\"";
static const size_t g_rsp_http_version_len = strlen(g_p_rsp_http_version);

static const char *g_rsp_err_code = "\",\"err_code\":";
static const size_t g_rsp_err_code_len = 13;

static const char *g_p_rsp_header = ",\"header\":{";
static const size_t g_rsp_header_len = 11;

static const char *g_p_set_cookies_list =  "},\"set_cookies_list\":";
static const size_t g_set_cookies_list_len = 21;

static const char *g_p_rsp_body = ",\"body\":";
static const size_t g_rsp_body_len = 8;

static const char *g_p_rsp_body_md5 = ",\"bodyMd5\":\"";
static const size_t g_p_rsp_body_md5_len = strlen(g_p_rsp_body_md5);

static const char *g_p_meta_tm = "\"},\"meta\":{\"tm\":";
static const size_t g_meta_tm_len = strlen(g_p_meta_tm);

static const char *g_p_meta_req_seq = ",\"req_seq\":";
static const size_t g_meta_req_seq_len = 11;

static const char *g_p_meta_req_ack = ",\"req_ack\":";
static const size_t g_meta_req_ack_len = 11;

static const char *g_p_meta_rsp_req = ",\"rsp_seq\":";
static const size_t g_meta_rsp_req_len = 11;

static const char *g_p_meta_rsp_ack = ",\"rsp_ack\":";
static const size_t g_meta_rsp_ack_len = 11;

static const char *g_p_net_src_ip = "},\"net\":{\"src_ip\":\"";
static const size_t g_net_src_ip_len = 19;

static const char *g_p_net_src_port = "\",\"src_port\":";
static const size_t g_net_src_port_len = 13;

static const char *g_p_net_dst_ip = ",\"dst_ip\":\"";
static const size_t g_net_dst_ip_len = 11;

static const char *g_p_net_dst_port = "\",\"dst_port\":";
static const size_t g_net_dst_port_len = 13;

static const char *g_p_pcap_filename = ",\"pcap_filename\":\"";
static const size_t g_pcap_filename_len = 18;

static const char *g_p_unique_id = "\"},\"unique_id\":{\"event_id\":\"";
static const size_t g_unique_id_len = 28;

static const char *g_p_file_dir =  "\"},\"file\":{\"file_direction\":\"";
static const size_t g_file_dir_len = 29;

static const char *g_p_file_type = "\",\"file_type\":";
static const size_t g_file_type_len = strlen(g_p_file_type);

static const char *g_p_file_name = ",\"file_name\":";
static const size_t g_file_name_len = strlen(g_p_file_name);

static const char *g_p_file_len =  ",\"file_len\":";
static const size_t g_file_len_len = strlen(g_p_file_len);

static const char *g_p_file_real_len = ",\"file_real_len\":";
static const size_t g_file_real_len_len = strlen(g_p_file_real_len);

static const char *g_p_file_warn = ",\"file_warn\":";
static const size_t g_file_warn_len = strlen(g_p_file_warn);

static const char *g_p_file_data = ",\"file_data\":";
static const size_t g_file_data_len = strlen(g_p_file_data);

static const char *g_p_is_incomplete = ",\"is_incomplete\":";
static const size_t g_is_incomplete_len = strlen(g_p_is_incomplete);

static const char *g_p_rw_flag = ",\"rw_flag\":";
static const size_t g_rw_flag_len = strlen(g_p_rw_flag);

static const char* g_p_upload_flag = ",\"upload_flag\":";
static const size_t g_upload_flag_len = strlen(g_p_upload_flag);

static const char *g_p_upload_dir = ",\"upload_dir\":";
static const size_t g_upload_dir_len = strlen(g_p_upload_dir);

static const char *g_p_md5sum = ",\"md5sum\":";
static const size_t g_md5sum_len = strlen(g_p_md5sum);

static const char *g_p_sha256 = ",\"sha256\":";
static const size_t g_sha256_len = strlen(g_p_sha256);

static const char *g_p_file_download = ",\"file_download\":";
static const size_t g_file_download_len = strlen(g_p_file_download);

static const char *g_p_file_type_reliable = ",\"file_type_reliable\":";
static const size_t g_file_type_reliable_len = strlen(g_p_file_type_reliable); 

static const char *g_p_analyse_flag =  "},\"analyze_flag\":";
static const size_t g_analyse_flag_len = 17;

/** new event format **/
static const char* p_event_header_new = "HTTP_EVENT 1.0";
static const size_t event_header_new_len = strlen(p_event_header_new);

static const char* p_s_ip_new = "S_IP";
static const size_t s_ip_new_len = strlen(p_s_ip_new);

static const char* p_d_ip_new = "D_IP";
static const size_t d_ip_new_len = strlen(p_d_ip_new);

static const char* p_event_id_new = "ID";
static const size_t event_id_new_len = strlen(p_event_id_new);

static const char* p_time_new = "TM";
static const size_t time_new_len = strlen(p_time_new);

static const char* p_file_new = "FILE";
static const size_t file_new_len = strlen(p_file_new);

static const char* p_error_code_new = "ERROR_CODE";
static const size_t error_code_new_len = strlen(p_error_code_new);

static const char* p_method_new = "METHOD";
static const size_t method_new_len = strlen(p_method_new);

static const char* p_url_new = "URL";
static const size_t url_new_len = strlen(p_url_new);

static const char* p_status_new = "STATUS";
static const size_t status_new_len = strlen(p_status_new);

static const char* p_set_cookie_list_new = "SET_COOKIES";
static const size_t set_cookie_list_new_len = strlen(p_set_cookie_list_new);

static const char* p_req_h_new = "REQ_H";
static const size_t req_h_new_len = strlen(p_req_h_new);

static const char* p_rsp_h_new = "RSP_H";
static const size_t rsp_h_new_len = strlen(p_rsp_h_new);

static const char* p_req_b_new = "REQ_B";
static const size_t req_b_new_len = strlen(p_req_b_new);

static const char *p_rsp_b_new = "RSP_B";
static const size_t rsp_b_new_len  = strlen(p_rsp_b_new);

static const char *p_complete_new = "COMPLETE";
static const size_t complete_new_len = strlen(p_complete_new);

static const char *p_incomplete_new = "IN_COMPLETE";
static const size_t incomplete_new_len = strlen(p_incomplete_new);

static const char *p_type_reliable_new = "TYPE_RELIABLE";
static const size_t type_reliable_new_len = strlen(p_type_reliable_new);

static const char *p_type_unreliable_new = "TYPE_UNRELIABLE";
static const size_t type_unreliable_new_len = strlen(p_type_unreliable_new);

static const char *p_end_new = "$END$";
static const size_t end_new_len = strlen(p_end_new);

static const char *g_p_null_content = "$NULL$";
static const size_t g_null_content_len = strlen(g_p_null_content);

typedef struct 
{
    char *p_str;
    size_t str_len;
}st_str_info_t;

static char *strlwr(char *str)
{
  if (str == NULL)
    return NULL;

  char *p = str;
  while (*p != '\0')
  {
    if (*p >= 'A' && *p <= 'Z')
      *p = (*p) + 0x20;
    p++;
  }
  return str;
}

static size_t format_bstr_info(bstr_t *p_bstr, st_str_info_t *p_st_value)
{
    // if (0 == p_bstr->length)
    // {
    //     return 0;
    // }
    
    p_st_value->p_str = cJSON_EscapeStringWithBufferSize(p_bstr->p_value, p_bstr->length, &(p_st_value->str_len));
    return p_st_value->str_len;
}

static size_t format_header(keyvalue_info_t *p_header, int header_num, st_str_info_t *p_st_header)
{   
    int i = 0;
    size_t ret_len = 0;
    char *p_header_str = NULL;
    p_header_str = (char *)malloc(MALLOC_HEADER_LEN);
    if (NULL == p_header_str)
    {
        p_st_header->str_len = 4; // "null"
        return 0;    
    }
    memset(p_header_str, 0, MALLOC_HEADER_LEN);
    char *p_tmp = p_header_str;

    size_t header_str_len = MALLOC_HEADER_LEN;
    size_t free_header_len = MALLOC_HEADER_LEN;
    char field[256];
    for (i = 0; i < header_num; i++)
    {
        size_t cp_len = MIN(p_header[i].key.length, 256 - 1);
        memcpy(field, p_header[i].key.p_value, cp_len);
        field[cp_len] = '\0';

        //char *p_header_value = NULL;
        st_str_info_t st_header_value = {0};
        ret_len = format_bstr_info(&(p_header[i].value), &st_header_value);

        // if (free_header_len < (p_header[i].key.length + ret_len + 4 /* \"\": */))
        if (free_header_len < (cp_len + ret_len + 5)) /* \"\":  , */
        {
            p_header_str = (char *)realloc(p_header_str, header_str_len + MALLOC_HEADER_LEN);
            memset(p_header_str + header_str_len, 0, MALLOC_HEADER_LEN);
            p_tmp = p_header_str + (header_str_len - free_header_len);
            header_str_len += MALLOC_HEADER_LEN;
            free_header_len += MALLOC_HEADER_LEN;
        }

        *p_tmp = '\"';
        p_tmp ++;
        //memcpy(p_tmp, strlwr(field), p_header[i].key.length);
        //p_tmp += p_header[i].key.length;
        memcpy(p_tmp, strlwr(field), cp_len);
        p_tmp += cp_len;
        memcpy(p_tmp, "\":", 2);
        p_tmp += 2;
        memcpy(p_tmp, st_header_value.p_str, ret_len);
        p_tmp += ret_len;

        free_header_len -= (cp_len + ret_len + 3);
        
        IF_FREE(st_header_value.p_str);
        if (i != header_num - 1)
        {
            *p_tmp = ',';
            p_tmp++;
            free_header_len -= 1;
        }
    }

    p_st_header->p_str = p_header_str;
    p_st_header->str_len =  strlen(p_header_str);
    return p_st_header->str_len;
}

static size_t format_set_cookie(bstr_t *set_cookies_list, int cookies_list_num, st_str_info_t *p_st_set_cookie_list)
{
    int i = 0; 
    size_t ret_len = 0;
    char *p_set_cookie_list = NULL;
    p_set_cookie_list = (char *)malloc(MALLOC_SET_COOKIE_LEN);
    size_t cookie_list_len = MALLOC_SET_COOKIE_LEN;
    size_t free_cookie_list_len = MALLOC_SET_COOKIE_LEN;
    if (p_set_cookie_list == NULL)
    {
        return 0;
    }
    memset(p_set_cookie_list, 0, MALLOC_SET_COOKIE_LEN);
    char *p_tmp = p_set_cookie_list; 

    *p_tmp = '[';
    free_cookie_list_len -= 1;
    p_tmp++;
    for (i = 0; i < cookies_list_num; i++)
    {
        //char *p_set_cookie = NULL;
        st_str_info_t st_set_cookie = {0};
        ret_len = format_bstr_info(&(set_cookies_list[i]), &st_set_cookie);

        if (free_cookie_list_len < ret_len + 3)
        {
            p_set_cookie_list = (char *)realloc(p_set_cookie_list, cookie_list_len + MALLOC_SET_COOKIE_LEN);
            memset(p_set_cookie_list + cookie_list_len, 0, MALLOC_SET_COOKIE_LEN);
            p_tmp = p_set_cookie_list + (cookie_list_len - free_cookie_list_len);
            cookie_list_len += MALLOC_SET_COOKIE_LEN;
            free_cookie_list_len += MALLOC_SET_COOKIE_LEN;
        }

        memcpy(p_tmp, st_set_cookie.p_str, ret_len);
        IF_FREE(st_set_cookie.p_str);
        p_tmp += ret_len;
        free_cookie_list_len -= ret_len;
        
        if (i != cookies_list_num - 1)
        {
            *p_tmp = ',';
            p_tmp++;
            free_cookie_list_len -= 1;
        }
    }

    *p_tmp = ']';
    p_st_set_cookie_list->p_str = p_set_cookie_list;
    p_st_set_cookie_list->str_len = strlen(p_set_cookie_list);
    return p_st_set_cookie_list->str_len;
}

static size_t calculate_req_len(http_req_info_t *p_req_info, st_str_info_t *p_st_url, st_str_info_t *p_st_req_body, st_str_info_t *p_st_req_header)
{
    size_t len = 0;
    size_t ret_len = 0;

    if ( NULL != p_req_info->full_url ) 
    {        
        p_st_url->p_str = cJSON_EscapeString(p_req_info->full_url);
        p_st_url->str_len += strlen(p_st_url->p_str);
    }
    len += p_st_url->str_len;

    ret_len = format_bstr_info(&(p_req_info->body), p_st_req_body);
    len += ret_len;

    ret_len = format_header(p_req_info->p_req_header, p_req_info->req_header_num, p_st_req_header);
    len += ret_len;

    return len;
}

static size_t calculate_rsp_len(http_rsp_info_t *p_rsp_info, st_str_info_t *p_st_rsp_body, st_str_info_t *p_st_rsp_header, st_str_info_t *p_st_set_cookie_list)
{
    size_t len = 0; 
    size_t ret_len = 0;

    ret_len = format_header(p_rsp_info->p_rsp_header, p_rsp_info->rsp_header_num, p_st_rsp_header);
    len += ret_len;

    ret_len = format_set_cookie(p_rsp_info->set_cookies_list, p_rsp_info->cookies_list_num, p_st_set_cookie_list);
    len += ret_len;

    ret_len = format_bstr_info(&(p_rsp_info->body), p_st_rsp_body);
    len += ret_len;

    return len;
}

char *CHttp2Parser::simple_fast_json_encode(upload_http_fast_info_t& st_upload_http_fast_info)
{
    upload_http_fast_info_t *p_st_upload_http_fast_info = &st_upload_http_fast_info;
    //size_t str_size;
    size_t len = 1000;

    if (p_st_upload_http_fast_info->req_url != NULL)
    {
        p_st_upload_http_fast_info->req_url = cJSON_EscapeString(p_st_upload_http_fast_info->req_url);
        len += strlen(p_st_upload_http_fast_info->req_url);
    }

    len += strlen(p_st_upload_http_fast_info->dst_ip);

    if (p_st_upload_http_fast_info->rsp_content_type.length > 0)
    {   
        size_t escape_str_len = 0;
        p_st_upload_http_fast_info->rsp_content_type.p_value = cJSON_EscapeStringWithBufferSize(p_st_upload_http_fast_info->rsp_content_type.p_value
                                                                                              , p_st_upload_http_fast_info->rsp_content_type.length
                                                                                              , &escape_str_len);
        //len += strlen(p_st_upload_http_fast_info->rsp_content_type.p_value);
        len += escape_str_len;
    }
    else
    {
        p_st_upload_http_fast_info->rsp_content_type.p_value = NULL;
    }

    char* buffer = (char*)cJSON_malloc(len);
    memset(buffer, 0, len);

    if (p_st_upload_http_fast_info->analyze_flag>0)
    {
        sprintf(buffer, fast_template_with_flag, or_null(p_st_upload_http_fast_info->req_url), or_empty(p_st_upload_http_fast_info->dst_ip), or_null(p_st_upload_http_fast_info->rsp_content_type.p_value), p_st_upload_http_fast_info->meta_tm,p_st_upload_http_fast_info->analyze_flag);
    }else{
        sprintf(buffer, fast_template, or_null(p_st_upload_http_fast_info->req_url), or_empty(p_st_upload_http_fast_info->dst_ip), or_null(p_st_upload_http_fast_info->rsp_content_type.p_value), p_st_upload_http_fast_info->meta_tm);
    }
    
    

    IF_FREE(p_st_upload_http_fast_info->req_url);
    IF_FREE((void*)p_st_upload_http_fast_info->rsp_content_type.p_value);
    return buffer;
}

int CHttp2Parser::get_rw_flag(const char* p_file_direction)
{
    if (std::string("upload") == p_file_direction) 
    {
        return RW_FLAG_WRITE;
    }
    else if (std::string("download") == p_file_direction)
    {
        return RW_FLAG_READ;
    }
    
    return RW_FLAG_OTHER;
}

bool CHttp2Parser::is_inline_file(upload_http_info_t& upload_http_info)
{
    http_rsp_info_t *p_rsp_info = &(upload_http_info.http_rsp_info);
    keyvalue_info_t *p_header = p_rsp_info->p_rsp_header;
    for (int i = 0; i < p_rsp_info->rsp_header_num; i++)
    {
        if (strncasecmp(p_header[i].key.p_value, "Content-Disposition", p_header[i].key.length) == 0 &&
            p_header[i].key.length == strlen("Content-Disposition") &&
            std::string(p_header[i].value.p_value, p_header[i].value.length).find("inline") != std::string::npos)
        {
            return true;
        }
    }

    return false;
}

/* 1代表是文件事件，0代表不是文件事件 */
int CHttp2Parser::is_file_event(upload_http_info_t& upload_http_info)
{
    http_file_info_t *p_file_info = &(upload_http_info.http_file_info);
    int rw_flag = get_rw_flag(p_file_info->p_file_direction);
    upload_http_info.rw_flag = rw_flag;
    if (rw_flag == RW_FLAG_OTHER)
    {
        upload_http_info.is_file_event = 0;
        return 0;
    }

    if (rw_flag == RW_FLAG_READ && is_inline_file(upload_http_info))
    {
        upload_http_info.is_file_event = 0;
        return 0;
    }

    if (p_file_info->file_len < m_upload_file_size_lower_limit)
    {
        upload_http_info.is_file_event = 0;
        return 0;
    }
    
    /* 这是原来的判断规则，万一业务需要，可以参考一下
    if (m_upload_file 
        && (( (RW_FLAG_WRITE == rw_flag and !m_post_file_url->hit_url_filter(upload_http_info.http_req_info.full_url)) 
            || 1 == p_file_info->download_flag) 
        || (m_upload_file_type->hit_upload_file_type(p_file_info->p_file_type ? p_file_info->p_file_type : "") 
            && upload_http_info.file_type_reliable))
        && !m_is_drop_file_event) */
    /*
    if (   m_upload_file 
        && (((RW_FLAG_WRITE == rw_flag and !m_post_file_url->hit_url_filter(upload_http_info.http_req_info.full_url)) || 1 == p_file_info->download_flag) || upload_http_info.file_type_reliable)// (((RW_FLAG_WRITE == rw_flag 且 没有命中要过滤url) 或 要落盘) 或 文件类型确定) 为真
        && !m_is_drop_file_event
        && m_upload_file_type->hit_upload_file_type(p_file_info->p_file_type ? p_file_info->p_file_type : ""))
    {
        upload_http_info.is_file_event = 1;
        return 1;
    }

    upload_http_info.is_file_event = 0;
    */
    return 0;
}

char* CHttp2Parser::simple_json_encode(upload_http_info_t& upload_http_info, int unknown_rule, int analyze, size_t *p_s_len) 
{
#ifdef DEBUG
    if (is_file_event(upload_http_info) == 1)
    {
        /* 放入异步队列 */
        http_cb_http_minio(&upload_http_info, unknown_rule, analyze);
        return NULL;
    }
    else
    {
        if (! m_use_new_event_format)
        {
            return simple_json_encode_inner(&upload_http_info, p_s_len);
        }
        else
        {
            return new_event_format_encode(&upload_http_info, p_s_len);
        }
    }
#endif
   return simple_json_encode_inner(&upload_http_info, p_s_len);

}

char *CHttp2Parser::simple_json_encode_inner(upload_http_info_t *p_upload_http_info, size_t *p_s_len)
{
    size_t len = 1000;
    size_t ret_len = 0;
    size_t offset = 0;
    http_req_info_t *p_req_info = &(p_upload_http_info->http_req_info);
    http_rsp_info_t *p_rsp_info = &(p_upload_http_info->http_rsp_info);
    http_meta_info_t *p_meta_info = &(p_upload_http_info->http_meta_info);
    net_info_t *p_net_info = &(p_upload_http_info->http_net_info);
    http_file_info_t *p_file_info = &(p_upload_http_info->http_file_info);
    
    st_str_info_t st_url = {0};
    st_str_info_t st_req_body = {0};
    st_str_info_t st_req_header = {0};
    st_str_info_t st_rsp_body = {0};
    st_str_info_t st_rsp_header = {0};
    st_str_info_t st_set_cookie = {0};

    bstr_t body = {0};
    if (p_upload_http_info->rw_flag == RW_FLAG_WRITE) 
    {
        body = p_req_info->body;
    }
    else if (p_upload_http_info->rw_flag == RW_FLAG_READ)
    {
        body = p_rsp_info->body;
    }
    
    p_req_info->body.length = MIN(p_req_info->body.length, (size_t)m_conf_http_request_body_max_size);
    p_rsp_info->body.length = MIN(p_rsp_info->body.length, (size_t)m_conf_http_response_body_max_size);
    /* 计算req占用的字节数 */
    size_t req_method_len = (p_req_info->p_method) ? strlen(p_req_info->p_method) : 0;
    size_t remote_addr_len = strlen(p_req_info->remote_addr);
    ret_len = calculate_req_len(p_req_info, &st_url, &st_req_body, &st_req_header);
    len += (req_method_len + remote_addr_len + ret_len);

    /* 计算rsp占用的字节数 */
    ret_len = calculate_rsp_len(p_rsp_info, &st_rsp_body, &st_rsp_header, &st_set_cookie); 
    len += ret_len;

    /* meta信息 p_session p_name p_uid三个信息已经废弃 */
    /* net 信息长度 */
    size_t src_ip_len = strlen(p_net_info->a_src_ip);
    size_t dst_ip_len = strlen(p_net_info->a_dst_ip);
    size_t pcap_filename_len = (p_net_info->pcap_filename) ? strlen(p_net_info->pcap_filename) : 0;
    len += (src_ip_len + dst_ip_len + pcap_filename_len);

    /* 计算file信息占用的字节数 */
    size_t file_direction_len =  (p_file_info->p_file_direction) ? strlen(p_file_info->p_file_direction) : 0;
    size_t file_type_len = (p_file_info->p_file_type) ? strlen(p_file_info->p_file_type) : 0;
    size_t file_name_len = (p_file_info->p_file_name) ? strlen(p_file_info->p_file_name) : 0;
    size_t file_warn_len = (p_file_info->p_file_warn) ? strlen(p_file_info->p_file_warn) : 0;
    // len += (file_direction_len + file_type_len + file_name_len + file_warn_len);

    
    /*
    FileUpload* p_minio_upload = NULL;
    if (p_upload_http_info->is_file_event)
    {
        p_minio_upload =  m_comm->get_minio_upload();
    }
    file_info_json json = file_info_format_encode(p_file_info->p_file_name, file_name_len,
                                                p_file_info->p_file_type, file_type_len,
                                                p_file_info->p_file_warn, file_warn_len,
                                                body.p_value, body.length, 
                                                0, p_upload_http_info->rw_flag, p_minio_upload, m_conf_http_request_body_max_size);
    file_type_len = (json.file_type) ? strlen(json.file_type) : 4;
    file_name_len = (json.file_name) ? strlen(json.file_name) : 4;
    file_warn_len = (json.file_warn) ? strlen(json.file_warn) : 4;
    size_t upload_dir_len = (json.dir) ? strlen(json.dir) : 4;
    size_t sha256_len = (json.sha256) ? strlen(json.sha256) : 4;
    len += (file_direction_len + file_type_len + file_name_len + file_warn_len + upload_dir_len + sha256_len);

    if (json.upload_flag || m_is_drop_file_event) 
    {
        if (p_upload_http_info->rw_flag == RW_FLAG_WRITE) 
        {
            IF_FREE(st_req_body.p_str);
            st_req_body.p_str = NULL;
            st_req_body.str_len = 0;
        }
        else 
        {
            IF_FREE(st_rsp_body.p_str);
            st_rsp_body.p_str = NULL;
            st_rsp_body.str_len = 0;
        }
    }
    
    std::string md5_sum = m_conf_http_body_md5 ? md5sum(body.p_value, body.length):"";
    len += md5_sum.size();
    */
    size_t unique_id_len = strlen(p_upload_http_info->a_unique_id);
    len += unique_id_len;

    char* buffer = NULL;
    if (0 == m_upload_protobuf_enable)
    {
        buffer = (char*)malloc(len);
        if (NULL == buffer)
        {
            goto end;
        }
    
        strcpy(buffer, g_p_req_method);
        offset += g_req_method_len;
        strcpy(buffer + offset, or_empty(p_req_info->p_method));
        offset += req_method_len;

        strcpy(buffer + offset, g_p_req_http_version);
        offset += g_req_http_version_len;
        ntos(p_req_info->http_major, buffer + offset, &offset);
        buffer[offset++] = '.';
        ntos(p_req_info->http_minor, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_req_remote_addr);
        offset += g_req_remote_addr_len;
        strcpy(buffer + offset, p_req_info->remote_addr);
        offset += remote_addr_len;
        
        strcpy(buffer + strlen(buffer), g_p_req_err_code);
        offset += g_req_err_code_len;
        ntos(p_req_info->error_code, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_req_url);
        offset += g_req_url_len;
        strcpy(buffer + offset, or_empty(st_url.p_str));
        offset += st_url.str_len;    

        strcpy(buffer + offset, g_p_req_body);
        offset += g_req_body_len;
        strcpy(buffer + offset, or_null(st_req_body.p_str));
        offset += (st_req_body.p_str ? st_req_body.str_len : 4);

        strcpy(buffer + offset, g_p_req_header);
        offset += g_req_header_len;
        strcpy(buffer + offset, or_null(st_req_header.p_str));
        offset += (st_req_header.p_str ? st_req_header.str_len : 4);

        strcpy(buffer + offset, g_p_rsp_status);
        offset += g_rsp_status_len;
        ntos(p_rsp_info->status_code, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_rsp_http_version);
        offset += g_rsp_http_version_len;
        ntos(p_rsp_info->http_major, buffer + offset, &offset);
        buffer[offset++] = '.';
        ntos(p_rsp_info->http_minor, buffer + offset, &offset);
        
        strcpy(buffer + offset, g_rsp_err_code);
        offset += g_rsp_err_code_len;
        ntos(p_rsp_info->error_code, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_rsp_header);
        offset += g_rsp_header_len;
        strcpy(buffer + offset,  or_null(st_rsp_header.p_str));
        offset += (st_rsp_header.p_str ? st_rsp_header.str_len : 4);

        strcpy(buffer + offset, g_p_set_cookies_list);
        offset += g_set_cookies_list_len;
        strcpy(buffer + offset, or_empty(st_set_cookie.p_str));
        offset += st_set_cookie.str_len;

        strcpy(buffer + offset, g_p_rsp_body);
        offset += g_rsp_body_len;
        strcpy(buffer + offset, or_null(st_rsp_body.p_str));
        offset += (st_rsp_body.p_str ? st_rsp_body.str_len : 4);

        strcpy(buffer + offset, g_p_meta_tm);
        offset += g_meta_tm_len;
        //ntos(p_meta_info->ts, buffer + offset, &offset);
        sprintf (buffer + offset, "%.3f", p_meta_info->ts);
        offset = strlen(buffer);

        strcpy(buffer + offset, g_p_meta_req_seq);
        offset += g_meta_req_seq_len;
        ntos(p_meta_info->req_seq, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_meta_req_ack);
        offset += g_meta_req_ack_len;
        ntos(p_meta_info->req_ack, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_meta_rsp_req);
        offset += g_meta_rsp_req_len;
        ntos(p_meta_info->rsp_seq, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_meta_rsp_ack);
        offset += g_meta_rsp_ack_len;
        ntos(p_meta_info->rsp_ack, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_net_src_ip);
        offset += g_net_src_ip_len;
        strcpy(buffer + offset, p_net_info->a_src_ip);
        offset += src_ip_len;

        strcpy(buffer + offset, g_p_net_src_port);
        offset += g_net_src_port_len;
        ntos(p_net_info->src_port, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_net_dst_ip);
        offset += g_net_dst_ip_len;
        strcpy(buffer + offset, p_net_info->a_dst_ip);
        offset += dst_ip_len;

        strcpy(buffer + offset, g_p_net_dst_port);
        offset += g_net_dst_port_len;
        ntos(p_net_info->dst_port, buffer + offset, &offset);

        strcpy(buffer + offset, g_p_pcap_filename);
        offset += g_pcap_filename_len;
        strcpy(buffer + offset, p_net_info->pcap_filename);
        offset += pcap_filename_len;

        strcpy(buffer + offset, g_p_unique_id);
        offset += g_unique_id_len;
        strcpy(buffer + offset, p_upload_http_info->a_unique_id);
        offset += unique_id_len;

        strcpy(buffer + offset, g_p_file_data);
        offset += g_file_data_len;
        strcpy(buffer + offset, "null");
        offset += 4;

        if (p_upload_http_info->analyze_flag>0)
        {
            strcpy(buffer + offset, g_p_analyse_flag);
            offset += g_analyse_flag_len;
            ntos(p_upload_http_info->analyze_flag, buffer + offset, &offset);
        }else{
            *(buffer + offset) = '}';
            offset++;
        }


        *(buffer + offset++) = '}';
        *(buffer + offset) = '\0';

        *p_s_len = strlen(buffer);
    }
    else if (1 == m_upload_protobuf_enable)
    {
        char buf[1024] = {0};
        ProtobufRawHttpEvent protobuf_raw_http_event;

        HttpRequest* req = protobuf_raw_http_event.mutable_req();
        req->set_method(or_empty(p_req_info->p_method));
        snprintf(buf, sizeof(buf), "%d.%d", p_req_info->http_major, p_req_info->http_minor);
        req->set_httpversion(buf);
        req->set_remoteaddr(p_req_info->remote_addr);
        req->set_errcode(p_req_info->error_code);
        req->set_url(p_req_info->full_url);

        if (NULL != st_req_body.p_str)
        {
            req->set_body(p_req_info->body.p_value, p_req_info->body.length);
        }

        google::protobuf::Map<std::string, std::string>* reqHeader = req->mutable_header();
        keyvalue_info_t *p_header = p_req_info->p_req_header;
        for (int i = 0; i < p_req_info->req_header_num; i++)
        {
            (*reqHeader)[std::string(p_header[i].key.p_value, p_header[i].key.length)] = std::string(p_header[i].value.p_value, p_header[i].value.length);
        }

        HttpResponse* rsp = protobuf_raw_http_event.mutable_rsp();
        snprintf(buf, sizeof(buf), "%d", p_rsp_info->status_code);
        rsp->set_status(buf);
        snprintf(buf, sizeof(buf), "%d.%d", p_rsp_info->http_major, p_rsp_info->http_minor);
        rsp->set_httpversion(buf);
        rsp->set_errcode(p_rsp_info->error_code);

        if (st_rsp_body.p_str != NULL)
        {
            rsp->set_body(p_rsp_info->body.p_value, p_rsp_info->body.length);
        }

        for (int i = 0; i < p_rsp_info->cookies_list_num; i++)
        {
            rsp->add_setcookieslist(p_rsp_info->set_cookies_list->p_value);
        }

        google::protobuf::Map<std::string, std::string>* rspHeader = rsp->mutable_header();
        p_header = p_rsp_info->p_rsp_header;
        for (int i = 0; i < p_rsp_info->rsp_header_num; i++)
        {
            (*rspHeader)[std::string(p_header[i].key.p_value, p_header[i].key.length)] = std::string(p_header[i].value.p_value, p_header[i].value.length);
        }

        Meta* meta = protobuf_raw_http_event.mutable_meta();
        meta->set_tm(p_meta_info->ts);

        Net* net = protobuf_raw_http_event.mutable_net();
        net->set_srcip(p_net_info->a_src_ip);
        net->set_srcport(p_net_info->src_port);
        net->set_dstip(p_net_info->a_dst_ip);
        net->set_dstport(p_net_info->dst_port);

        UniqueId* unique_id = protobuf_raw_http_event.mutable_uniqueid();
        unique_id->set_eventid(p_upload_http_info->a_unique_id);

        buffer = (char*)malloc(protobuf_raw_http_event.ByteSizeLong());
        if (NULL == buffer)
        {
            goto end;
        }

        protobuf_raw_http_event.SerializeToArray(buffer, protobuf_raw_http_event.ByteSizeLong());
        *p_s_len = protobuf_raw_http_event.ByteSizeLong();
    }
end:
    SAFE_FREE(st_req_header.p_str);
    SAFE_FREE(st_rsp_header.p_str);
    SAFE_FREE(st_set_cookie.p_str);
    IF_FREE(st_url.p_str);
    IF_FREE(st_req_body.p_str);
    IF_FREE(st_rsp_body.p_str);

    return buffer;
}

char *CHttp2Parser::new_event_format_encode(upload_http_info_t *p_upload_http_info, size_t *p_s_len)
{
#ifdef DEBUG
    size_t len = 1000;
    size_t real_len = 0;
    
    char *buffer = NULL;

    http_req_info_t *p_req_info = &(p_upload_http_info->http_req_info);
    http_rsp_info_t *p_rsp_info = &(p_upload_http_info->http_rsp_info);
    http_meta_info_t *p_meta_info = &(p_upload_http_info->http_meta_info);
    net_info_t *p_net_info = &(p_upload_http_info->http_net_info);
    http_file_info_t *p_file_info = &(p_upload_http_info->http_file_info);
    size_t unique_id_len = strlen(p_upload_http_info->a_unique_id);
    size_t src_ip_len = strlen(p_net_info->a_src_ip);
    size_t dst_ip_len = strlen(p_net_info->a_dst_ip);
    size_t url_len = strlen(p_req_info->full_url);
    size_t method_len = strlen(p_req_info->p_method);
    size_t filename_len = 0;
    size_t filetype_len = 0;
    size_t sha256_len = 0;
    upload_stats_t result;

    len += unique_id_len;
    len += src_ip_len;
    len += dst_ip_len;
    len += url_len;
    len += method_len;

    bstr_t req_body = {0};
    bstr_t rsp_body = {0};
    if (p_upload_http_info->is_file_event)
    {
        filetype_len = strlen(p_file_info->p_file_type);
        filename_len = strlen(p_file_info->p_file_name);
        len += filename_len;
        len += filetype_len;
        bstr_t body = {0};
        if (p_upload_http_info->rw_flag == RW_FLAG_WRITE) 
        {
            body = p_req_info->body;
            rsp_body = p_rsp_info->body;
            rsp_body.length = MIN(rsp_body.length, (size_t)m_conf_http_response_body_max_size);
        }
        else if (p_upload_http_info->rw_flag == RW_FLAG_READ)
        {
            body = p_rsp_info->body;
            req_body = p_req_info->body;
            req_body.length = MIN(req_body.length, (size_t)m_conf_http_request_body_max_size);
        }
        FileUpload* p_minio_upload = m_comm->get_minio_upload();

        result = p_minio_upload->upload_file(body.p_value, body.length, NULL);
        if (result.ret != 0) /* 上传失败 */
        {
            if (p_upload_http_info->rw_flag == RW_FLAG_WRITE)
            {
                req_body = body;
                req_body.length = MIN(req_body.length, (size_t)m_conf_http_request_body_max_size);
            }
            else
            {
                rsp_body = body;
                rsp_body.length = MIN(rsp_body.length, (size_t)m_conf_http_response_body_max_size);
            }

            len += g_null_content_len;
        }
        else
        {
            sha256_len = result.str_sha256.size(); 
            len += sha256_len;
        }

        if (p_upload_http_info->file_type_reliable)
        {
            len += type_reliable_new_len;
        }
        else
        {
            len += type_unreliable_new_len;
        }
        len += complete_new_len;
    }
    else
    {
        req_body = p_req_info->body;
        rsp_body = p_rsp_info->body;
        req_body.length = MIN(req_body.length, (size_t)m_conf_http_request_body_max_size);
        rsp_body.length = MIN(rsp_body.length, (size_t)m_conf_http_response_body_max_size);
    }
    
    len += req_body.length;
    len += rsp_body.length;

    for (int i = 0; i < p_req_info->req_header_num; i++)
    {
        len += p_req_info->p_req_header[i].key.length;
        len += p_req_info->p_req_header[i].value.length;
    }

    for (int i = 0; i < p_rsp_info->rsp_header_num; i++)
    {
        len += p_rsp_info->p_rsp_header[i].key.length;
        len += p_rsp_info->p_rsp_header[i].value.length;
    }

    for (int i = 0; i < p_rsp_info->cookies_list_num; i++)
    {
        len += p_rsp_info->set_cookies_list[i].length;
    }

    buffer = (char *)malloc(len);
    if (buffer == NULL)
    {
        return NULL;
    }
    memset(buffer, 0, len);

    memcpy(buffer + real_len, p_event_header_new, event_header_new_len);
    real_len += event_header_new_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_s_ip_new, s_ip_new_len);
    real_len += s_ip_new_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_net_info->a_src_ip, src_ip_len);
    real_len += src_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_net_info->src_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_d_ip_new, d_ip_new_len);
    real_len += d_ip_new_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_net_info->a_dst_ip, dst_ip_len);
    real_len += dst_ip_len;
    buffer[real_len++] = ' ';
    ntos(p_net_info->dst_port, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_event_id_new, event_id_new_len);
    real_len += event_id_new_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_upload_http_info->a_unique_id, unique_id_len);
    real_len += unique_id_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_time_new, time_new_len);
    real_len += time_new_len;
    buffer[real_len++] = ' ';
    uint64_t ts = p_meta_info->ts * 1000;
    ntos(ts, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_file_new, file_new_len);
    real_len += file_new_len;
    buffer[real_len++] = ' ';
    if (p_upload_http_info->is_file_event)
    {
        ntos(1, buffer + real_len, &real_len);
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';
        if (p_upload_http_info->rw_flag == RW_FLAG_WRITE)
        {
            buffer[real_len++] = 'W';
        }
        else
        {
            buffer[real_len++] = 'R';
        }
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_complete_new, complete_new_len);
        real_len += complete_new_len;
        buffer[real_len++] = ' ';
        if (p_upload_http_info->file_type_reliable)
        {
            memcpy(buffer + real_len, p_type_reliable_new, type_reliable_new_len);
            real_len += type_reliable_new_len;
        }
        else
        {
            memcpy(buffer + real_len, p_type_unreliable_new, type_unreliable_new_len);
            real_len += type_unreliable_new_len;
        }
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_file_info->p_file_type, filetype_len);
        real_len += filetype_len;
        buffer[real_len++] = ' ';
        ntos(p_file_info->file_len, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        ntos(filename_len, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_file_info->p_file_name, filename_len);
        real_len += filename_len;
        buffer[real_len++] = ' ';
        if (sha256_len > 0)
        {
            memcpy(buffer + real_len, result.str_sha256.c_str(), sha256_len);
            real_len += sha256_len;
        }
        else
        {
            memcpy(buffer + real_len, g_p_null_content, g_null_content_len);
            real_len += g_null_content_len;
        }
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';
    }
    else
    {
        ntos(0, buffer + real_len, &real_len);
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';
    }
    

    memcpy(buffer + real_len, p_error_code_new, error_code_new_len);
    real_len += error_code_new_len;
    buffer[real_len++] = ' ';
    ntos(p_rsp_info->error_code, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_method_new, method_new_len);
    real_len += method_new_len;
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_req_info->p_method, method_len);
    real_len += method_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_url_new, url_new_len);
    real_len += url_new_len;
    buffer[real_len++] = ' ';
    ntos(url_len, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, p_req_info->full_url, url_len);
    real_len += url_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_status_new, status_new_len);
    real_len += status_new_len;
    buffer[real_len++] = ' ';
    ntos(p_rsp_info->status_code, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_set_cookie_list_new, set_cookie_list_new_len);
    real_len += set_cookie_list_new_len;
    buffer[real_len++] = ' ';
    ntos(p_rsp_info->cookies_list_num, buffer + real_len, &real_len);
    for (int i = 0; i < p_rsp_info->cookies_list_num; i++)
    {
        buffer[real_len++] = ' ';
        ntos(p_rsp_info->set_cookies_list[i].length, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_rsp_info->set_cookies_list[i].p_value, p_rsp_info->set_cookies_list[i].length);
        real_len += p_rsp_info->set_cookies_list[i].length;
    }
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_req_h_new, req_h_new_len);
    real_len += req_h_new_len;
    buffer[real_len++] = ' ';
    ntos(p_req_info->req_header_num, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    for (int i = 0; i < p_req_info->req_header_num; i++)
    {
        ntos(p_req_info->p_req_header[i].key.length, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_req_info->p_req_header[i].key.p_value, p_req_info->p_req_header[i].key.length);
        real_len += p_req_info->p_req_header[i].key.length;
        buffer[real_len++] = ' ';

        ntos(p_req_info->p_req_header[i].value.length, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_req_info->p_req_header[i].value.p_value, p_req_info->p_req_header[i].value.length);
        real_len += p_req_info->p_req_header[i].value.length;
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';
    }

    memcpy(buffer + real_len, p_rsp_h_new, rsp_h_new_len);
    real_len += rsp_h_new_len;
    buffer[real_len++] = ' ';
    ntos(p_rsp_info->rsp_header_num, buffer + real_len, &real_len);
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    for (int i = 0; i < p_rsp_info->rsp_header_num; i++)
    {
        ntos(p_rsp_info->p_rsp_header[i].key.length, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_rsp_info->p_rsp_header[i].key.p_value, p_rsp_info->p_rsp_header[i].key.length);
        real_len += p_rsp_info->p_rsp_header[i].key.length;
        buffer[real_len++] = ' ';

        ntos(p_rsp_info->p_rsp_header[i].value.length, buffer + real_len, &real_len);
        buffer[real_len++] = ' ';
        memcpy(buffer + real_len, p_rsp_info->p_rsp_header[i].value.p_value, p_rsp_info->p_rsp_header[i].value.length);
        real_len += p_rsp_info->p_rsp_header[i].value.length;
        buffer[real_len++] = '\r';
        buffer[real_len++] = '\n';
    }

    memcpy(buffer + real_len, p_req_b_new, req_b_new_len);
    real_len += req_b_new_len;
    buffer[real_len++] = ' ';
    ntos(req_body.length, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, req_body.p_value, req_body.length);
    real_len += req_body.length;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_rsp_b_new, rsp_b_new_len);
    real_len += rsp_b_new_len;
    buffer[real_len++] = ' ';
    ntos(rsp_body.length, buffer + real_len, &real_len);
    buffer[real_len++] = ' ';
    memcpy(buffer + real_len, rsp_body.p_value, rsp_body.length);
    real_len += rsp_body.length;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';

    memcpy(buffer + real_len, p_end_new, end_new_len);
    real_len += end_new_len;
    buffer[real_len++] = '\r';
    buffer[real_len++] = '\n';
    *p_s_len = real_len;

    return buffer;
#endif
    return NULL;
}