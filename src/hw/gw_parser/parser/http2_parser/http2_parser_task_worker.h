/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __HTTP2_PARSER_TASK_WORKER_H__
#define __HTTP2_PARSER_TASK_WORKER_H__

#include "task_worker.h"

//  TaskWorkerData twd;

class CHttp2Parser;

class CTaskWorkerHttp2 : public CTaskWorker
{
public:
  virtual CWorkerQueue *get_wq(void) const;
  // virtual int deal_data(const TaskWorkerData *) = 0;
  // virtual void free_data(const TaskWorkerData *) = 0;

  virtual void init(void);
  virtual void fini(void);
  virtual void release(void) const;

public:
  virtual ~CTaskWorkerHttp2();

  inline void set_wq(CWorkerQueue *pwq)
  {
    m_pwq = pwq;
  }

  inline void set_parser(CHttp2Parser *parser)
  {
    m_parser = parser;
  }

  inline CHttp2Parser *get_parser(void)
  {
    return m_parser;
  }

protected:
  CWorkerQueue *m_pwq;
  CHttp2Parser *m_parser;
};

#endif // __HTTP2_PARSER_TASK_WORKER_H__
