#ifndef HPACK_TABLE_H_
#define HPACK_TABLE_H_

#include <stdint.h>
#include <utility>
#include <string>
#include "hpack_huffman.h"

typedef std::pair<std::string, std::string> header;

struct RingTable {
    header h;
    RingTable *nxt, *pre;
};

class Table {
public:
    Table();
    ~Table();
    bool findHeader(int &index, const header h);
    void deleteLastEntry();
    void addHeader(const header h);
    void setDynamicTableSize(uint32_t size);
    uint32_t getDynamicTableSize(void){return this->dynamic_table_size;};
    int64_t parseString(std::string &dst, const uint8_t* buf);
    int64_t packString(uint8_t* buf, const std::string content, bool to_huffman);
    header getHeader(uint32_t index);
    int64_t parseHeader(header &dst, uint32_t index, const uint8_t* buf, bool isIndexed);
private:
    uint32_t dynamic_table_size;
    RingTable *head, *tail;
    HuffmanTree *huffman;
    uint32_t entry_size;
    uint32_t entry_num;
};

#endif // HPACK_TABLE_H_