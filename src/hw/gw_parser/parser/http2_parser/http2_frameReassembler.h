#ifndef HTTP2_FRAMEREASSEMBLER_H
#define HTTP2_FRAMEREASSEMBLER_H

#include <vector>
#include "http2_frame.h"

namespace analyzer { namespace submodule{

static constexpr size_t MIN_BUFFER_SIZE = 65535;
static constexpr size_t MAX_BUFFER_SIZE = 33554430; // ~32MB
/**
 * class HTTP2_FrameReassembler
 *
 * Description: class used to manage packet fragment reassembly and processing.
 *
 */
class HTTP2_FrameReassembler{
public:
    HTTP2_FrameReassembler();
    ~HTTP2_FrameReassembler();

    // resize the internal assembly buffer
    void resizeBuffer(uint32_t size);

    // extract frames from packet
    std::vector<HTTP2_Frame*> process(const uint8_t* data, uint32_t len);
private:
    // generate a frame from the given data
    HTTP2_Frame* loadFrame(HTTP2_FrameHeader* fh, uint8_t* payload, uint32_t len);

    // allocates and initializes the data buffer
    void allocateBuffer(void);

    // store fragmented data in buffer
    void setBuffer(uint8_t *data,uint32_t len);

    // add new fragment to the buffer
    void appendBuffer(uint8_t* data, uint32_t len);
    
    void clearBuffer(void);
private:
    bool fragmentedPacket;  // packet is fragmented?
    uint8_t* buffer;        
    uint32_t bufferLen;
    uint32_t bufferSize;
    uint32_t copyLen;       // a complete frame remain len
};

} } // namespace analyzer::*

#endif//HTTP2_FRAMEREASSEMBLER_H