/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>

#include "http2_parser.h"

// 公共函数
extern "C" void gw_no_mem(const char *func)
{
  fprintf(stderr, "Out of memory in %s.\n", func);
  exit(1);
}


CParser::~CParser(void)
{
}

// 加载模块
extern "C" __attribute__((visibility("default"))) int load_parser(CParser **p, int size)
{
  if (p == NULL || size <= 0)
  {
    return -1;
  }

  p[0] = new CHttp2Parser();

  return 1;
}

// 卸载模块
extern "C" __attribute__((visibility("default"))) void unload_parser(CParser **p, int size)
{
  for (int i = 0; i < size; i++)
  {
    delete p[i];
  }

  return;
}
