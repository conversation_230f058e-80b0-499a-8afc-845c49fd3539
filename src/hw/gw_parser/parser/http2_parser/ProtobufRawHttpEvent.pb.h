// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ProtobufRawHttpEvent.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ProtobufRawHttpEvent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ProtobufRawHttpEvent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3020000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3020001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ProtobufRawHttpEvent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ProtobufRawHttpEvent_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ProtobufRawHttpEvent_2eproto;
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {
class HttpRequest;
struct HttpRequestDefaultTypeInternal;
extern HttpRequestDefaultTypeInternal _HttpRequest_default_instance_;
class HttpRequest_HeaderEntry_DoNotUse;
struct HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal;
extern HttpRequest_HeaderEntry_DoNotUseDefaultTypeInternal _HttpRequest_HeaderEntry_DoNotUse_default_instance_;
class HttpResponse;
struct HttpResponseDefaultTypeInternal;
extern HttpResponseDefaultTypeInternal _HttpResponse_default_instance_;
class HttpResponse_HeaderEntry_DoNotUse;
struct HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal;
extern HttpResponse_HeaderEntry_DoNotUseDefaultTypeInternal _HttpResponse_HeaderEntry_DoNotUse_default_instance_;
class Meta;
struct MetaDefaultTypeInternal;
extern MetaDefaultTypeInternal _Meta_default_instance_;
class Net;
struct NetDefaultTypeInternal;
extern NetDefaultTypeInternal _Net_default_instance_;
class ProtobufRawHttpEvent;
struct ProtobufRawHttpEventDefaultTypeInternal;
extern ProtobufRawHttpEventDefaultTypeInternal _ProtobufRawHttpEvent_default_instance_;
class RawFileInfo;
struct RawFileInfoDefaultTypeInternal;
extern RawFileInfoDefaultTypeInternal _RawFileInfo_default_instance_;
class Source;
struct SourceDefaultTypeInternal;
extern SourceDefaultTypeInternal _Source_default_instance_;
class UniqueId;
struct UniqueIdDefaultTypeInternal;
extern UniqueIdDefaultTypeInternal _UniqueId_default_instance_;
}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com
PROTOBUF_NAMESPACE_OPEN
template<> ::com::quanzhi::audit_core::common::model::HttpRequest* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpRequest>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpRequest_HeaderEntry_DoNotUse>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::HttpResponse* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpResponse>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpResponse_HeaderEntry_DoNotUse>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Meta* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Meta>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Net* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Net>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::ProtobufRawHttpEvent>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::RawFileInfo* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::RawFileInfo>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::Source* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Source>(Arena*);
template<> ::com::quanzhi::audit_core::common::model::UniqueId* Arena::CreateMaybeMessage<::com::quanzhi::audit_core::common::model::UniqueId>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace com {
namespace quanzhi {
namespace audit_core {
namespace common {
namespace model {

enum SourceTypeEnum : int {
  app_har = 0,
  flow = 1,
  SourceTypeEnum_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SourceTypeEnum_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SourceTypeEnum_IsValid(int value);
constexpr SourceTypeEnum SourceTypeEnum_MIN = app_har;
constexpr SourceTypeEnum SourceTypeEnum_MAX = flow;
constexpr int SourceTypeEnum_ARRAYSIZE = SourceTypeEnum_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SourceTypeEnum_descriptor();
template<typename T>
inline const std::string& SourceTypeEnum_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SourceTypeEnum>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SourceTypeEnum_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SourceTypeEnum_descriptor(), enum_t_value);
}
inline bool SourceTypeEnum_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SourceTypeEnum* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SourceTypeEnum>(
    SourceTypeEnum_descriptor(), name, value);
}
// ===================================================================

class ProtobufRawHttpEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent) */ {
 public:
  inline ProtobufRawHttpEvent() : ProtobufRawHttpEvent(nullptr) {}
  ~ProtobufRawHttpEvent() override;
  explicit PROTOBUF_CONSTEXPR ProtobufRawHttpEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProtobufRawHttpEvent(const ProtobufRawHttpEvent& from);
  ProtobufRawHttpEvent(ProtobufRawHttpEvent&& from) noexcept
    : ProtobufRawHttpEvent() {
    *this = ::std::move(from);
  }

  inline ProtobufRawHttpEvent& operator=(const ProtobufRawHttpEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProtobufRawHttpEvent& operator=(ProtobufRawHttpEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProtobufRawHttpEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProtobufRawHttpEvent* internal_default_instance() {
    return reinterpret_cast<const ProtobufRawHttpEvent*>(
               &_ProtobufRawHttpEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProtobufRawHttpEvent& a, ProtobufRawHttpEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(ProtobufRawHttpEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProtobufRawHttpEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProtobufRawHttpEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProtobufRawHttpEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProtobufRawHttpEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ProtobufRawHttpEvent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProtobufRawHttpEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent";
  }
  protected:
  explicit ProtobufRawHttpEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCharsetFieldNumber = 7,
    kReqFieldNumber = 1,
    kRspFieldNumber = 2,
    kMetaFieldNumber = 3,
    kNetFieldNumber = 4,
    kFileFieldNumber = 5,
    kUniqueIdFieldNumber = 6,
    kSourceFieldNumber = 8,
  };
  // string charset = 7;
  void clear_charset();
  const std::string& charset() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_charset(ArgT0&& arg0, ArgT... args);
  std::string* mutable_charset();
  PROTOBUF_NODISCARD std::string* release_charset();
  void set_allocated_charset(std::string* charset);
  private:
  const std::string& _internal_charset() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_charset(const std::string& value);
  std::string* _internal_mutable_charset();
  public:

  // .com.quanzhi.audit_core.common.model.HttpRequest req = 1;
  bool has_req() const;
  private:
  bool _internal_has_req() const;
  public:
  void clear_req();
  const ::com::quanzhi::audit_core::common::model::HttpRequest& req() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::HttpRequest* release_req();
  ::com::quanzhi::audit_core::common::model::HttpRequest* mutable_req();
  void set_allocated_req(::com::quanzhi::audit_core::common::model::HttpRequest* req);
  private:
  const ::com::quanzhi::audit_core::common::model::HttpRequest& _internal_req() const;
  ::com::quanzhi::audit_core::common::model::HttpRequest* _internal_mutable_req();
  public:
  void unsafe_arena_set_allocated_req(
      ::com::quanzhi::audit_core::common::model::HttpRequest* req);
  ::com::quanzhi::audit_core::common::model::HttpRequest* unsafe_arena_release_req();

  // .com.quanzhi.audit_core.common.model.HttpResponse rsp = 2;
  bool has_rsp() const;
  private:
  bool _internal_has_rsp() const;
  public:
  void clear_rsp();
  const ::com::quanzhi::audit_core::common::model::HttpResponse& rsp() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::HttpResponse* release_rsp();
  ::com::quanzhi::audit_core::common::model::HttpResponse* mutable_rsp();
  void set_allocated_rsp(::com::quanzhi::audit_core::common::model::HttpResponse* rsp);
  private:
  const ::com::quanzhi::audit_core::common::model::HttpResponse& _internal_rsp() const;
  ::com::quanzhi::audit_core::common::model::HttpResponse* _internal_mutable_rsp();
  public:
  void unsafe_arena_set_allocated_rsp(
      ::com::quanzhi::audit_core::common::model::HttpResponse* rsp);
  ::com::quanzhi::audit_core::common::model::HttpResponse* unsafe_arena_release_rsp();

  // .com.quanzhi.audit_core.common.model.Meta meta = 3;
  bool has_meta() const;
  private:
  bool _internal_has_meta() const;
  public:
  void clear_meta();
  const ::com::quanzhi::audit_core::common::model::Meta& meta() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Meta* release_meta();
  ::com::quanzhi::audit_core::common::model::Meta* mutable_meta();
  void set_allocated_meta(::com::quanzhi::audit_core::common::model::Meta* meta);
  private:
  const ::com::quanzhi::audit_core::common::model::Meta& _internal_meta() const;
  ::com::quanzhi::audit_core::common::model::Meta* _internal_mutable_meta();
  public:
  void unsafe_arena_set_allocated_meta(
      ::com::quanzhi::audit_core::common::model::Meta* meta);
  ::com::quanzhi::audit_core::common::model::Meta* unsafe_arena_release_meta();

  // .com.quanzhi.audit_core.common.model.Net net = 4;
  bool has_net() const;
  private:
  bool _internal_has_net() const;
  public:
  void clear_net();
  const ::com::quanzhi::audit_core::common::model::Net& net() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Net* release_net();
  ::com::quanzhi::audit_core::common::model::Net* mutable_net();
  void set_allocated_net(::com::quanzhi::audit_core::common::model::Net* net);
  private:
  const ::com::quanzhi::audit_core::common::model::Net& _internal_net() const;
  ::com::quanzhi::audit_core::common::model::Net* _internal_mutable_net();
  public:
  void unsafe_arena_set_allocated_net(
      ::com::quanzhi::audit_core::common::model::Net* net);
  ::com::quanzhi::audit_core::common::model::Net* unsafe_arena_release_net();

  // .com.quanzhi.audit_core.common.model.RawFileInfo file = 5;
  bool has_file() const;
  private:
  bool _internal_has_file() const;
  public:
  void clear_file();
  const ::com::quanzhi::audit_core::common::model::RawFileInfo& file() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::RawFileInfo* release_file();
  ::com::quanzhi::audit_core::common::model::RawFileInfo* mutable_file();
  void set_allocated_file(::com::quanzhi::audit_core::common::model::RawFileInfo* file);
  private:
  const ::com::quanzhi::audit_core::common::model::RawFileInfo& _internal_file() const;
  ::com::quanzhi::audit_core::common::model::RawFileInfo* _internal_mutable_file();
  public:
  void unsafe_arena_set_allocated_file(
      ::com::quanzhi::audit_core::common::model::RawFileInfo* file);
  ::com::quanzhi::audit_core::common::model::RawFileInfo* unsafe_arena_release_file();

  // .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 6;
  bool has_uniqueid() const;
  private:
  bool _internal_has_uniqueid() const;
  public:
  void clear_uniqueid();
  const ::com::quanzhi::audit_core::common::model::UniqueId& uniqueid() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::UniqueId* release_uniqueid();
  ::com::quanzhi::audit_core::common::model::UniqueId* mutable_uniqueid();
  void set_allocated_uniqueid(::com::quanzhi::audit_core::common::model::UniqueId* uniqueid);
  private:
  const ::com::quanzhi::audit_core::common::model::UniqueId& _internal_uniqueid() const;
  ::com::quanzhi::audit_core::common::model::UniqueId* _internal_mutable_uniqueid();
  public:
  void unsafe_arena_set_allocated_uniqueid(
      ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid);
  ::com::quanzhi::audit_core::common::model::UniqueId* unsafe_arena_release_uniqueid();

  // .com.quanzhi.audit_core.common.model.Source source = 8;
  bool has_source() const;
  private:
  bool _internal_has_source() const;
  public:
  void clear_source();
  const ::com::quanzhi::audit_core::common::model::Source& source() const;
  PROTOBUF_NODISCARD ::com::quanzhi::audit_core::common::model::Source* release_source();
  ::com::quanzhi::audit_core::common::model::Source* mutable_source();
  void set_allocated_source(::com::quanzhi::audit_core::common::model::Source* source);
  private:
  const ::com::quanzhi::audit_core::common::model::Source& _internal_source() const;
  ::com::quanzhi::audit_core::common::model::Source* _internal_mutable_source();
  public:
  void unsafe_arena_set_allocated_source(
      ::com::quanzhi::audit_core::common::model::Source* source);
  ::com::quanzhi::audit_core::common::model::Source* unsafe_arena_release_source();

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr charset_;
  ::com::quanzhi::audit_core::common::model::HttpRequest* req_;
  ::com::quanzhi::audit_core::common::model::HttpResponse* rsp_;
  ::com::quanzhi::audit_core::common::model::Meta* meta_;
  ::com::quanzhi::audit_core::common::model::Net* net_;
  ::com::quanzhi::audit_core::common::model::RawFileInfo* file_;
  ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid_;
  ::com::quanzhi::audit_core::common::model::Source* source_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class Meta final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Meta) */ {
 public:
  inline Meta() : Meta(nullptr) {}
  ~Meta() override;
  explicit PROTOBUF_CONSTEXPR Meta(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Meta(const Meta& from);
  Meta(Meta&& from) noexcept
    : Meta() {
    *this = ::std::move(from);
  }

  inline Meta& operator=(const Meta& from) {
    CopyFrom(from);
    return *this;
  }
  inline Meta& operator=(Meta&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Meta& default_instance() {
    return *internal_default_instance();
  }
  static inline const Meta* internal_default_instance() {
    return reinterpret_cast<const Meta*>(
               &_Meta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Meta& a, Meta& b) {
    a.Swap(&b);
  }
  inline void Swap(Meta* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Meta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Meta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Meta>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Meta& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Meta& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Meta* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Meta";
  }
  protected:
  explicit Meta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTmFieldNumber = 1,
  };
  // double tm = 1;
  void clear_tm();
  double tm() const;
  void set_tm(double value);
  private:
  double _internal_tm() const;
  void _internal_set_tm(double value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Meta)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double tm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class UniqueId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.UniqueId) */ {
 public:
  inline UniqueId() : UniqueId(nullptr) {}
  ~UniqueId() override;
  explicit PROTOBUF_CONSTEXPR UniqueId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UniqueId(const UniqueId& from);
  UniqueId(UniqueId&& from) noexcept
    : UniqueId() {
    *this = ::std::move(from);
  }

  inline UniqueId& operator=(const UniqueId& from) {
    CopyFrom(from);
    return *this;
  }
  inline UniqueId& operator=(UniqueId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UniqueId& default_instance() {
    return *internal_default_instance();
  }
  static inline const UniqueId* internal_default_instance() {
    return reinterpret_cast<const UniqueId*>(
               &_UniqueId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UniqueId& a, UniqueId& b) {
    a.Swap(&b);
  }
  inline void Swap(UniqueId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UniqueId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UniqueId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UniqueId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UniqueId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UniqueId& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UniqueId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.UniqueId";
  }
  protected:
  explicit UniqueId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventIdFieldNumber = 1,
  };
  // string eventId = 1;
  void clear_eventid();
  const std::string& eventid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_eventid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_eventid();
  PROTOBUF_NODISCARD std::string* release_eventid();
  void set_allocated_eventid(std::string* eventid);
  private:
  const std::string& _internal_eventid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_eventid(const std::string& value);
  std::string* _internal_mutable_eventid();
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.UniqueId)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr eventid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class HttpRequest_HeaderEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HttpRequest_HeaderEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HttpRequest_HeaderEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  HttpRequest_HeaderEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR HttpRequest_HeaderEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HttpRequest_HeaderEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HttpRequest_HeaderEntry_DoNotUse& other);
  static const HttpRequest_HeaderEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HttpRequest_HeaderEntry_DoNotUse*>(&_HttpRequest_HeaderEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "com.quanzhi.audit_core.common.model.HttpRequest.HeaderEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "com.quanzhi.audit_core.common.model.HttpRequest.HeaderEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};

// -------------------------------------------------------------------

class HttpRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.HttpRequest) */ {
 public:
  inline HttpRequest() : HttpRequest(nullptr) {}
  ~HttpRequest() override;
  explicit PROTOBUF_CONSTEXPR HttpRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HttpRequest(const HttpRequest& from);
  HttpRequest(HttpRequest&& from) noexcept
    : HttpRequest() {
    *this = ::std::move(from);
  }

  inline HttpRequest& operator=(const HttpRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline HttpRequest& operator=(HttpRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HttpRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const HttpRequest* internal_default_instance() {
    return reinterpret_cast<const HttpRequest*>(
               &_HttpRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(HttpRequest& a, HttpRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(HttpRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HttpRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HttpRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HttpRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HttpRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HttpRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HttpRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.HttpRequest";
  }
  protected:
  explicit HttpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kHeaderFieldNumber = 5,
    kBodyFieldNumber = 1,
    kRemoteAddrFieldNumber = 2,
    kUrlFieldNumber = 3,
    kHttpVersionFieldNumber = 4,
    kMethodFieldNumber = 6,
    kErrCodeFieldNumber = 7,
  };
  // map<string, string> header = 5;
  int header_size() const;
  private:
  int _internal_header_size() const;
  public:
  void clear_header();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_header() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_header();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      header() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_header();

  // bytes body = 1;
  void clear_body();
  const std::string& body() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_body(ArgT0&& arg0, ArgT... args);
  std::string* mutable_body();
  PROTOBUF_NODISCARD std::string* release_body();
  void set_allocated_body(std::string* body);
  private:
  const std::string& _internal_body() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_body(const std::string& value);
  std::string* _internal_mutable_body();
  public:

  // string remoteAddr = 2;
  void clear_remoteaddr();
  const std::string& remoteaddr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_remoteaddr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_remoteaddr();
  PROTOBUF_NODISCARD std::string* release_remoteaddr();
  void set_allocated_remoteaddr(std::string* remoteaddr);
  private:
  const std::string& _internal_remoteaddr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_remoteaddr(const std::string& value);
  std::string* _internal_mutable_remoteaddr();
  public:

  // string url = 3;
  void clear_url();
  const std::string& url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_url();
  PROTOBUF_NODISCARD std::string* release_url();
  void set_allocated_url(std::string* url);
  private:
  const std::string& _internal_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_url(const std::string& value);
  std::string* _internal_mutable_url();
  public:

  // string httpVersion = 4;
  void clear_httpversion();
  const std::string& httpversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_httpversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_httpversion();
  PROTOBUF_NODISCARD std::string* release_httpversion();
  void set_allocated_httpversion(std::string* httpversion);
  private:
  const std::string& _internal_httpversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_httpversion(const std::string& value);
  std::string* _internal_mutable_httpversion();
  public:

  // string method = 6;
  void clear_method();
  const std::string& method() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_method(ArgT0&& arg0, ArgT... args);
  std::string* mutable_method();
  PROTOBUF_NODISCARD std::string* release_method();
  void set_allocated_method(std::string* method);
  private:
  const std::string& _internal_method() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_method(const std::string& value);
  std::string* _internal_mutable_method();
  public:

  // int32 errCode = 7;
  void clear_errcode();
  int32_t errcode() const;
  void set_errcode(int32_t value);
  private:
  int32_t _internal_errcode() const;
  void _internal_set_errcode(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.HttpRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HttpRequest_HeaderEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> header_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr body_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr remoteaddr_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr url_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr httpversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr method_;
  int32_t errcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class HttpResponse_HeaderEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HttpResponse_HeaderEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HttpResponse_HeaderEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  HttpResponse_HeaderEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR HttpResponse_HeaderEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HttpResponse_HeaderEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HttpResponse_HeaderEntry_DoNotUse& other);
  static const HttpResponse_HeaderEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HttpResponse_HeaderEntry_DoNotUse*>(&_HttpResponse_HeaderEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "com.quanzhi.audit_core.common.model.HttpResponse.HeaderEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "com.quanzhi.audit_core.common.model.HttpResponse.HeaderEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};

// -------------------------------------------------------------------

class HttpResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.HttpResponse) */ {
 public:
  inline HttpResponse() : HttpResponse(nullptr) {}
  ~HttpResponse() override;
  explicit PROTOBUF_CONSTEXPR HttpResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HttpResponse(const HttpResponse& from);
  HttpResponse(HttpResponse&& from) noexcept
    : HttpResponse() {
    *this = ::std::move(from);
  }

  inline HttpResponse& operator=(const HttpResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline HttpResponse& operator=(HttpResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HttpResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const HttpResponse* internal_default_instance() {
    return reinterpret_cast<const HttpResponse*>(
               &_HttpResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(HttpResponse& a, HttpResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(HttpResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HttpResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HttpResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HttpResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HttpResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HttpResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HttpResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.HttpResponse";
  }
  protected:
  explicit HttpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kHeaderFieldNumber = 3,
    kSetCookiesListFieldNumber = 5,
    kStatusFieldNumber = 1,
    kHttpVersionFieldNumber = 2,
    kBodyFieldNumber = 4,
    kErrCodeFieldNumber = 6,
  };
  // map<string, string> header = 3;
  int header_size() const;
  private:
  int _internal_header_size() const;
  public:
  void clear_header();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_header() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_header();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      header() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_header();

  // repeated bytes setCookiesList = 5;
  int setcookieslist_size() const;
  private:
  int _internal_setcookieslist_size() const;
  public:
  void clear_setcookieslist();
  const std::string& setcookieslist(int index) const;
  std::string* mutable_setcookieslist(int index);
  void set_setcookieslist(int index, const std::string& value);
  void set_setcookieslist(int index, std::string&& value);
  void set_setcookieslist(int index, const char* value);
  void set_setcookieslist(int index, const void* value, size_t size);
  std::string* add_setcookieslist();
  void add_setcookieslist(const std::string& value);
  void add_setcookieslist(std::string&& value);
  void add_setcookieslist(const char* value);
  void add_setcookieslist(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& setcookieslist() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_setcookieslist();
  private:
  const std::string& _internal_setcookieslist(int index) const;
  std::string* _internal_add_setcookieslist();
  public:

  // string status = 1;
  void clear_status();
  const std::string& status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status();
  PROTOBUF_NODISCARD std::string* release_status();
  void set_allocated_status(std::string* status);
  private:
  const std::string& _internal_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status(const std::string& value);
  std::string* _internal_mutable_status();
  public:

  // string httpVersion = 2;
  void clear_httpversion();
  const std::string& httpversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_httpversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_httpversion();
  PROTOBUF_NODISCARD std::string* release_httpversion();
  void set_allocated_httpversion(std::string* httpversion);
  private:
  const std::string& _internal_httpversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_httpversion(const std::string& value);
  std::string* _internal_mutable_httpversion();
  public:

  // bytes body = 4;
  void clear_body();
  const std::string& body() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_body(ArgT0&& arg0, ArgT... args);
  std::string* mutable_body();
  PROTOBUF_NODISCARD std::string* release_body();
  void set_allocated_body(std::string* body);
  private:
  const std::string& _internal_body() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_body(const std::string& value);
  std::string* _internal_mutable_body();
  public:

  // int32 errCode = 6;
  void clear_errcode();
  int32_t errcode() const;
  void set_errcode(int32_t value);
  private:
  int32_t _internal_errcode() const;
  void _internal_set_errcode(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.HttpResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HttpResponse_HeaderEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> header_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> setcookieslist_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr httpversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr body_;
  int32_t errcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class Source final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Source) */ {
 public:
  inline Source() : Source(nullptr) {}
  ~Source() override;
  explicit PROTOBUF_CONSTEXPR Source(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Source(const Source& from);
  Source(Source&& from) noexcept
    : Source() {
    *this = ::std::move(from);
  }

  inline Source& operator=(const Source& from) {
    CopyFrom(from);
    return *this;
  }
  inline Source& operator=(Source&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Source& default_instance() {
    return *internal_default_instance();
  }
  static inline const Source* internal_default_instance() {
    return reinterpret_cast<const Source*>(
               &_Source_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Source& a, Source& b) {
    a.Swap(&b);
  }
  inline void Swap(Source* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Source* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Source* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Source>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Source& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Source& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Source* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Source";
  }
  protected:
  explicit Source(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskIdFieldNumber = 2,
    kAppFieldNumber = 3,
    kSourceTypeFieldNumber = 1,
  };
  // string taskId = 2;
  void clear_taskid();
  const std::string& taskid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_taskid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_taskid();
  PROTOBUF_NODISCARD std::string* release_taskid();
  void set_allocated_taskid(std::string* taskid);
  private:
  const std::string& _internal_taskid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_taskid(const std::string& value);
  std::string* _internal_mutable_taskid();
  public:

  // string app = 3;
  void clear_app();
  const std::string& app() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_app(ArgT0&& arg0, ArgT... args);
  std::string* mutable_app();
  PROTOBUF_NODISCARD std::string* release_app();
  void set_allocated_app(std::string* app);
  private:
  const std::string& _internal_app() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_app(const std::string& value);
  std::string* _internal_mutable_app();
  public:

  // .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
  void clear_sourcetype();
  ::com::quanzhi::audit_core::common::model::SourceTypeEnum sourcetype() const;
  void set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value);
  private:
  ::com::quanzhi::audit_core::common::model::SourceTypeEnum _internal_sourcetype() const;
  void _internal_set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Source)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr taskid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr app_;
  int sourcetype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class RawFileInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.RawFileInfo) */ {
 public:
  inline RawFileInfo() : RawFileInfo(nullptr) {}
  ~RawFileInfo() override;
  explicit PROTOBUF_CONSTEXPR RawFileInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RawFileInfo(const RawFileInfo& from);
  RawFileInfo(RawFileInfo&& from) noexcept
    : RawFileInfo() {
    *this = ::std::move(from);
  }

  inline RawFileInfo& operator=(const RawFileInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RawFileInfo& operator=(RawFileInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RawFileInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RawFileInfo* internal_default_instance() {
    return reinterpret_cast<const RawFileInfo*>(
               &_RawFileInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(RawFileInfo& a, RawFileInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RawFileInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RawFileInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RawFileInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RawFileInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RawFileInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RawFileInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RawFileInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.RawFileInfo";
  }
  protected:
  explicit RawFileInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFileDirectionFieldNumber = 1,
    kFileNameFieldNumber = 4,
    kFileTypeFieldNumber = 5,
    kFileWarnFieldNumber = 8,
    kUploadDirFieldNumber = 10,
    kSha256FieldNumber = 11,
    kRwFlagFieldNumber = 2,
    kIsIncompleteFieldNumber = 3,
    kFileLenFieldNumber = 7,
    kFileTypeReliableFieldNumber = 6,
    kUploadFlagFieldNumber = 9,
  };
  // string fileDirection = 1;
  void clear_filedirection();
  const std::string& filedirection() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filedirection(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filedirection();
  PROTOBUF_NODISCARD std::string* release_filedirection();
  void set_allocated_filedirection(std::string* filedirection);
  private:
  const std::string& _internal_filedirection() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filedirection(const std::string& value);
  std::string* _internal_mutable_filedirection();
  public:

  // string fileName = 4;
  void clear_filename();
  const std::string& filename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filename();
  PROTOBUF_NODISCARD std::string* release_filename();
  void set_allocated_filename(std::string* filename);
  private:
  const std::string& _internal_filename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filename(const std::string& value);
  std::string* _internal_mutable_filename();
  public:

  // string fileType = 5;
  void clear_filetype();
  const std::string& filetype() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filetype(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filetype();
  PROTOBUF_NODISCARD std::string* release_filetype();
  void set_allocated_filetype(std::string* filetype);
  private:
  const std::string& _internal_filetype() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filetype(const std::string& value);
  std::string* _internal_mutable_filetype();
  public:

  // string fileWarn = 8;
  void clear_filewarn();
  const std::string& filewarn() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filewarn(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filewarn();
  PROTOBUF_NODISCARD std::string* release_filewarn();
  void set_allocated_filewarn(std::string* filewarn);
  private:
  const std::string& _internal_filewarn() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filewarn(const std::string& value);
  std::string* _internal_mutable_filewarn();
  public:

  // string uploadDir = 10;
  void clear_uploaddir();
  const std::string& uploaddir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uploaddir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uploaddir();
  PROTOBUF_NODISCARD std::string* release_uploaddir();
  void set_allocated_uploaddir(std::string* uploaddir);
  private:
  const std::string& _internal_uploaddir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uploaddir(const std::string& value);
  std::string* _internal_mutable_uploaddir();
  public:

  // string sha256 = 11;
  void clear_sha256();
  const std::string& sha256() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sha256(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sha256();
  PROTOBUF_NODISCARD std::string* release_sha256();
  void set_allocated_sha256(std::string* sha256);
  private:
  const std::string& _internal_sha256() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sha256(const std::string& value);
  std::string* _internal_mutable_sha256();
  public:

  // int32 rwFlag = 2;
  void clear_rwflag();
  int32_t rwflag() const;
  void set_rwflag(int32_t value);
  private:
  int32_t _internal_rwflag() const;
  void _internal_set_rwflag(int32_t value);
  public:

  // int32 isIncomplete = 3;
  void clear_isincomplete();
  int32_t isincomplete() const;
  void set_isincomplete(int32_t value);
  private:
  int32_t _internal_isincomplete() const;
  void _internal_set_isincomplete(int32_t value);
  public:

  // int64 fileLen = 7;
  void clear_filelen();
  int64_t filelen() const;
  void set_filelen(int64_t value);
  private:
  int64_t _internal_filelen() const;
  void _internal_set_filelen(int64_t value);
  public:

  // int32 fileTypeReliable = 6;
  void clear_filetypereliable();
  int32_t filetypereliable() const;
  void set_filetypereliable(int32_t value);
  private:
  int32_t _internal_filetypereliable() const;
  void _internal_set_filetypereliable(int32_t value);
  public:

  // int32 uploadFlag = 9;
  void clear_uploadflag();
  int32_t uploadflag() const;
  void set_uploadflag(int32_t value);
  private:
  int32_t _internal_uploadflag() const;
  void _internal_set_uploadflag(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.RawFileInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filedirection_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filename_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filetype_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filewarn_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uploaddir_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sha256_;
  int32_t rwflag_;
  int32_t isincomplete_;
  int64_t filelen_;
  int32_t filetypereliable_;
  int32_t uploadflag_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// -------------------------------------------------------------------

class Net final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:com.quanzhi.audit_core.common.model.Net) */ {
 public:
  inline Net() : Net(nullptr) {}
  ~Net() override;
  explicit PROTOBUF_CONSTEXPR Net(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Net(const Net& from);
  Net(Net&& from) noexcept
    : Net() {
    *this = ::std::move(from);
  }

  inline Net& operator=(const Net& from) {
    CopyFrom(from);
    return *this;
  }
  inline Net& operator=(Net&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Net& default_instance() {
    return *internal_default_instance();
  }
  static inline const Net* internal_default_instance() {
    return reinterpret_cast<const Net*>(
               &_Net_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Net& a, Net& b) {
    a.Swap(&b);
  }
  inline void Swap(Net* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Net* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Net* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Net>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Net& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Net& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Net* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "com.quanzhi.audit_core.common.model.Net";
  }
  protected:
  explicit Net(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSrcIpFieldNumber = 1,
    kDstIpFieldNumber = 3,
    kSrcPortFieldNumber = 2,
    kDstPortFieldNumber = 4,
  };
  // string srcIp = 1;
  void clear_srcip();
  const std::string& srcip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_srcip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_srcip();
  PROTOBUF_NODISCARD std::string* release_srcip();
  void set_allocated_srcip(std::string* srcip);
  private:
  const std::string& _internal_srcip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_srcip(const std::string& value);
  std::string* _internal_mutable_srcip();
  public:

  // string dstIp = 3;
  void clear_dstip();
  const std::string& dstip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dstip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dstip();
  PROTOBUF_NODISCARD std::string* release_dstip();
  void set_allocated_dstip(std::string* dstip);
  private:
  const std::string& _internal_dstip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dstip(const std::string& value);
  std::string* _internal_mutable_dstip();
  public:

  // int32 srcPort = 2;
  void clear_srcport();
  int32_t srcport() const;
  void set_srcport(int32_t value);
  private:
  int32_t _internal_srcport() const;
  void _internal_set_srcport(int32_t value);
  public:

  // int32 dstPort = 4;
  void clear_dstport();
  int32_t dstport() const;
  void set_dstport(int32_t value);
  private:
  int32_t _internal_dstport() const;
  void _internal_set_dstport(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:com.quanzhi.audit_core.common.model.Net)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr srcip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dstip_;
  int32_t srcport_;
  int32_t dstport_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_ProtobufRawHttpEvent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProtobufRawHttpEvent

// .com.quanzhi.audit_core.common.model.HttpRequest req = 1;
inline bool ProtobufRawHttpEvent::_internal_has_req() const {
  return this != internal_default_instance() && req_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_req() const {
  return _internal_has_req();
}
inline void ProtobufRawHttpEvent::clear_req() {
  if (GetArenaForAllocation() == nullptr && req_ != nullptr) {
    delete req_;
  }
  req_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::HttpRequest& ProtobufRawHttpEvent::_internal_req() const {
  const ::com::quanzhi::audit_core::common::model::HttpRequest* p = req_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::HttpRequest&>(
      ::com::quanzhi::audit_core::common::model::_HttpRequest_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::HttpRequest& ProtobufRawHttpEvent::req() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.req)
  return _internal_req();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_req(
    ::com::quanzhi::audit_core::common::model::HttpRequest* req) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(req_);
  }
  req_ = req;
  if (req) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.req)
}
inline ::com::quanzhi::audit_core::common::model::HttpRequest* ProtobufRawHttpEvent::release_req() {
  
  ::com::quanzhi::audit_core::common::model::HttpRequest* temp = req_;
  req_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::HttpRequest* ProtobufRawHttpEvent::unsafe_arena_release_req() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.req)
  
  ::com::quanzhi::audit_core::common::model::HttpRequest* temp = req_;
  req_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::HttpRequest* ProtobufRawHttpEvent::_internal_mutable_req() {
  
  if (req_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpRequest>(GetArenaForAllocation());
    req_ = p;
  }
  return req_;
}
inline ::com::quanzhi::audit_core::common::model::HttpRequest* ProtobufRawHttpEvent::mutable_req() {
  ::com::quanzhi::audit_core::common::model::HttpRequest* _msg = _internal_mutable_req();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.req)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_req(::com::quanzhi::audit_core::common::model::HttpRequest* req) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete req_;
  }
  if (req) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(req);
    if (message_arena != submessage_arena) {
      req = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, req, submessage_arena);
    }
    
  } else {
    
  }
  req_ = req;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.req)
}

// .com.quanzhi.audit_core.common.model.HttpResponse rsp = 2;
inline bool ProtobufRawHttpEvent::_internal_has_rsp() const {
  return this != internal_default_instance() && rsp_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_rsp() const {
  return _internal_has_rsp();
}
inline void ProtobufRawHttpEvent::clear_rsp() {
  if (GetArenaForAllocation() == nullptr && rsp_ != nullptr) {
    delete rsp_;
  }
  rsp_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::HttpResponse& ProtobufRawHttpEvent::_internal_rsp() const {
  const ::com::quanzhi::audit_core::common::model::HttpResponse* p = rsp_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::HttpResponse&>(
      ::com::quanzhi::audit_core::common::model::_HttpResponse_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::HttpResponse& ProtobufRawHttpEvent::rsp() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.rsp)
  return _internal_rsp();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_rsp(
    ::com::quanzhi::audit_core::common::model::HttpResponse* rsp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rsp_);
  }
  rsp_ = rsp;
  if (rsp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.rsp)
}
inline ::com::quanzhi::audit_core::common::model::HttpResponse* ProtobufRawHttpEvent::release_rsp() {
  
  ::com::quanzhi::audit_core::common::model::HttpResponse* temp = rsp_;
  rsp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::HttpResponse* ProtobufRawHttpEvent::unsafe_arena_release_rsp() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.rsp)
  
  ::com::quanzhi::audit_core::common::model::HttpResponse* temp = rsp_;
  rsp_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::HttpResponse* ProtobufRawHttpEvent::_internal_mutable_rsp() {
  
  if (rsp_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::HttpResponse>(GetArenaForAllocation());
    rsp_ = p;
  }
  return rsp_;
}
inline ::com::quanzhi::audit_core::common::model::HttpResponse* ProtobufRawHttpEvent::mutable_rsp() {
  ::com::quanzhi::audit_core::common::model::HttpResponse* _msg = _internal_mutable_rsp();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.rsp)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_rsp(::com::quanzhi::audit_core::common::model::HttpResponse* rsp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete rsp_;
  }
  if (rsp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rsp);
    if (message_arena != submessage_arena) {
      rsp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rsp, submessage_arena);
    }
    
  } else {
    
  }
  rsp_ = rsp;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.rsp)
}

// .com.quanzhi.audit_core.common.model.Meta meta = 3;
inline bool ProtobufRawHttpEvent::_internal_has_meta() const {
  return this != internal_default_instance() && meta_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_meta() const {
  return _internal_has_meta();
}
inline void ProtobufRawHttpEvent::clear_meta() {
  if (GetArenaForAllocation() == nullptr && meta_ != nullptr) {
    delete meta_;
  }
  meta_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Meta& ProtobufRawHttpEvent::_internal_meta() const {
  const ::com::quanzhi::audit_core::common::model::Meta* p = meta_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Meta&>(
      ::com::quanzhi::audit_core::common::model::_Meta_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Meta& ProtobufRawHttpEvent::meta() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.meta)
  return _internal_meta();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_meta(
    ::com::quanzhi::audit_core::common::model::Meta* meta) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(meta_);
  }
  meta_ = meta;
  if (meta) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.meta)
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawHttpEvent::release_meta() {
  
  ::com::quanzhi::audit_core::common::model::Meta* temp = meta_;
  meta_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawHttpEvent::unsafe_arena_release_meta() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.meta)
  
  ::com::quanzhi::audit_core::common::model::Meta* temp = meta_;
  meta_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawHttpEvent::_internal_mutable_meta() {
  
  if (meta_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Meta>(GetArenaForAllocation());
    meta_ = p;
  }
  return meta_;
}
inline ::com::quanzhi::audit_core::common::model::Meta* ProtobufRawHttpEvent::mutable_meta() {
  ::com::quanzhi::audit_core::common::model::Meta* _msg = _internal_mutable_meta();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.meta)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_meta(::com::quanzhi::audit_core::common::model::Meta* meta) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete meta_;
  }
  if (meta) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(meta);
    if (message_arena != submessage_arena) {
      meta = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, meta, submessage_arena);
    }
    
  } else {
    
  }
  meta_ = meta;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.meta)
}

// .com.quanzhi.audit_core.common.model.Net net = 4;
inline bool ProtobufRawHttpEvent::_internal_has_net() const {
  return this != internal_default_instance() && net_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_net() const {
  return _internal_has_net();
}
inline void ProtobufRawHttpEvent::clear_net() {
  if (GetArenaForAllocation() == nullptr && net_ != nullptr) {
    delete net_;
  }
  net_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Net& ProtobufRawHttpEvent::_internal_net() const {
  const ::com::quanzhi::audit_core::common::model::Net* p = net_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Net&>(
      ::com::quanzhi::audit_core::common::model::_Net_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Net& ProtobufRawHttpEvent::net() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.net)
  return _internal_net();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_net(
    ::com::quanzhi::audit_core::common::model::Net* net) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(net_);
  }
  net_ = net;
  if (net) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.net)
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawHttpEvent::release_net() {
  
  ::com::quanzhi::audit_core::common::model::Net* temp = net_;
  net_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawHttpEvent::unsafe_arena_release_net() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.net)
  
  ::com::quanzhi::audit_core::common::model::Net* temp = net_;
  net_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawHttpEvent::_internal_mutable_net() {
  
  if (net_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Net>(GetArenaForAllocation());
    net_ = p;
  }
  return net_;
}
inline ::com::quanzhi::audit_core::common::model::Net* ProtobufRawHttpEvent::mutable_net() {
  ::com::quanzhi::audit_core::common::model::Net* _msg = _internal_mutable_net();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.net)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_net(::com::quanzhi::audit_core::common::model::Net* net) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete net_;
  }
  if (net) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(net);
    if (message_arena != submessage_arena) {
      net = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, net, submessage_arena);
    }
    
  } else {
    
  }
  net_ = net;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.net)
}

// .com.quanzhi.audit_core.common.model.RawFileInfo file = 5;
inline bool ProtobufRawHttpEvent::_internal_has_file() const {
  return this != internal_default_instance() && file_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_file() const {
  return _internal_has_file();
}
inline void ProtobufRawHttpEvent::clear_file() {
  if (GetArenaForAllocation() == nullptr && file_ != nullptr) {
    delete file_;
  }
  file_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::RawFileInfo& ProtobufRawHttpEvent::_internal_file() const {
  const ::com::quanzhi::audit_core::common::model::RawFileInfo* p = file_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::RawFileInfo&>(
      ::com::quanzhi::audit_core::common::model::_RawFileInfo_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::RawFileInfo& ProtobufRawHttpEvent::file() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.file)
  return _internal_file();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_file(
    ::com::quanzhi::audit_core::common::model::RawFileInfo* file) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(file_);
  }
  file_ = file;
  if (file) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.file)
}
inline ::com::quanzhi::audit_core::common::model::RawFileInfo* ProtobufRawHttpEvent::release_file() {
  
  ::com::quanzhi::audit_core::common::model::RawFileInfo* temp = file_;
  file_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::RawFileInfo* ProtobufRawHttpEvent::unsafe_arena_release_file() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.file)
  
  ::com::quanzhi::audit_core::common::model::RawFileInfo* temp = file_;
  file_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::RawFileInfo* ProtobufRawHttpEvent::_internal_mutable_file() {
  
  if (file_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::RawFileInfo>(GetArenaForAllocation());
    file_ = p;
  }
  return file_;
}
inline ::com::quanzhi::audit_core::common::model::RawFileInfo* ProtobufRawHttpEvent::mutable_file() {
  ::com::quanzhi::audit_core::common::model::RawFileInfo* _msg = _internal_mutable_file();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.file)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_file(::com::quanzhi::audit_core::common::model::RawFileInfo* file) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete file_;
  }
  if (file) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(file);
    if (message_arena != submessage_arena) {
      file = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, file, submessage_arena);
    }
    
  } else {
    
  }
  file_ = file;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.file)
}

// .com.quanzhi.audit_core.common.model.UniqueId uniqueId = 6;
inline bool ProtobufRawHttpEvent::_internal_has_uniqueid() const {
  return this != internal_default_instance() && uniqueid_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_uniqueid() const {
  return _internal_has_uniqueid();
}
inline void ProtobufRawHttpEvent::clear_uniqueid() {
  if (GetArenaForAllocation() == nullptr && uniqueid_ != nullptr) {
    delete uniqueid_;
  }
  uniqueid_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::UniqueId& ProtobufRawHttpEvent::_internal_uniqueid() const {
  const ::com::quanzhi::audit_core::common::model::UniqueId* p = uniqueid_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::UniqueId&>(
      ::com::quanzhi::audit_core::common::model::_UniqueId_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::UniqueId& ProtobufRawHttpEvent::uniqueid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.uniqueId)
  return _internal_uniqueid();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_uniqueid(
    ::com::quanzhi::audit_core::common::model::UniqueId* uniqueid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(uniqueid_);
  }
  uniqueid_ = uniqueid;
  if (uniqueid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.uniqueId)
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawHttpEvent::release_uniqueid() {
  
  ::com::quanzhi::audit_core::common::model::UniqueId* temp = uniqueid_;
  uniqueid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawHttpEvent::unsafe_arena_release_uniqueid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.uniqueId)
  
  ::com::quanzhi::audit_core::common::model::UniqueId* temp = uniqueid_;
  uniqueid_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawHttpEvent::_internal_mutable_uniqueid() {
  
  if (uniqueid_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::UniqueId>(GetArenaForAllocation());
    uniqueid_ = p;
  }
  return uniqueid_;
}
inline ::com::quanzhi::audit_core::common::model::UniqueId* ProtobufRawHttpEvent::mutable_uniqueid() {
  ::com::quanzhi::audit_core::common::model::UniqueId* _msg = _internal_mutable_uniqueid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.uniqueId)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_uniqueid(::com::quanzhi::audit_core::common::model::UniqueId* uniqueid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete uniqueid_;
  }
  if (uniqueid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(uniqueid);
    if (message_arena != submessage_arena) {
      uniqueid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, uniqueid, submessage_arena);
    }
    
  } else {
    
  }
  uniqueid_ = uniqueid;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.uniqueId)
}

// string charset = 7;
inline void ProtobufRawHttpEvent::clear_charset() {
  charset_.ClearToEmpty();
}
inline const std::string& ProtobufRawHttpEvent::charset() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset)
  return _internal_charset();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProtobufRawHttpEvent::set_charset(ArgT0&& arg0, ArgT... args) {
 
 charset_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset)
}
inline std::string* ProtobufRawHttpEvent::mutable_charset() {
  std::string* _s = _internal_mutable_charset();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset)
  return _s;
}
inline const std::string& ProtobufRawHttpEvent::_internal_charset() const {
  return charset_.Get();
}
inline void ProtobufRawHttpEvent::_internal_set_charset(const std::string& value) {
  
  charset_.Set(value, GetArenaForAllocation());
}
inline std::string* ProtobufRawHttpEvent::_internal_mutable_charset() {
  
  return charset_.Mutable(GetArenaForAllocation());
}
inline std::string* ProtobufRawHttpEvent::release_charset() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset)
  return charset_.Release();
}
inline void ProtobufRawHttpEvent::set_allocated_charset(std::string* charset) {
  if (charset != nullptr) {
    
  } else {
    
  }
  charset_.SetAllocated(charset, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (charset_.IsDefault()) {
    charset_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.charset)
}

// .com.quanzhi.audit_core.common.model.Source source = 8;
inline bool ProtobufRawHttpEvent::_internal_has_source() const {
  return this != internal_default_instance() && source_ != nullptr;
}
inline bool ProtobufRawHttpEvent::has_source() const {
  return _internal_has_source();
}
inline void ProtobufRawHttpEvent::clear_source() {
  if (GetArenaForAllocation() == nullptr && source_ != nullptr) {
    delete source_;
  }
  source_ = nullptr;
}
inline const ::com::quanzhi::audit_core::common::model::Source& ProtobufRawHttpEvent::_internal_source() const {
  const ::com::quanzhi::audit_core::common::model::Source* p = source_;
  return p != nullptr ? *p : reinterpret_cast<const ::com::quanzhi::audit_core::common::model::Source&>(
      ::com::quanzhi::audit_core::common::model::_Source_default_instance_);
}
inline const ::com::quanzhi::audit_core::common::model::Source& ProtobufRawHttpEvent::source() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.source)
  return _internal_source();
}
inline void ProtobufRawHttpEvent::unsafe_arena_set_allocated_source(
    ::com::quanzhi::audit_core::common::model::Source* source) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(source_);
  }
  source_ = source;
  if (source) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.source)
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawHttpEvent::release_source() {
  
  ::com::quanzhi::audit_core::common::model::Source* temp = source_;
  source_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawHttpEvent::unsafe_arena_release_source() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.source)
  
  ::com::quanzhi::audit_core::common::model::Source* temp = source_;
  source_ = nullptr;
  return temp;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawHttpEvent::_internal_mutable_source() {
  
  if (source_ == nullptr) {
    auto* p = CreateMaybeMessage<::com::quanzhi::audit_core::common::model::Source>(GetArenaForAllocation());
    source_ = p;
  }
  return source_;
}
inline ::com::quanzhi::audit_core::common::model::Source* ProtobufRawHttpEvent::mutable_source() {
  ::com::quanzhi::audit_core::common::model::Source* _msg = _internal_mutable_source();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.source)
  return _msg;
}
inline void ProtobufRawHttpEvent::set_allocated_source(::com::quanzhi::audit_core::common::model::Source* source) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete source_;
  }
  if (source) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source);
    if (message_arena != submessage_arena) {
      source = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source, submessage_arena);
    }
    
  } else {
    
  }
  source_ = source;
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.ProtobufRawHttpEvent.source)
}

// -------------------------------------------------------------------

// Meta

// double tm = 1;
inline void Meta::clear_tm() {
  tm_ = 0;
}
inline double Meta::_internal_tm() const {
  return tm_;
}
inline double Meta::tm() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Meta.tm)
  return _internal_tm();
}
inline void Meta::_internal_set_tm(double value) {
  
  tm_ = value;
}
inline void Meta::set_tm(double value) {
  _internal_set_tm(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Meta.tm)
}

// -------------------------------------------------------------------

// UniqueId

// string eventId = 1;
inline void UniqueId::clear_eventid() {
  eventid_.ClearToEmpty();
}
inline const std::string& UniqueId::eventid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return _internal_eventid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UniqueId::set_eventid(ArgT0&& arg0, ArgT... args) {
 
 eventid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.UniqueId.eventId)
}
inline std::string* UniqueId::mutable_eventid() {
  std::string* _s = _internal_mutable_eventid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return _s;
}
inline const std::string& UniqueId::_internal_eventid() const {
  return eventid_.Get();
}
inline void UniqueId::_internal_set_eventid(const std::string& value) {
  
  eventid_.Set(value, GetArenaForAllocation());
}
inline std::string* UniqueId::_internal_mutable_eventid() {
  
  return eventid_.Mutable(GetArenaForAllocation());
}
inline std::string* UniqueId::release_eventid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.UniqueId.eventId)
  return eventid_.Release();
}
inline void UniqueId::set_allocated_eventid(std::string* eventid) {
  if (eventid != nullptr) {
    
  } else {
    
  }
  eventid_.SetAllocated(eventid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (eventid_.IsDefault()) {
    eventid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.UniqueId.eventId)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HttpRequest

// bytes body = 1;
inline void HttpRequest::clear_body() {
  body_.ClearToEmpty();
}
inline const std::string& HttpRequest::body() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.body)
  return _internal_body();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpRequest::set_body(ArgT0&& arg0, ArgT... args) {
 
 body_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.body)
}
inline std::string* HttpRequest::mutable_body() {
  std::string* _s = _internal_mutable_body();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpRequest.body)
  return _s;
}
inline const std::string& HttpRequest::_internal_body() const {
  return body_.Get();
}
inline void HttpRequest::_internal_set_body(const std::string& value) {
  
  body_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpRequest::_internal_mutable_body() {
  
  return body_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpRequest::release_body() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpRequest.body)
  return body_.Release();
}
inline void HttpRequest::set_allocated_body(std::string* body) {
  if (body != nullptr) {
    
  } else {
    
  }
  body_.SetAllocated(body, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (body_.IsDefault()) {
    body_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpRequest.body)
}

// string remoteAddr = 2;
inline void HttpRequest::clear_remoteaddr() {
  remoteaddr_.ClearToEmpty();
}
inline const std::string& HttpRequest::remoteaddr() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr)
  return _internal_remoteaddr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpRequest::set_remoteaddr(ArgT0&& arg0, ArgT... args) {
 
 remoteaddr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr)
}
inline std::string* HttpRequest::mutable_remoteaddr() {
  std::string* _s = _internal_mutable_remoteaddr();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr)
  return _s;
}
inline const std::string& HttpRequest::_internal_remoteaddr() const {
  return remoteaddr_.Get();
}
inline void HttpRequest::_internal_set_remoteaddr(const std::string& value) {
  
  remoteaddr_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpRequest::_internal_mutable_remoteaddr() {
  
  return remoteaddr_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpRequest::release_remoteaddr() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr)
  return remoteaddr_.Release();
}
inline void HttpRequest::set_allocated_remoteaddr(std::string* remoteaddr) {
  if (remoteaddr != nullptr) {
    
  } else {
    
  }
  remoteaddr_.SetAllocated(remoteaddr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (remoteaddr_.IsDefault()) {
    remoteaddr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpRequest.remoteAddr)
}

// string url = 3;
inline void HttpRequest::clear_url() {
  url_.ClearToEmpty();
}
inline const std::string& HttpRequest::url() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.url)
  return _internal_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpRequest::set_url(ArgT0&& arg0, ArgT... args) {
 
 url_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.url)
}
inline std::string* HttpRequest::mutable_url() {
  std::string* _s = _internal_mutable_url();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpRequest.url)
  return _s;
}
inline const std::string& HttpRequest::_internal_url() const {
  return url_.Get();
}
inline void HttpRequest::_internal_set_url(const std::string& value) {
  
  url_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpRequest::_internal_mutable_url() {
  
  return url_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpRequest::release_url() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpRequest.url)
  return url_.Release();
}
inline void HttpRequest::set_allocated_url(std::string* url) {
  if (url != nullptr) {
    
  } else {
    
  }
  url_.SetAllocated(url, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (url_.IsDefault()) {
    url_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpRequest.url)
}

// string httpVersion = 4;
inline void HttpRequest::clear_httpversion() {
  httpversion_.ClearToEmpty();
}
inline const std::string& HttpRequest::httpversion() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.httpVersion)
  return _internal_httpversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpRequest::set_httpversion(ArgT0&& arg0, ArgT... args) {
 
 httpversion_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.httpVersion)
}
inline std::string* HttpRequest::mutable_httpversion() {
  std::string* _s = _internal_mutable_httpversion();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpRequest.httpVersion)
  return _s;
}
inline const std::string& HttpRequest::_internal_httpversion() const {
  return httpversion_.Get();
}
inline void HttpRequest::_internal_set_httpversion(const std::string& value) {
  
  httpversion_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpRequest::_internal_mutable_httpversion() {
  
  return httpversion_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpRequest::release_httpversion() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpRequest.httpVersion)
  return httpversion_.Release();
}
inline void HttpRequest::set_allocated_httpversion(std::string* httpversion) {
  if (httpversion != nullptr) {
    
  } else {
    
  }
  httpversion_.SetAllocated(httpversion, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (httpversion_.IsDefault()) {
    httpversion_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpRequest.httpVersion)
}

// map<string, string> header = 5;
inline int HttpRequest::_internal_header_size() const {
  return header_.size();
}
inline int HttpRequest::header_size() const {
  return _internal_header_size();
}
inline void HttpRequest::clear_header() {
  header_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HttpRequest::_internal_header() const {
  return header_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HttpRequest::header() const {
  // @@protoc_insertion_point(field_map:com.quanzhi.audit_core.common.model.HttpRequest.header)
  return _internal_header();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HttpRequest::_internal_mutable_header() {
  return header_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HttpRequest::mutable_header() {
  // @@protoc_insertion_point(field_mutable_map:com.quanzhi.audit_core.common.model.HttpRequest.header)
  return _internal_mutable_header();
}

// string method = 6;
inline void HttpRequest::clear_method() {
  method_.ClearToEmpty();
}
inline const std::string& HttpRequest::method() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.method)
  return _internal_method();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpRequest::set_method(ArgT0&& arg0, ArgT... args) {
 
 method_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.method)
}
inline std::string* HttpRequest::mutable_method() {
  std::string* _s = _internal_mutable_method();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpRequest.method)
  return _s;
}
inline const std::string& HttpRequest::_internal_method() const {
  return method_.Get();
}
inline void HttpRequest::_internal_set_method(const std::string& value) {
  
  method_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpRequest::_internal_mutable_method() {
  
  return method_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpRequest::release_method() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpRequest.method)
  return method_.Release();
}
inline void HttpRequest::set_allocated_method(std::string* method) {
  if (method != nullptr) {
    
  } else {
    
  }
  method_.SetAllocated(method, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (method_.IsDefault()) {
    method_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpRequest.method)
}

// int32 errCode = 7;
inline void HttpRequest::clear_errcode() {
  errcode_ = 0;
}
inline int32_t HttpRequest::_internal_errcode() const {
  return errcode_;
}
inline int32_t HttpRequest::errcode() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpRequest.errCode)
  return _internal_errcode();
}
inline void HttpRequest::_internal_set_errcode(int32_t value) {
  
  errcode_ = value;
}
inline void HttpRequest::set_errcode(int32_t value) {
  _internal_set_errcode(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpRequest.errCode)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HttpResponse

// string status = 1;
inline void HttpResponse::clear_status() {
  status_.ClearToEmpty();
}
inline const std::string& HttpResponse::status() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpResponse.status)
  return _internal_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpResponse::set_status(ArgT0&& arg0, ArgT... args) {
 
 status_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.status)
}
inline std::string* HttpResponse::mutable_status() {
  std::string* _s = _internal_mutable_status();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpResponse.status)
  return _s;
}
inline const std::string& HttpResponse::_internal_status() const {
  return status_.Get();
}
inline void HttpResponse::_internal_set_status(const std::string& value) {
  
  status_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpResponse::_internal_mutable_status() {
  
  return status_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpResponse::release_status() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpResponse.status)
  return status_.Release();
}
inline void HttpResponse::set_allocated_status(std::string* status) {
  if (status != nullptr) {
    
  } else {
    
  }
  status_.SetAllocated(status, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_.IsDefault()) {
    status_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpResponse.status)
}

// string httpVersion = 2;
inline void HttpResponse::clear_httpversion() {
  httpversion_.ClearToEmpty();
}
inline const std::string& HttpResponse::httpversion() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpResponse.httpVersion)
  return _internal_httpversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpResponse::set_httpversion(ArgT0&& arg0, ArgT... args) {
 
 httpversion_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.httpVersion)
}
inline std::string* HttpResponse::mutable_httpversion() {
  std::string* _s = _internal_mutable_httpversion();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpResponse.httpVersion)
  return _s;
}
inline const std::string& HttpResponse::_internal_httpversion() const {
  return httpversion_.Get();
}
inline void HttpResponse::_internal_set_httpversion(const std::string& value) {
  
  httpversion_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpResponse::_internal_mutable_httpversion() {
  
  return httpversion_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpResponse::release_httpversion() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpResponse.httpVersion)
  return httpversion_.Release();
}
inline void HttpResponse::set_allocated_httpversion(std::string* httpversion) {
  if (httpversion != nullptr) {
    
  } else {
    
  }
  httpversion_.SetAllocated(httpversion, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (httpversion_.IsDefault()) {
    httpversion_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpResponse.httpVersion)
}

// map<string, string> header = 3;
inline int HttpResponse::_internal_header_size() const {
  return header_.size();
}
inline int HttpResponse::header_size() const {
  return _internal_header_size();
}
inline void HttpResponse::clear_header() {
  header_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HttpResponse::_internal_header() const {
  return header_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HttpResponse::header() const {
  // @@protoc_insertion_point(field_map:com.quanzhi.audit_core.common.model.HttpResponse.header)
  return _internal_header();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HttpResponse::_internal_mutable_header() {
  return header_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HttpResponse::mutable_header() {
  // @@protoc_insertion_point(field_mutable_map:com.quanzhi.audit_core.common.model.HttpResponse.header)
  return _internal_mutable_header();
}

// bytes body = 4;
inline void HttpResponse::clear_body() {
  body_.ClearToEmpty();
}
inline const std::string& HttpResponse::body() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpResponse.body)
  return _internal_body();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HttpResponse::set_body(ArgT0&& arg0, ArgT... args) {
 
 body_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.body)
}
inline std::string* HttpResponse::mutable_body() {
  std::string* _s = _internal_mutable_body();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpResponse.body)
  return _s;
}
inline const std::string& HttpResponse::_internal_body() const {
  return body_.Get();
}
inline void HttpResponse::_internal_set_body(const std::string& value) {
  
  body_.Set(value, GetArenaForAllocation());
}
inline std::string* HttpResponse::_internal_mutable_body() {
  
  return body_.Mutable(GetArenaForAllocation());
}
inline std::string* HttpResponse::release_body() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.HttpResponse.body)
  return body_.Release();
}
inline void HttpResponse::set_allocated_body(std::string* body) {
  if (body != nullptr) {
    
  } else {
    
  }
  body_.SetAllocated(body, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (body_.IsDefault()) {
    body_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.HttpResponse.body)
}

// repeated bytes setCookiesList = 5;
inline int HttpResponse::_internal_setcookieslist_size() const {
  return setcookieslist_.size();
}
inline int HttpResponse::setcookieslist_size() const {
  return _internal_setcookieslist_size();
}
inline void HttpResponse::clear_setcookieslist() {
  setcookieslist_.Clear();
}
inline std::string* HttpResponse::add_setcookieslist() {
  std::string* _s = _internal_add_setcookieslist();
  // @@protoc_insertion_point(field_add_mutable:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
  return _s;
}
inline const std::string& HttpResponse::_internal_setcookieslist(int index) const {
  return setcookieslist_.Get(index);
}
inline const std::string& HttpResponse::setcookieslist(int index) const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
  return _internal_setcookieslist(index);
}
inline std::string* HttpResponse::mutable_setcookieslist(int index) {
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
  return setcookieslist_.Mutable(index);
}
inline void HttpResponse::set_setcookieslist(int index, const std::string& value) {
  setcookieslist_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::set_setcookieslist(int index, std::string&& value) {
  setcookieslist_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::set_setcookieslist(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  setcookieslist_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::set_setcookieslist(int index, const void* value, size_t size) {
  setcookieslist_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline std::string* HttpResponse::_internal_add_setcookieslist() {
  return setcookieslist_.Add();
}
inline void HttpResponse::add_setcookieslist(const std::string& value) {
  setcookieslist_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::add_setcookieslist(std::string&& value) {
  setcookieslist_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::add_setcookieslist(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  setcookieslist_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline void HttpResponse::add_setcookieslist(const void* value, size_t size) {
  setcookieslist_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
HttpResponse::setcookieslist() const {
  // @@protoc_insertion_point(field_list:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
  return setcookieslist_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
HttpResponse::mutable_setcookieslist() {
  // @@protoc_insertion_point(field_mutable_list:com.quanzhi.audit_core.common.model.HttpResponse.setCookiesList)
  return &setcookieslist_;
}

// int32 errCode = 6;
inline void HttpResponse::clear_errcode() {
  errcode_ = 0;
}
inline int32_t HttpResponse::_internal_errcode() const {
  return errcode_;
}
inline int32_t HttpResponse::errcode() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.HttpResponse.errCode)
  return _internal_errcode();
}
inline void HttpResponse::_internal_set_errcode(int32_t value) {
  
  errcode_ = value;
}
inline void HttpResponse::set_errcode(int32_t value) {
  _internal_set_errcode(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.HttpResponse.errCode)
}

// -------------------------------------------------------------------

// Source

// .com.quanzhi.audit_core.common.model.SourceTypeEnum sourceType = 1;
inline void Source::clear_sourcetype() {
  sourcetype_ = 0;
}
inline ::com::quanzhi::audit_core::common::model::SourceTypeEnum Source::_internal_sourcetype() const {
  return static_cast< ::com::quanzhi::audit_core::common::model::SourceTypeEnum >(sourcetype_);
}
inline ::com::quanzhi::audit_core::common::model::SourceTypeEnum Source::sourcetype() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.sourceType)
  return _internal_sourcetype();
}
inline void Source::_internal_set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value) {
  
  sourcetype_ = value;
}
inline void Source::set_sourcetype(::com::quanzhi::audit_core::common::model::SourceTypeEnum value) {
  _internal_set_sourcetype(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.sourceType)
}

// string taskId = 2;
inline void Source::clear_taskid() {
  taskid_.ClearToEmpty();
}
inline const std::string& Source::taskid() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.taskId)
  return _internal_taskid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Source::set_taskid(ArgT0&& arg0, ArgT... args) {
 
 taskid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.taskId)
}
inline std::string* Source::mutable_taskid() {
  std::string* _s = _internal_mutable_taskid();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Source.taskId)
  return _s;
}
inline const std::string& Source::_internal_taskid() const {
  return taskid_.Get();
}
inline void Source::_internal_set_taskid(const std::string& value) {
  
  taskid_.Set(value, GetArenaForAllocation());
}
inline std::string* Source::_internal_mutable_taskid() {
  
  return taskid_.Mutable(GetArenaForAllocation());
}
inline std::string* Source::release_taskid() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Source.taskId)
  return taskid_.Release();
}
inline void Source::set_allocated_taskid(std::string* taskid) {
  if (taskid != nullptr) {
    
  } else {
    
  }
  taskid_.SetAllocated(taskid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (taskid_.IsDefault()) {
    taskid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Source.taskId)
}

// string app = 3;
inline void Source::clear_app() {
  app_.ClearToEmpty();
}
inline const std::string& Source::app() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Source.app)
  return _internal_app();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Source::set_app(ArgT0&& arg0, ArgT... args) {
 
 app_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Source.app)
}
inline std::string* Source::mutable_app() {
  std::string* _s = _internal_mutable_app();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Source.app)
  return _s;
}
inline const std::string& Source::_internal_app() const {
  return app_.Get();
}
inline void Source::_internal_set_app(const std::string& value) {
  
  app_.Set(value, GetArenaForAllocation());
}
inline std::string* Source::_internal_mutable_app() {
  
  return app_.Mutable(GetArenaForAllocation());
}
inline std::string* Source::release_app() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Source.app)
  return app_.Release();
}
inline void Source::set_allocated_app(std::string* app) {
  if (app != nullptr) {
    
  } else {
    
  }
  app_.SetAllocated(app, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (app_.IsDefault()) {
    app_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Source.app)
}

// -------------------------------------------------------------------

// RawFileInfo

// string fileDirection = 1;
inline void RawFileInfo::clear_filedirection() {
  filedirection_.ClearToEmpty();
}
inline const std::string& RawFileInfo::filedirection() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection)
  return _internal_filedirection();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_filedirection(ArgT0&& arg0, ArgT... args) {
 
 filedirection_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection)
}
inline std::string* RawFileInfo::mutable_filedirection() {
  std::string* _s = _internal_mutable_filedirection();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection)
  return _s;
}
inline const std::string& RawFileInfo::_internal_filedirection() const {
  return filedirection_.Get();
}
inline void RawFileInfo::_internal_set_filedirection(const std::string& value) {
  
  filedirection_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_filedirection() {
  
  return filedirection_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_filedirection() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection)
  return filedirection_.Release();
}
inline void RawFileInfo::set_allocated_filedirection(std::string* filedirection) {
  if (filedirection != nullptr) {
    
  } else {
    
  }
  filedirection_.SetAllocated(filedirection, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (filedirection_.IsDefault()) {
    filedirection_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.fileDirection)
}

// int32 rwFlag = 2;
inline void RawFileInfo::clear_rwflag() {
  rwflag_ = 0;
}
inline int32_t RawFileInfo::_internal_rwflag() const {
  return rwflag_;
}
inline int32_t RawFileInfo::rwflag() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.rwFlag)
  return _internal_rwflag();
}
inline void RawFileInfo::_internal_set_rwflag(int32_t value) {
  
  rwflag_ = value;
}
inline void RawFileInfo::set_rwflag(int32_t value) {
  _internal_set_rwflag(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.rwFlag)
}

// int32 isIncomplete = 3;
inline void RawFileInfo::clear_isincomplete() {
  isincomplete_ = 0;
}
inline int32_t RawFileInfo::_internal_isincomplete() const {
  return isincomplete_;
}
inline int32_t RawFileInfo::isincomplete() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.isIncomplete)
  return _internal_isincomplete();
}
inline void RawFileInfo::_internal_set_isincomplete(int32_t value) {
  
  isincomplete_ = value;
}
inline void RawFileInfo::set_isincomplete(int32_t value) {
  _internal_set_isincomplete(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.isIncomplete)
}

// string fileName = 4;
inline void RawFileInfo::clear_filename() {
  filename_.ClearToEmpty();
}
inline const std::string& RawFileInfo::filename() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileName)
  return _internal_filename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_filename(ArgT0&& arg0, ArgT... args) {
 
 filename_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileName)
}
inline std::string* RawFileInfo::mutable_filename() {
  std::string* _s = _internal_mutable_filename();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.fileName)
  return _s;
}
inline const std::string& RawFileInfo::_internal_filename() const {
  return filename_.Get();
}
inline void RawFileInfo::_internal_set_filename(const std::string& value) {
  
  filename_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_filename() {
  
  return filename_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_filename() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.fileName)
  return filename_.Release();
}
inline void RawFileInfo::set_allocated_filename(std::string* filename) {
  if (filename != nullptr) {
    
  } else {
    
  }
  filename_.SetAllocated(filename, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (filename_.IsDefault()) {
    filename_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.fileName)
}

// string fileType = 5;
inline void RawFileInfo::clear_filetype() {
  filetype_.ClearToEmpty();
}
inline const std::string& RawFileInfo::filetype() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileType)
  return _internal_filetype();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_filetype(ArgT0&& arg0, ArgT... args) {
 
 filetype_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileType)
}
inline std::string* RawFileInfo::mutable_filetype() {
  std::string* _s = _internal_mutable_filetype();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.fileType)
  return _s;
}
inline const std::string& RawFileInfo::_internal_filetype() const {
  return filetype_.Get();
}
inline void RawFileInfo::_internal_set_filetype(const std::string& value) {
  
  filetype_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_filetype() {
  
  return filetype_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_filetype() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.fileType)
  return filetype_.Release();
}
inline void RawFileInfo::set_allocated_filetype(std::string* filetype) {
  if (filetype != nullptr) {
    
  } else {
    
  }
  filetype_.SetAllocated(filetype, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (filetype_.IsDefault()) {
    filetype_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.fileType)
}

// int32 fileTypeReliable = 6;
inline void RawFileInfo::clear_filetypereliable() {
  filetypereliable_ = 0;
}
inline int32_t RawFileInfo::_internal_filetypereliable() const {
  return filetypereliable_;
}
inline int32_t RawFileInfo::filetypereliable() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileTypeReliable)
  return _internal_filetypereliable();
}
inline void RawFileInfo::_internal_set_filetypereliable(int32_t value) {
  
  filetypereliable_ = value;
}
inline void RawFileInfo::set_filetypereliable(int32_t value) {
  _internal_set_filetypereliable(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileTypeReliable)
}

// int64 fileLen = 7;
inline void RawFileInfo::clear_filelen() {
  filelen_ = int64_t{0};
}
inline int64_t RawFileInfo::_internal_filelen() const {
  return filelen_;
}
inline int64_t RawFileInfo::filelen() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileLen)
  return _internal_filelen();
}
inline void RawFileInfo::_internal_set_filelen(int64_t value) {
  
  filelen_ = value;
}
inline void RawFileInfo::set_filelen(int64_t value) {
  _internal_set_filelen(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileLen)
}

// string fileWarn = 8;
inline void RawFileInfo::clear_filewarn() {
  filewarn_.ClearToEmpty();
}
inline const std::string& RawFileInfo::filewarn() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn)
  return _internal_filewarn();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_filewarn(ArgT0&& arg0, ArgT... args) {
 
 filewarn_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn)
}
inline std::string* RawFileInfo::mutable_filewarn() {
  std::string* _s = _internal_mutable_filewarn();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn)
  return _s;
}
inline const std::string& RawFileInfo::_internal_filewarn() const {
  return filewarn_.Get();
}
inline void RawFileInfo::_internal_set_filewarn(const std::string& value) {
  
  filewarn_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_filewarn() {
  
  return filewarn_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_filewarn() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn)
  return filewarn_.Release();
}
inline void RawFileInfo::set_allocated_filewarn(std::string* filewarn) {
  if (filewarn != nullptr) {
    
  } else {
    
  }
  filewarn_.SetAllocated(filewarn, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (filewarn_.IsDefault()) {
    filewarn_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.fileWarn)
}

// int32 uploadFlag = 9;
inline void RawFileInfo::clear_uploadflag() {
  uploadflag_ = 0;
}
inline int32_t RawFileInfo::_internal_uploadflag() const {
  return uploadflag_;
}
inline int32_t RawFileInfo::uploadflag() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.uploadFlag)
  return _internal_uploadflag();
}
inline void RawFileInfo::_internal_set_uploadflag(int32_t value) {
  
  uploadflag_ = value;
}
inline void RawFileInfo::set_uploadflag(int32_t value) {
  _internal_set_uploadflag(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.uploadFlag)
}

// string uploadDir = 10;
inline void RawFileInfo::clear_uploaddir() {
  uploaddir_.ClearToEmpty();
}
inline const std::string& RawFileInfo::uploaddir() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir)
  return _internal_uploaddir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_uploaddir(ArgT0&& arg0, ArgT... args) {
 
 uploaddir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir)
}
inline std::string* RawFileInfo::mutable_uploaddir() {
  std::string* _s = _internal_mutable_uploaddir();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir)
  return _s;
}
inline const std::string& RawFileInfo::_internal_uploaddir() const {
  return uploaddir_.Get();
}
inline void RawFileInfo::_internal_set_uploaddir(const std::string& value) {
  
  uploaddir_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_uploaddir() {
  
  return uploaddir_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_uploaddir() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir)
  return uploaddir_.Release();
}
inline void RawFileInfo::set_allocated_uploaddir(std::string* uploaddir) {
  if (uploaddir != nullptr) {
    
  } else {
    
  }
  uploaddir_.SetAllocated(uploaddir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uploaddir_.IsDefault()) {
    uploaddir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.uploadDir)
}

// string sha256 = 11;
inline void RawFileInfo::clear_sha256() {
  sha256_.ClearToEmpty();
}
inline const std::string& RawFileInfo::sha256() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.RawFileInfo.sha256)
  return _internal_sha256();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RawFileInfo::set_sha256(ArgT0&& arg0, ArgT... args) {
 
 sha256_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.RawFileInfo.sha256)
}
inline std::string* RawFileInfo::mutable_sha256() {
  std::string* _s = _internal_mutable_sha256();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.RawFileInfo.sha256)
  return _s;
}
inline const std::string& RawFileInfo::_internal_sha256() const {
  return sha256_.Get();
}
inline void RawFileInfo::_internal_set_sha256(const std::string& value) {
  
  sha256_.Set(value, GetArenaForAllocation());
}
inline std::string* RawFileInfo::_internal_mutable_sha256() {
  
  return sha256_.Mutable(GetArenaForAllocation());
}
inline std::string* RawFileInfo::release_sha256() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.RawFileInfo.sha256)
  return sha256_.Release();
}
inline void RawFileInfo::set_allocated_sha256(std::string* sha256) {
  if (sha256 != nullptr) {
    
  } else {
    
  }
  sha256_.SetAllocated(sha256, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (sha256_.IsDefault()) {
    sha256_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.RawFileInfo.sha256)
}

// -------------------------------------------------------------------

// Net

// string srcIp = 1;
inline void Net::clear_srcip() {
  srcip_.ClearToEmpty();
}
inline const std::string& Net::srcip() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.srcIp)
  return _internal_srcip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Net::set_srcip(ArgT0&& arg0, ArgT... args) {
 
 srcip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.srcIp)
}
inline std::string* Net::mutable_srcip() {
  std::string* _s = _internal_mutable_srcip();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Net.srcIp)
  return _s;
}
inline const std::string& Net::_internal_srcip() const {
  return srcip_.Get();
}
inline void Net::_internal_set_srcip(const std::string& value) {
  
  srcip_.Set(value, GetArenaForAllocation());
}
inline std::string* Net::_internal_mutable_srcip() {
  
  return srcip_.Mutable(GetArenaForAllocation());
}
inline std::string* Net::release_srcip() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Net.srcIp)
  return srcip_.Release();
}
inline void Net::set_allocated_srcip(std::string* srcip) {
  if (srcip != nullptr) {
    
  } else {
    
  }
  srcip_.SetAllocated(srcip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (srcip_.IsDefault()) {
    srcip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Net.srcIp)
}

// int32 srcPort = 2;
inline void Net::clear_srcport() {
  srcport_ = 0;
}
inline int32_t Net::_internal_srcport() const {
  return srcport_;
}
inline int32_t Net::srcport() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.srcPort)
  return _internal_srcport();
}
inline void Net::_internal_set_srcport(int32_t value) {
  
  srcport_ = value;
}
inline void Net::set_srcport(int32_t value) {
  _internal_set_srcport(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.srcPort)
}

// string dstIp = 3;
inline void Net::clear_dstip() {
  dstip_.ClearToEmpty();
}
inline const std::string& Net::dstip() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.dstIp)
  return _internal_dstip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Net::set_dstip(ArgT0&& arg0, ArgT... args) {
 
 dstip_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.dstIp)
}
inline std::string* Net::mutable_dstip() {
  std::string* _s = _internal_mutable_dstip();
  // @@protoc_insertion_point(field_mutable:com.quanzhi.audit_core.common.model.Net.dstIp)
  return _s;
}
inline const std::string& Net::_internal_dstip() const {
  return dstip_.Get();
}
inline void Net::_internal_set_dstip(const std::string& value) {
  
  dstip_.Set(value, GetArenaForAllocation());
}
inline std::string* Net::_internal_mutable_dstip() {
  
  return dstip_.Mutable(GetArenaForAllocation());
}
inline std::string* Net::release_dstip() {
  // @@protoc_insertion_point(field_release:com.quanzhi.audit_core.common.model.Net.dstIp)
  return dstip_.Release();
}
inline void Net::set_allocated_dstip(std::string* dstip) {
  if (dstip != nullptr) {
    
  } else {
    
  }
  dstip_.SetAllocated(dstip, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (dstip_.IsDefault()) {
    dstip_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:com.quanzhi.audit_core.common.model.Net.dstIp)
}

// int32 dstPort = 4;
inline void Net::clear_dstport() {
  dstport_ = 0;
}
inline int32_t Net::_internal_dstport() const {
  return dstport_;
}
inline int32_t Net::dstport() const {
  // @@protoc_insertion_point(field_get:com.quanzhi.audit_core.common.model.Net.dstPort)
  return _internal_dstport();
}
inline void Net::_internal_set_dstport(int32_t value) {
  
  dstport_ = value;
}
inline void Net::set_dstport(int32_t value) {
  _internal_set_dstport(value);
  // @@protoc_insertion_point(field_set:com.quanzhi.audit_core.common.model.Net.dstPort)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace common
}  // namespace audit_core
}  // namespace quanzhi
}  // namespace com

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::com::quanzhi::audit_core::common::model::SourceTypeEnum> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::quanzhi::audit_core::common::model::SourceTypeEnum>() {
  return ::com::quanzhi::audit_core::common::model::SourceTypeEnum_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ProtobufRawHttpEvent_2eproto
