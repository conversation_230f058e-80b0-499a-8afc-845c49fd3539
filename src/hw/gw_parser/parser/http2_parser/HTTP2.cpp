#include <iostream>
#include <cstring>
#include "HTTP2.h"

using namespace analyzer::submodule;

// This is the HTTP2 client connection preface. It indicates the connection is using HTTP2 protocol.
static constexpr uint8_t connectionPreface[]=
{
    0x50,0x52,0x49,0x20,0x2a,0x20,0x48,0x54,
    0x54,0x50,0x2f,0x32,0x2e,0x30,0x0d,0x0a,
    0x0d,0x0a,0x53,0x4d,0x0d,0x0a,0x0d,0x0a
};

static constexpr uint8_t CONN_PREFACE_LENGTH = static_cast<uint8_t>(sizeof(connectionPreface)/sizeof(uint8_t));

HTTP2_Analyzer::HTTP2_Analyzer()
{
    this->tables[0] = this->tables[1] = nullptr;
    this->reassemblers = nullptr;
    this->last_streams[0] = this->last_streams[1] = 0;
    this->last_stream_id = 0;
    this->discard_num = 0;
    this->initReassemblers();
    this->initTables();
    this->initStreams();
}

HTTP2_Analyzer::~HTTP2_Analyzer()
{
    this->destroyReassemblers();
    this->destoryTables();
    this->destroyStreams();
}

/*
bool HTTP2_Analyzer::isHttp2Connection(int len,const uint8_t *data)
{
    if(!data || len == 0)
    {
        return false;
    }
    if( (this->is_http2) || (len >= CONN_PREFACE_LENGTH && (memcmp((void *)data,(void *)connectionPreface,CONN_PREFACE_LENGTH) == 0)) )
    {
        if(this->first_connection)
        {
            // connection preface
            this->initReassemblers();
            this->initStreams();
        }
        this->is_http2 = true;
        return true;
    }
    else
    {
        this->is_http2 = false;
        printf("this data is not HTTP2!\n");
        return false;
    }
}
*/

// deliver a connection 
int HTTP2_Analyzer::deliverStream(int len,const uint8_t *data,bool orig,http2_req_info_t &req,http2_rsp_info_t &res,bool h2c)
{
    if(!data || len == 0)
    {
        return -1;
    }
    
    std::vector<HTTP2_Frame*> frames;
    // when get keep-alive tcp connection,this way may go wrong
    if(len >= CONN_PREFACE_LENGTH && (memcmp((void *)data,(void *)connectionPreface,CONN_PREFACE_LENGTH) == 0) )
    {
        /*
        // connection preface
        this->initReassemblers();
        this->initStreams();
        */
        frames = this->reassemblers->process(data+CONN_PREFACE_LENGTH,len-CONN_PREFACE_LENGTH);
    }
    else
    {
        frames = this->reassemblers->process(data,len);
    }
    for(auto it=frames.begin();it!=frames.end();++it)
    {
        if(*it == nullptr)
        {
            // maybe the end of data or error,whatever return
            printf("Unable to parse http 2 frame from data stream, fatal error!\n");
            return -1;
        }
        HTTP2_Frame* frame = *it;
        uint32_t stream_id = 0;
        
        if(frame->getType() == PUSH_PROMISE_FRAME)
        {
            // process PUSH_PROMISE frame in promisedstream 
            stream_id = static_cast<HTTP2_PushPromise_Frame*>(frame)->getPromisedStreamId();
        }
        else
        {
            stream_id = frame->getStreamId();
        }

        this->addStreamTimer();

        if(stream_id == 0)
        {
            // setting、window_update、etc
            this->handleStream0(frame);
        }
        else
        {
            HTTP2_Stream *stream = this->getStream(stream_id,orig,h2c);
            if(stream)
            {
                bool closed = stream->handleFrame(frame,orig);
                if(closed)
                {
                    // stream ended
                    stream->handleStreamEnd(req,res);
                    this->removeStream(stream);
                }
            }
            
        }
        delete frame;
    }
    return 0;
}

void HTTP2_Analyzer::initStreams(void)
{
    // init streams map object
}

void HTTP2_Analyzer::destroyStreams(void)
{
    auto it = this->streams.begin();
    auto next = this->streams.begin();
    for(;it!=this->streams.end();it=next)
    {
        next++ = it;
        auto stream = it->second;
        delete stream;
        this->streams.erase(it);
    }
    this->streams.clear();
}

HTTP2_Stream* HTTP2_Analyzer::getStream(uint32_t stream_id, bool orig,bool h2c)
{
    HTTP2_Stream *stream = nullptr;
    auto it = this->streams.find(stream_id);
    if(it == this->streams.end())
    {
        // not found in current streams
        if(this->last_stream_id > 0 && stream_id > this->last_stream_id)
        {
            printf("Streams greater than goaway can't be established!\n");
            return nullptr;
        }        
        if(stream_id < this->last_streams[orig])
        {
            printf("Stream Id less than last established stream!\n");
            return nullptr;
        }
        if(this->checkStreams())
        {
            this->last_streams[orig] = stream_id;
            stream = new HTTP2_Stream(stream_id,this->tables,h2c);
            stream->setBeginTime(time(NULL));
            this->streams.insert(std::pair<uint32_t,HTTP2_Stream*>(stream_id,stream));
        }
        else
        {
            printf("check failure!\n");
            this->discard_num++;    
        }
    }
    else
    {
        printf("stream already exit!\n");
        stream = it->second;
    }
    return stream;
}

void HTTP2_Analyzer::removeStream(HTTP2_Stream* s)
{
    this->streams.erase(s->getStreamId());
    delete s;
}

void HTTP2_Analyzer::initReassemblers(void)
{
    if(!this->reassemblers)
    {
        this->reassemblers = new HTTP2_FrameReassembler();
    }
}

void HTTP2_Analyzer::destroyReassemblers(void)
{
    if(this->reassemblers)
    {
        delete this->reassemblers;
    }
}

void HTTP2_Analyzer::initTables(void)
{
    if(!this->tables[0])
    {
        this->tables[0] = new Table();
    }
    if(!this->tables[1])
    {
        this->tables[1] = new Table();
    }
}

void HTTP2_Analyzer::destoryTables(void)
{
    if(this->tables[0])
    {
        delete this->tables[0];
        this->tables[0] = nullptr;
    }
    if(this->tables[1])
    {
        delete this->tables[1];
        this->tables[1] = nullptr;
    }
}

void HTTP2_Analyzer::handleStream0(HTTP2_Frame* frame)
{
    switch(frame->getType())
    {
        case SETTINGS_FRAME:
            this->handleSettings(static_cast<HTTP2_Settings_Frame*>(frame));
            break;
        case GOAWAY_FRAME:
            this->handleGoAway(static_cast<HTTP2_GoAway_Frame*>(frame));
            break;
        case PING_FRAME:
            this->handlePing(static_cast<HTTP2_Ping_Frame*>(frame));
            break;
        case WINDOW_UPDATE_FRAME:
            this->handleWindowUpdate(static_cast<HTTP2_WindowUpdate_Frame*>(frame));
            break;
        // The following are invalid with stream id == 0
        case DATA_FRAME:
        case HEADERS_FRAME:
        case PRIORITY_FRAME:
        case RST_STREAM_FRAME:
        case PUSH_PROMISE_FRAME:
        case CONTINUATION_FRAME:
            printf("Unexpected frame in Stream 0\n");
            break;
    }
}

void HTTP2_Analyzer::handleSettings(HTTP2_Settings_Frame* frame)
{
    
}

void HTTP2_Analyzer::handleGoAway(HTTP2_GoAway_Frame* frame)
{
    // get last_stream_id;
    this->last_stream_id = frame->getLastStreamId();
}

void HTTP2_Analyzer::handlePing(HTTP2_Ping_Frame* frame)
{

}

void HTTP2_Analyzer::handleWindowUpdate(HTTP2_WindowUpdate_Frame* frame)
{

}

bool HTTP2_Analyzer::checkStreams()
{
    if(this->streams.size() + 1 > STREAM_MAX_NUM)
    {
        return false;
    }
    else
    {
        return true;
    }
}

void HTTP2_Analyzer::addStreamTimer()
{
#ifdef DEBUG
    auto it = this->streams.find(stream_id);
    if(it == this->streams.end())
    {
        // not exits
        return;
    }
    /*
     ** map 用[] 会直接添加，不能用做判断
    if(!streams[stream_id])
    {
        return;
    }
    */
    if(time(NULL) - streams[stream_id]->getBeginTime() > TIME_OUT)
    {
        // time out
        this->removeStream(streams[stream_id]);
    }
#endif

    auto it = this->streams.begin();
    for(;it != this->streams.end();++it)
    {
        if((size_t)(time(NULL) - it->second->getBeginTime()) > TIME_OUT)
        {
            // time out
            this->removeStream(it->second);
        }
    }
}
