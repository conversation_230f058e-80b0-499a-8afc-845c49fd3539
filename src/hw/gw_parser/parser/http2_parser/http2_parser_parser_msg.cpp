#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <strings.h>
#include <memory.h>
#include <unistd.h>
#include <inttypes.h>
#include <arpa/inet.h>

#include "http2_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "gw_stats.h"

#include "cJSON.h"
#include "simple_json.h"

#include "gw_i_upload.h"

#include "http2_parser_msg_task_worker.h"
#include "urlfilter_rule.h"
#include "accoutfilter_rule.h"
#include "display_stats_define.h"
#include "get_file_type.h"

#include "pp.h"

// 运行模式状态检查
#define RUNMODE_IS_SHOW_URL(x) (x == 2 || x == 3 || x == 4)
#define RUNMODE_IS_SHOW_BODY(x) (x == 3)
#define RUNMODE_IS_REQ_BODY_FORMAT_STRING(x) (RUNMODE_IS_RESP_BODY_FORMAT_STRING(x) || x == 5 || RUNMODE_IS_RESP_BODY_USE_TEST_DATA(x))
#define RUNMODE_IS_RESP_BODY_USE_TEST_DATA(x) (x == 6)
#define RUNMODE_IS_RESP_BODY_FORMAT_STRING(x) (x >= 1000 || x == 0 || x == 2 || x == 3)

#define REQUEST_CT_FORM "multipart/form-data"
const char *g_p_file_warn = "large";
const char *g_p_file_upload = "upload";
const char *g_p_file_download = "download";

static const char msg_unknown_rule_type[] = "http_unknown_rule";
static const char msg_ruled_type[] = "http_ruled";
static const char msg_drop_file_type[] = "http_drop_file";

thread_local uint64_t g_u64_http_upload_ms = 0;
thread_local uint32_t g_u32_http_upload_index = 0;

#ifdef DEBUG
static void add_header_array_to_json(keyvalue_info_t **pp_header_info, int *p_header_info_num, http2_header_item_t *phi, ssize_t header_item_num)
{
  http2_header_item_t *header = phi;
  ssize_t k = header_item_num;
  ssize_t i = 0;
  ssize_t j = 0;

  keyvalue_info_t *p_header_info = NULL;
  p_header_info = (keyvalue_info_t *)malloc(sizeof(keyvalue_info_t) * k);
  if (NULL == p_header_info)
  {
    return ;
  }
  memset(p_header_info, 0, sizeof(keyvalue_info_t) * k);

  while (k > 0)
  { 
    int duplicate_flag = 0;
    for (j = 0; j < (header_item_num - k); j++)
    {
      if (p_header_info[j].key.length == header->field.length && !strncasecmp(p_header_info[j].key.p_value, header->field.bstr, header->field.length))
      {
        p_header_info[j].value.p_value = header->value.bstr;
        p_header_info[j].value.length = header->value.length;
        duplicate_flag = 1;
        break;
      }
    }
    if (!duplicate_flag)
    {
      p_header_info[i].key.p_value = header->field.bstr;
      p_header_info[i].key.length = header->field.length;
      p_header_info[i].value.p_value = header->value.bstr;
      p_header_info[i].value.length = header->value.length;
      i++;
    }
    k--;
    header++;
  }
  *p_header_info_num = i;
  *pp_header_info = p_header_info;
}


int CHttp2Parser::worker_routine_http2_parser_inner(const void *p)
{
  return http2_parser_msg_routine((http2_parser_msg_t *)p);
}

void CHttp2Parser::free_upload_http_info(upload_http_info_t *p_upload_http_info)
{
  if (NULL == p_upload_http_info)
  {
    return ;
  }

  http_req_info_t *p_req_http_info = &(p_upload_http_info->http_req_info);
  http_rsp_info_t *p_rsp_http_info = &(p_upload_http_info->http_rsp_info);
  http_file_info_t *p_http_file_info = &(p_upload_http_info->http_file_info);

  SAFE_FREE(p_req_http_info->p_req_header);
  SAFE_FREE(p_rsp_http_info->p_rsp_header);
  SAFE_FREE(p_rsp_http_info->set_cookies_list);
  SAFE_FREE(p_http_file_info->p_file_type);
  SAFE_FREE(p_http_file_info->p_file_name);
}

// HTTP2解析数据的组装
int CHttp2Parser::http2_parser_msg_routine(http2_parser_msg_t *phpm)
{
  if (m_conf_upload_mode == 0)
  {
    GWLOG_ERROR(m_comm, "upload moudle not exist\n");
  }
  int i_ret = 0;

  char *s = NULL;
  size_t s_len = 0;

  int i_upload_file = 0;
  bstring_t st_body_value;
  memset(&st_body_value, 0, sizeof(bstring_t));

  int unkown_rule = phpm->unkown_rule;//0 正常传，1 丢弃文件事件，其他值 unknown_rule
  http2_parser_ext_data_t hped[1] = {0};
  http2_parser_ext_data_t *phped = &hped[0];
  http2_header_info_t st_header_info;
  memset(&st_header_info, 0, sizeof(http2_header_info_t));

  upload_http_info_t st_upload_http_info;
  memset(&st_upload_http_info, 0, sizeof(upload_http_info_t));

#define HAS_APPLICATION ((strlen("application") < phpm->req_hdr_content_type.length) && (!strncmp(phpm->req_hdr_content_type.bstr, "application", strlen("application"))))
#define NOT_URLENCODE ((strlen("application/x-www-urlencoded") > phpm->req_hdr_content_type.length) || (strncmp(phpm->req_hdr_content_type.bstr, "application/x-www-urlencoded", strlen("application/x-www-urlencoded"))))
#define NOT_FROM_URLENCODE ((strlen("application/x-www-form-urlencoded") > phpm->req_hdr_content_type.length) || (strncmp(phpm->req_hdr_content_type.bstr, "application/x-www-form-urlencoded", strlen("application/x-www-form-urlencoded"))))
#define FROM_DATA ((strlen(REQUEST_CT_FORM) < phpm->req_hdr_content_type.length) && (!strncmp(phpm->req_hdr_content_type.bstr, REQUEST_CT_FORM, strlen(REQUEST_CT_FORM))))
//#define SPECIFIED_API (m_post_file_url->hit_url_filter(phpm->full_uri))
  /* 判断是上传文件还是下载文件 */
  if (phpm->method == HTTP_POST || phpm->method == HTTP_PUT)
  {
    if (FROM_DATA || (HAS_APPLICATION && NOT_URLENCODE && NOT_FROM_URLENCODE))
    {
      i_upload_file = 1;
    }
    else
    {
      i_upload_file = 0;
    }
  }
  else
  {
    i_upload_file = 0;
  }

  st_header_info.p_req_url = phpm->full_uri;
  http2_parser_msg_routine_req(phpm, &st_upload_http_info.http_req_info, &st_body_value);
  http2_parser_msg_routine_rsp(phpm, &st_upload_http_info.http_rsp_info, phped, &st_header_info);


  /* 添加net信息 */
  if (phpm->addr.client.v == 4)
  {
    strcpy(st_upload_http_info.http_net_info.a_src_ip, st_upload_http_info.http_req_info.remote_addr);
    get_addr_str(phpm->addr.server.ipv4, st_upload_http_info.http_net_info.a_dst_ip, sizeof(st_upload_http_info.http_net_info.a_dst_ip));
  }
  else
  {
    strcpy(st_upload_http_info.http_net_info.a_src_ip, st_upload_http_info.http_req_info.remote_addr);
    get_ip6addr_str((uint32_t*)phpm->addr.server.ipv6, st_upload_http_info.http_net_info.a_dst_ip, sizeof(st_upload_http_info.http_net_info.a_dst_ip));
  }
  st_upload_http_info.http_net_info.src_port = phpm->addr.client.port;
  st_upload_http_info.http_net_info.dst_port = phpm->addr.server.port;
  if (phpm->pcap_filename)
  {
    memcpy(st_upload_http_info.http_net_info.pcap_filename, phpm->pcap_filename, 255);
  }
  
  /* 添加unique_id */
  add_event_id(st_upload_http_info.a_unique_id);
  
  /* 添加meta信息 */
  if (1 == m_conf_pcap_timestamp)
  {
    // 取自PCAP结构里的时间信息
    st_upload_http_info.http_meta_info.ts = phpm->pcap_ts;
  }
  else
  {
    uint64_t u64_real_ms = m_comm->gw_real_time_ms();
    //st_upload_http_info.http_meta_info.ts = time(NULL);
    st_upload_http_info.http_meta_info.ts = ((double)u64_real_ms / 1000);
  }
  
  st_upload_http_info.http_meta_info.req_seq = phpm->req_seq;
  st_upload_http_info.http_meta_info.req_ack = phpm->req_ack;
  st_upload_http_info.http_meta_info.rsp_seq = phpm->rsp_seq;
  st_upload_http_info.http_meta_info.rsp_ack = phpm->rsp_ack;

  bool analyze = m_comm->get_gw_stats()->check_analyze_log(phpm->addr.client.ipv4,phpm->addr.server.ipv4,phpm->addr.server.port);
  if (analyze)
  {
    st_upload_http_info.analyze_flag = m_comm->get_gw_stats()->get_analyze_flag();
  }

  s = simple_json_encode(st_upload_http_info, unkown_rule, analyze, &s_len);
  phpm->is_file_event = st_upload_http_info.is_file_event;
  if (phpm->is_file_event) {
    // 内存交给 下一个线程(mini_upload)来释放
    phpm->req_msg.header = {0};
    phpm->req_msg.body = {0};
    phpm->rsp_msg.header = {0};
    phpm->rsp_msg.body = {0};
  }

  if (s != NULL)
  {
    // 上传数据
    if (m_is_drop_file_event)
    {
      unkown_rule = 1;
    }

    // 数据放到内部上传队列中
    free_upload_http_info(&st_upload_http_info);
    http_cb_upload_msg(s, unkown_rule, analyze, s_len);
  }

  return 0;
}

int CHttp2Parser::http2_parser_msg_routine_req(http2_parser_msg_t *phpm, http_req_info_t *p_http_req_info, bstring_t *p_body_value)
{
  http2_parser_half_msg_t *p_req_msg = &phpm->req_msg;
  //char *url_full = phpm->full_uri;

  /* 添加method信息 */
  //p_http_req_info->p_method = http_method_str((enum http_method)phpm->method);
  p_http_req_info->http_major = phpm->http_req_ver.major;
  p_http_req_info->http_minor = phpm->http_req_ver.minor;
  /* 添加remote addr 信息 */
  if (likely(phpm->addr.client.v == 4))
  {
    get_addr_str(phpm->addr.client.ipv4, p_http_req_info->remote_addr, COUNTOF(p_http_req_info->remote_addr));
  }
  else
  {
    get_ip6addr_str((uint32_t*)phpm->addr.client.ipv6, p_http_req_info->remote_addr, COUNTOF(p_http_req_info->remote_addr));
  }

  /* 添加error code 信息 */
  p_http_req_info->error_code = phpm->req_error_code;

  /* 添加url 信息 */
  //p_http_req_info->full_url = url_full;
  memcpy(p_http_req_info->full_url, phpm->full_uri, strlen(phpm->full_uri));
  memcpy(p_http_req_info->host, phpm->host, strlen(phpm->host));
  p_http_req_info->p_header = p_req_msg->header.bstr;
  p_http_req_info->header_length = p_req_msg->header.length;
  p_http_req_info->p_body = p_req_msg->body.bstr;

  if (RUNMODE_IS_REQ_BODY_FORMAT_STRING(m_conf_run_mode))
  {
    if (m_i_insert_original_req_body == 1 || p_body_value->length == 0)
    {
      p_http_req_info->body.p_value = p_req_msg->body.bstr;
      p_http_req_info->body.length = p_req_msg->body.length;
    }
    else
    {
      p_http_req_info->body.p_value = p_body_value->bstr;
      p_http_req_info->body.length = p_body_value->length;
    }
  }

  /* 添加 request header 信息 */
  add_header_array_to_json(&(p_http_req_info->p_req_header),  &(p_http_req_info->req_header_num), p_req_msg->header_item, p_req_msg->header_item_num);

  return 0;
}

int CHttp2Parser::http2_parser_msg_routine_rsp(http2_parser_msg *phpm, http_rsp_info *p_http_rsp_info, http2_parser_ext_data *phped, http2_header_info *p_http_header_info)
{
  http2_parser_half_msg_t *p_rsp_msg = &phpm->rsp_msg;
  size_t length = 0;
  uint64_t u64_content_length = 0;
  int malloc_size = 16;

  p_http_rsp_info->status_code = phpm->status_code;
  p_http_rsp_info->http_major = phpm->http_rsp_ver.major;
  p_http_rsp_info->http_minor = phpm->http_rsp_ver.minor;

  p_http_rsp_info->error_code = phpm->rsp_error_code;
  p_http_rsp_info->p_header = p_rsp_msg->header.bstr;
  p_http_rsp_info->header_length = p_rsp_msg->header.length;
  add_header_array_to_json(&(p_http_rsp_info->p_rsp_header), &(p_http_rsp_info->rsp_header_num), p_rsp_msg->header_item, p_rsp_msg->header_item_num);

  if (p_rsp_msg->header_item != NULL && p_rsp_msg->header_item_num > 0)
  {
    ssize_t header_item_num = p_rsp_msg->header_item_num;
    http2_header_item_t *header = p_rsp_msg->header_item;

    while (header_item_num > 0)
    {
      if (0 == my_strnicmp(&header->field, "Set-Cookie"))
      {
        if (p_http_rsp_info->set_cookies_list == NULL)
        {
          p_http_rsp_info->set_cookies_list = (bstr_t *)malloc(sizeof(bstr_t) * malloc_size);
          memset(p_http_rsp_info->set_cookies_list, 0, sizeof(bstr_t) * malloc_size);
        }

        if (p_http_rsp_info->cookies_list_num == malloc_size)
        {
          malloc_size += malloc_size;
          bstr_t *cookies_list_tmp = (bstr_t *)malloc(sizeof(bstr_t) * malloc_size);
          memset(cookies_list_tmp, 0, sizeof(bstr_t) * malloc_size);
          memcpy(cookies_list_tmp,  p_http_rsp_info->set_cookies_list, p_http_rsp_info->cookies_list_num * sizeof(bstr_t));
          free(p_http_rsp_info->set_cookies_list);
          p_http_rsp_info->set_cookies_list = cookies_list_tmp;
        }
        p_http_rsp_info->set_cookies_list[p_http_rsp_info->cookies_list_num].p_value = header->value.bstr;
        p_http_rsp_info->set_cookies_list[p_http_rsp_info->cookies_list_num].length = header->value.length;
        p_http_rsp_info->cookies_list_num++;
      }
      header_item_num--;
      header++;
    }
  }

  if (phpm->rsp_hdr_content_type.bstr)
  {
    memcpy(p_http_header_info->a_rsp_content_type, phpm->rsp_hdr_content_type.bstr, MIN(phpm->rsp_hdr_content_type.length, sizeof(p_http_header_info->a_rsp_content_type) - 1));
  }

  if (phpm->rsp_hdr_content_disposition.bstr)
  {
    memcpy(p_http_header_info->a_rsp_disposition, phpm->rsp_hdr_content_disposition.bstr, MIN(phpm->rsp_hdr_content_disposition.length, sizeof(p_http_header_info->a_rsp_disposition) - 1));
  }

  if (phpm->rsp_hdr_content_range.bstr)
  {
    memcpy(p_http_header_info->a_rsp_content_range, phpm->rsp_hdr_content_range.bstr, MIN(phpm->rsp_hdr_content_range.length, sizeof(p_http_header_info->a_rsp_content_range) - 1));
  }
  
  if (RUNMODE_IS_RESP_BODY_FORMAT_STRING(m_conf_run_mode))
  {
    // 封装数据结构，并发送给服务器
    length = p_rsp_msg->body.length;

    if (p_rsp_msg->is_chunked == 0)
    {
      if (p_rsp_msg->content_length > 0 && p_rsp_msg->content_length != ULLONG_MAX)
      {
        u64_content_length = p_rsp_msg->content_length;
      }
    }

    p_http_rsp_info->body.p_value = p_rsp_msg->body.bstr;
    p_http_rsp_info->body.length = length;
  }
  else if (RUNMODE_IS_RESP_BODY_USE_TEST_DATA(m_conf_run_mode))
  {
    // for test body
    p_http_rsp_info->body.p_value = g_test_mode_http_body;
    p_http_rsp_info->body.length = sizeof(g_test_mode_http_body);
  }

  phped->pstr = (char*)p_rsp_msg->body.bstr;
  phped->length = u64_content_length > 0 ? u64_content_length : length;
  
  return 0;
}
#endif

void CHttp2Parser::http2_cb_upload_msg(const char *s, int unkown_rule, bool analyze, size_t s_len)
{
  //size_t length = strlen(s);
  size_t length  = s_len;
  http2_cb_upstream(s, length);

  if (unlikely(m_p_upload == NULL))
  {
    GWLOG_INFO(m_comm, "upload null(%s)\n", m_conf_upload_name.c_str());
    cJSON_free((void*)s);
    return;
  }

  UploadMsg *pum = new UploadMsg;
  memset(pum, 0, sizeof(UploadMsg));

  pum->cb = sizeof(UploadMsg);
  pum->destroy_func = free_upload_msg;
  pum->parser = this;
  pum->length = length;
  pum->s = s;
  pum->log_for_analyze = analyze;

  switch (unkown_rule)
  {
  case 0:
    pum->msgtype = msg_ruled_type;
    break;
  case 1:
    pum->msgtype = msg_drop_file_type;
    break;
  default:
    pum->msgtype = msg_unknown_rule_type;
  }

  pum->mem_size = sizeof(UploadMsg) + pum->length;

  m_p_upload->put_msg(pum);
}

void CHttp2Parser::free_upload_msg(const struct UploadMsg *pum)
{
  CHttp2Parser *pThis = (CHttp2Parser *)pum->parser;
  ASSERT(pThis != NULL);
  (void)pThis;

  //cJSON_free((void*)pum->s);

  delete pum;
}

/*
void CHttp2Parser::free_http2_parser_msg_inner(const http2_parser_msg_t *p)
{
  SAFE_FREE(p->req_msg.header_item);
  SAFE_FREE(p->rsp_msg.header_item);

  BSTR_SAFE_FREE(p->req_msg.header.bstr);
  BSTR_SAFE_FREE(p->req_msg.body.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.header.bstr);
  BSTR_SAFE_FREE(p->rsp_msg.body.bstr);
}
*/

CWorkerQueue *CHttp2Parser::new_wq_http2_parser_msg(void)
{
  m_p_wq[HTTPPARSER_WQ_HTTP_PARSER_MSG] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_http2_parser_msg();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerMsg *ptw = new CTaskWorkerMsg();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_HTTP_PARSER_MSG] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_parser_queue_max_num, m_conf_http_parser_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_MSG_QUEUE);
  // pwq->set_queue_destroy_callback((q_destroy_func_t)free_http_parser_msg);
  pwq->init();
  pwq->create_queue();
  // pwq->create_thread(m_conf_http_parser_thread_num, worker_routine_http_parser, this);
  pwq->adjust_worker_thread_num(m_conf_http_parser_thread_num);

  //m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data(), 50);
  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 50);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}


void CHttp2Parser::add_event_id(char *p_event_id)
{
    if (NULL == p_event_id)
    {
      return;
    }

    char a_unique_code[64] = {0};
    //char a_unique_encode[128] = {0};
    uint64_t u64_time_val = 0;
    get_ms_timeval(&u64_time_val);

    if (g_u64_http_upload_ms == 0)
    {
        g_u64_http_upload_ms = u64_time_val;
        g_u32_http_upload_index = 1;
    }
    else
    {
        if (u64_time_val == g_u64_http_upload_ms)
        {
            g_u32_http_upload_index ++;
        }
        else
        {
            g_u64_http_upload_ms = u64_time_val;
            g_u32_http_upload_index = 1;
        }
    }

    /* 获取唯一标识ID */
    get_unique_event_id(m_str_gw_ip.c_str(), g_u64_http_upload_ms, g_u32_http_upload_index, a_unique_code, sizeof(a_unique_code) - 1);

    /* 将unique_code进行base64编码 */
    base64_encode((unsigned char*)p_event_id, (unsigned char*)a_unique_code, strlen(a_unique_code));

    return;
}

// static void get_addr_str(u_int addr, char *buf, size_t size)
// {
//   buf[size - 1] = '\0';
//   unsigned char *bytes = (unsigned char *)&addr;
//   size_t offset = 0;
//   int i = 0;
//   for (i = 0; i < 4; i++)
//   {
//     ntos(bytes[i], buf + offset, &offset);
//     if (i != 3)
//     {
//       *(buf + offset++) = '.';
//     }
//   }
// }