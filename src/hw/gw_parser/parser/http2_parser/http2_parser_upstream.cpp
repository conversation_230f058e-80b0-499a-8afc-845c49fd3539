/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "http2_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "gw_stats.h"

#include "cJSON.h"

#include "http2_parser_upstream_task_worker.h"
#include "display_stats_define.h"

// typedef struct http_upstream_data
// {
//   size_t mem_size;
//   CHttpParser *parser;

//   size_t length;
//   char *s;

// } http_upstream_data_t;

//////

/**
 * 增加上层协议解析对象。
 * @param CParser *parser
 */
void CHttp2Parser::add_upstream(CParser *parser)
{
  m_vec_upstream_parser.push_back(parser);
}

/**
 * 清空上层协议解析对象
 */
void CHttp2Parser::reset_upstream(void)
{
  m_vec_upstream_parser.clear();
}

/**
 * 推送到上层消息(异步方式, Json序列化数据)
 * @param char *s
 * @param size_t *length
 */
void CHttp2Parser::push_upstream_msg(char *s, size_t length)
{
}

/**
 * 是否使用当前协议解析流数据
 * @param StreamData*
 */
bool CHttp2Parser::is_parsed(const struct StreamData *) const
{
  return false;
}

void CHttp2Parser::http2_cb_upstream(const char *s, size_t length)
{
  if (get_wq_upstream() == NULL)
  {
    return;
  }

  http2_upstream_data_t *phdsd = new http2_upstream_data_t;
  size_t mem_size = sizeof(http2_upstream_data_t) + length;
  // phdsd->mem_size = sizeof(http_upstream_data_t) + length;

  phdsd->length = length;
  phdsd->s = new char[length];
  memcpy(phdsd->s, s, length);

  // phdsd->parser = this;

  if (!get_wq_upstream()->queue_put_data(phdsd, mem_size))
  {
    free_upstream_inner(phdsd);
    delete phdsd;
  }
}

/**
 * 克隆会话流数据到队列中使用(预留)
 * @param struct StreamData*
 */
struct StreamData *CHttp2Parser::clone_stream_data(const struct StreamData *)
{
  return NULL;
}

// void CHttpParser::free_upstream(http_upstream_data_t *p)
// {
//   size_t length = p->mem_size;
//   CHttpParser *pThis = p->parser;
//   CWorkerQueue *pwq = pThis->get_wq_upstream();
//   volatile uint64_t &stats_queue_memory_size = pwq->get_queue_mem_size();

//   free_upstream_inner(p);

//   delete p;

//   __sync_fetch_and_sub(&stats_queue_memory_size, length);
// }

// int CHttpParser::worker_routine_http_upstream(void *args_ptr, worker_routine_param_t *pwrp, void *p)
// {
//   CHttpParser *pThis = (CHttpParser *)args_ptr;

//   switch (pwrp->step)
//   {
//   case WQ_WR_BEGIN:
//     break;

//   case WQ_WR_END:
//     break;

//   case WQ_WR_DATA:
//     return pThis->worker_routine_http_upstream_inner(p);
//     break;

//   default:
//     break;
//   }

//   return 0;
// }

int CHttp2Parser::worker_routine_http2_upstream_inner(const void *p)
{
  http2_upstream_data_t *phdsd = (http2_upstream_data_t *)p;

  for (size_t i = 0; i < m_vec_upstream_parser.size(); i++)
  {
    m_vec_upstream_parser[i]->push_upstream_msg(phdsd->s, phdsd->length);
  }

  return 0;
}

CWorkerQueue *CHttp2Parser::new_wq_upstream(void)
{
  m_p_wq[HTTPPARSER_WQ_UPSTREAM] = m_comm->create_worker_queue();

  CWorkerQueue *pwq = get_wq_upstream();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerUpstream *ptw = new CTaskWorkerUpstream();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[HTTPPARSER_WQ_UPSTREAM] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_http_upstream_queue_max_num, m_conf_http_upstream_queue_memory_max_size_bytes);
  pwq->set_queue_name(HTTP_UPSTREAM_QUEUE);
  // pwq->set_queue_destroy_callback((q_destroy_func_t)free_upstream);
  pwq->init();
  pwq->create_queue();
  // pwq->create_thread(m_conf_http_upstream_thread_num, worker_routine_http_upstream, this);
  pwq->adjust_worker_thread_num(m_conf_http_upstream_thread_num);

  //m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data(), 55);
  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, 55);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

void CHttp2Parser::free_upstream_inner(const http2_upstream_data_t *p)
{
  delete[] p->s;
}
