#ifndef __HTTP2_PARSER_H__
#define __HTTP2_PARSER_H__

#include <string>
#include <vector>
#include <set>
#include <algorithm>
#include <cctype>
#include <zlib.h>

#include "gw_i_parser.h"
#include "utils.h"
#include "HTTP2.h"
#include "gw_stats.h"
#include "gw_ver.h"
#include "pp.h"

#define HTTPPARSER_VER GW_VER_STRING(GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION)

#define HTTPPARSER_WQ_HTTP_GZIP 0
#define HTTPPARSER_WQ_HTTP_PARSER_MSG 1
#define HTTPPARSER_WQ_UPSTREAM 2
#define HTTPPARSER_WQ_HTTP_GZIP_PARSER 3
#define HTTPPARSER_WQ_MINIO_FILE  4
#define HTTPPARSER_WQ_MAX_NUM   8

enum http_parser_type { HTTP_REQUEST, HTTP_RESPONSE, HTTP_BOTH };

typedef struct
{
  volatile uint64_t cnt_p;       // 解析总数
  volatile uint64_t cnt_p_bytes; // 字节总数
  volatile uint64_t cnt_p_succ;  // 解析成功数量
  volatile uint64_t cnt_p_fail;  // 解析失败数量

} stats_http_parser_t;

typedef struct
{
  volatile uint64_t cnt_m;      // 解析总数
  volatile uint64_t cnt_m_succ; // 解析成功数量
  volatile uint64_t cnt_m_fail; // 解析失败数量
  volatile uint64_t cnt_match_by_trunk_failed;      // 因为trunk导致的失败
  volatile uint64_t cnt_match_by_trunk_too_large;    // 因为trunk导致的失败
  volatile uint64_t cnt_match_by_ungzip_failed;     // 因为gzip解压导致的失败
  volatile uint64_t cnt_match_by_payload_too_large;  // 因为内容过长导致的失败
  volatile uint64_t cnt_match_by_packet_lost;       // 因为内容过长导致的失败
} stats_http_match_t;

typedef struct 
{
  volatile uint64_t cnt_op;       // 完整的交互总数(Request + Response)
  volatile uint64_t cnt_op_filter; // 过滤数量
  volatile uint64_t cnt_op_pass;   // 未过滤的数量
} stats_http_filter;

typedef struct
{
  // session
  volatile uint64_t cnt_session;       // 数量
  volatile uint64_t cnt_session_bytes; // 字节总数量

  // parser
  volatile stats_http_parser_t p;
  // match
  volatile stats_http_match_t m;

  // request
  volatile stats_http_parser_t req;
  volatile stats_http_match_t req_match_rsp; // 匹配respone 成功数量

  // response
  volatile stats_http_parser_t rsp;
  volatile stats_http_match_t rsp_match_req; // 匹配request

  // ip-method filter
  volatile stats_http_filter ip_method_filter;

  volatile stats_http_filter url_filter;

  volatile stats_http_filter host_filter;

  volatile stats_http_filter content_type_filter;

  volatile stats_http_filter file_type_filter;
} stats_http_t;

struct StreamData;
struct bstring;

class CTaskWorker;
class CTaskWorkerUpstream;
class CWorkerQueue;
class CUrlfilterRule;
class CAccoutfilterRule;
class CIpfilterRule;
class CUpload; 
class CFileTypeFilter;
class CContentTypeFilter;
class CIpMethodFilter;
class CHostFilter;
class CHostWhite;
class CUrlFilter;
class CUrlPostFile;
class CUploadFileType;
struct worker_routine_param;
struct thread_local_gzip_data;

class CGwCommon;
class CFilterRule;

struct conn;

struct app_stream;

struct HalfStreamData;
struct SessionMgtData;

class CSessionMgt;
class CSession;
class CTcpParser;

struct http2_parser_msg;
struct cJSON;

struct http2_parser_ext_data;
struct http2_header_info;

struct http2_upstream_data;

struct tcp_stream;
struct half_stream;
struct upload_http_info;
struct http_req_info;
struct http_rsp_info;
struct bstr;
struct http_file_info;

struct http_minio_file;

extern "C" {
typedef struct upload_http_fast_info upload_http_fast_info_t;
typedef struct upload_http_info upload_http_info_t;
}

class CHttp2Parser : public CParser
{
public:
  CHttp2Parser(void);
  virtual ~CHttp2Parser(void);

public:
  virtual void cache_clean();
  /**
   * 在接收数据时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 在连接关闭时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 在连接重置时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual bool probe_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 在接收数据时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  virtual int parse_clear(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 在连接关闭时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 在连接重置时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @param struct conn *
   */
  virtual int parse_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*);

  /**
   * 获取当前流解析出来的数据。
   * @param struct StreamData *
   * @param int dir
   * @param int *data_len
   * @param int *offset_out
   */
  virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out);

  /**
   * 已处理字节数。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard(struct StreamData *, int dir, int num);

  /**
   * 已处理字节数，同时更新数据。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard_and_update(struct StreamData *, int dir, int num);

  // /**
  //  * 删除解析对象中在会话管理中的单边数据。
  //  * @param HalfStreamData*
  //  */
  // virtual void del_session_half_stream(HalfStreamData *);

  /**
   * @param StreamData*
   */
  virtual void del_session_stream(StreamData *);

  /**
   * @param SessionMgtData*
   */
  virtual void del_session_param(SessionMgtData *);

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置过滤规则。
   * @param CFilterRule *rule
   */
  virtual void set_url_filter_rule(CFilterRule *rule);

  /**
   *  设置账号过滤规则 
   *  @param CFilterRule *rule
   */
  virtual void set_accout_filter_rule(CFilterRule *rule);

  virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule);

  /**
   * 增加上层协议解析对象。
   * @param CParser *parser
   */
  virtual void add_upstream(CParser *parser);

  /**
   * 清空上层协议解析对象
   */
  virtual void reset_upstream(void);

  /**
   * 推送到上层消息(异步方式, Json序列化数据)
   * @param char *s
   * @param size_t *length
   */
  virtual void push_upstream_msg(char *s, size_t length);

  /**
   * 是否使用当前协议解析流数据
   * @param struct StreamData*
   */
  virtual bool is_parsed(const struct StreamData *) const;

  /**
   * 克隆会话流数据到队列中使用(预留)
   * @param struct StreamData*
   */
  virtual struct StreamData *clone_stream_data(const struct StreamData *);

  /**
   *  获取解析http数量(针对http parser) 
   */
  virtual uint64_t get_parser_http_cnt();

  /**
   *  获取解析http成功的数量(针对http parser) 
   */
  virtual uint64_t get_succ_parser_http_cnt();

  /**
   *  获取解析parser的状态数据，以便于进行查看Parser内部状态
   */
  virtual void* get_parser_status();

  /**
   * 设置解析对象type
   */
  virtual void set_parser_type(int type);

  virtual void set_tcp_parser(CTcpParser *p);

  virtual void read_conf_urlbase_for_mon();

  virtual void read_conf_filetype_for_mon();

  virtual void get_log_buf(char *log_buf, size_t log_buf_len) const;

  virtual uint32_t parser_status() const;

  virtual void set_drop_file_event(bool is_drop) {return ;};

protected:
  CGwCommon *m_comm;
  analyzer::submodule::upload_http2_info_t st_upload_http2_info;
  volatile int m_quit_signal;
  char m_name[32];
  const char *json;
  int cnt;
  bool h2c;

protected:
  void print_http2_match_stats(void) const;
  void print_http2_param(void) const;

protected:
  //int worker_routine_http2_parser_inner(const void *p);
  //void free_http2_parser_msg_inner(const http2_parser_msg *p);
  // HTTP2解析数据的组装
  //int http2_parser_msg_routine(http2_parser_msg *phpm);
  //int http_parser_msg_routine_req(http2_parser_msg *phpm, http_req_info *p_http_req_info, bstring *p_body_value);
  //int http_parser_msg_routine_rsp(http2_parser_msg *phpm, http_rsp_info *p_http_rsp_info, http2_parser_ext_data *phped, http2_header_info *p_http_header_info);

  //void free_upload_http_info(upload_http_info *p_upload_http_info);

  void add_event_id(char *p_event_id);

  friend class CTaskWorkerMsg;

public:
  inline CWorkerQueue *get_wq_http2_parser_msg(void) const
  {
    return m_p_wq[HTTPPARSER_WQ_HTTP_PARSER_MSG];
  }
protected:
  CWorkerQueue *new_wq_http2_parser_msg(void);

protected:
  stats_http_t m_stats_http2; // 状态

protected:
  void http2_cb_upload_msg(const char *s, int unkown_rule, bool analyze, size_t s_len);
  static void free_upload_msg(const struct UploadMsg *); // this包含在结构体中

protected:
  void http2_cb_upstream(const char *s, size_t length);

  // static void free_upstream(http_upstream_data *); // this包含在结构体中
  void free_upstream_inner(const http2_upstream_data *p);

  // static int worker_routine_http2_upstream(void *args_ptr, worker_routine_param *pwrp, void *p);
  int worker_routine_http2_upstream_inner(const void *p);

  friend class CTaskWorkerUpstream;

protected:
  CWorkerQueue *m_p_wq[HTTPPARSER_WQ_MAX_NUM];
  CTaskWorker *m_p_tw[HTTPPARSER_WQ_MAX_NUM];

  void free_task_worker(CTaskWorker *p);
  void free_worker_queue(CWorkerQueue *p);

  void modify_stats(int enable);
  void send_empty_http_req_enable(int enable);
  void send_empty_http_rsp_enable(int enable);
  //void upload_protobuf_enable(int enable);

  void modify_drop_percent(int percent);

protected:
  char *simple_fast_json_encode(upload_http_fast_info_t& p_st_http_fast_info);
  char* simple_json_encode(upload_http_info_t& p_upload_http_info, int unknown_rule, int analyze, size_t *p_s_len);
  char* simple_json_encode_inner(upload_http_info_t *p_upload_http_info, size_t *p_s_len);

  char* new_event_format_encode(upload_http_info_t *p_upload_http_info, size_t *p_s_len);
  int is_file_event(upload_http_info_t& upload_http_info);
  int get_rw_flag(const char* p_file_direction);
  bool is_inline_file(upload_http_info_t& upload_http_info);

protected:
  inline CWorkerQueue *get_wq_upstream(void) const
  {
    return m_p_wq[HTTPPARSER_WQ_UPSTREAM];
  }
  CWorkerQueue *new_wq_upstream(void);

protected:
  analyzer::submodule::HTTP2_Analyzer *analyzer;

  int m_conf_http_parser_queue_max_num;
  uint64_t m_conf_http_parser_queue_memory_max_size_bytes;
  int m_conf_http_gzip_mode;
  int m_conf_run_mode;
  int m_conf_http_parser_mode;
  int m_conf_http_url_show_size;
  int m_conf_http_request_body_max_size;
  int m_conf_http_response_body_max_size;
  int m_conf_http_parser_thread_num;
  int m_conf_pcap_timestamp;
  int m_conf_upload_mode;     
  int m_conf_http_body_show_size;
  int m_u32_http_gzip_deep;      
  int m_i_insert_body_flag;
  int m_i_insert_original_req_body;      
  std::string m_conf_upload_name;        
  //int m_u64_http_upload_ms;
  //int m_u32_http_upload_index;

  int m_conf_http_gzip_thread_num;
  int m_conf_http_gzip_queue_max_num;             
  uint64_t m_conf_http_gzip_queue_memory_max_size_bytes; 

  int m_conf_http_upstream_thread_num;                    
  int m_conf_http_upstream_queue_max_num;                    
  uint64_t m_conf_http_upstream_queue_memory_max_size_bytes;
  int m_conf_multi_queue_forward_enable;
  std::string m_str_gw_ip;
  int m_i_fast_message; //用于快速解析识别(目标IP、URL、content_type)

  int m_conf_upstream;                          // TODO 是否发送到下游解析对象
  std::vector<CParser *> m_vec_upstream_parser; // TODO 下游通知解析对象

  CUrlfilterRule *m_urlfilter_rule;
  CAccoutfilterRule *m_accoutfilter_rule;
  CIpfilterRule *m_upload_client_ipfilter_rule;
  CIpfilterRule *m_upload_server_ipfilter_rule;
  int m_conf_http_pipeline_mode;
  int m_conf_http_header_min_size;

  int m_conf_http_gzip_parser_thread_num;
  int m_conf_http_gzip_parser_queue_max_num;
  int m_conf_http_gzip_parser_mode;
  uint64_t m_conf_http_gzip_parser_queue_memory_max_size_bytes;
  CUpload *m_p_upload;
  int m_http_file_download_flag;
  int m_http_type;
  int m_stream_debug;
  int m_rsp_continue_ignore;
  CTcpParser* m_tcp_parser;
  std::string m_str_url_filter_base_path;
  std::vector<std::string> m_vec_url_filter;

  CFileTypeFilter *m_file_type_filter;
  CContentTypeFilter *m_content_type_filter;
  CHostFilter *m_host_filter;
  CHostWhite *m_host_white;
  CIpMethodFilter *m_ipmethod_filter;
  CUrlFilter *m_url_filter;
  CUrlFilter *m_url_white;
  CUrlPostFile *m_post_file_url;
  CUploadFileType *m_upload_file_type;
  bool m_upload_file;
  int m_conf_http_recv_max_size;
  int m_drop_empty_rsp;
  int m_conf_http_minio_file_thread_num;
  int m_conf_http_minio_file_queue_max_num;             
  uint64_t m_conf_http_minio_file_queue_memory_max_size_bytes; 
  int m_use_new_event_format;
  int m_conf_http_body_md5;
  int m_conf_parser_enable;
  int m_conf_send_empty_http_req_enable;
  int m_conf_send_empty_http_rsp_enable;   //not use at present
  bool m_is_drop_file_event;//该丢弃指的是放入一个特定的kafka的topic
  int m_upload_protobuf_enable;
  int m_drop_percent;
  int m_drop_enable;
  int m_upload_file_size_lower_limit;


};

extern "C" void gw_no_mem(const char *func);

#endif // __HTTP_PARSER_H__
