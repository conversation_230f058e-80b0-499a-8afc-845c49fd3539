ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC = gcc

TARGET = libbrotli.so
INCLUDE = -I ./common
SOURCE = $(wildcard ./*.c ./common/*.c)	# 获取当前目录下所有的.c文件
OBJS = $(patsubst %.c, %.o, $(SOURCE))	# 列出SOURCE所有的.c文件对应的.o文件

$(TARGET):$(OBJS)
	$(CC) -shared $(OBJS) -o $(TARGET)

%.o : %.c
	$(CC) -c -fPIC $(INCLUDE) -o $@ $<

.PHONY:clean
clean:
	rm $(OBJS) $(TARGET)
