/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "http2_parser.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "http2_parser_msg_task_worker.h"


int CTaskWorkerMsg::deal_data(const TaskWorkerData *ptwd)
{
  //return get_parser()->worker_routine_http2_parser_inner(ptwd);
  return 0;
}

void CTaskWorkerMsg::free_data(const TaskWorkerData *ptwd)
{
  /*
  const http2_parser_msg_t *p = (const http2_parser_msg_t *)ptwd;
  get_parser()->free_http2_parser_msg_inner(p);

  delete p;
  */
}
