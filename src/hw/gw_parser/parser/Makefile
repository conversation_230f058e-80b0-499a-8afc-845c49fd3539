
# dirs = ssl_parser http_parser hive_parser
# dirs = $(shell ls -l | grep ^d | grep _parser| awk '{print $$9}')
dirs = $(filter-out $(BUILD_FILTER_OUT_DIRS), $(shell ls -l | grep ^d | grep _parser| awk '{print $$9}'))
dirs = http_parser http2_parser grpc_parser ftp_parser mail_parser ssl_parser smb_parser nfs_parser oracle_parser
.PHONY: all clean


all:
	@for dir in $(dirs); do \
		$(MAKE) -C $$dir || exit "$$?"; \
	done

clean:
	@for dir in $(dirs); do \
		$(MAKE) -C $$dir clean; \
	done


