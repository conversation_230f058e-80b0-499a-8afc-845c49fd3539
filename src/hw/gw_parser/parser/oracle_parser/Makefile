
ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif


CFLAGS          = -std=c++11 -g -fvisibility=hidden -fPIC -I. -I../.././include -I../.././utils/queue/ -I../.././utils/cjson/


LDFLAGS         = -shared
LDFLAGS        += -lstdc++ -lssl -lcrypto  -lpthread -liconv


CFLAGS         += -I/opt/openssl/include/
LDFLAGS        += -L/opt/openssl/lib/

CFLAGS         += -I/usr/libiconv/include
LDFLAGS        += -L/usr/libiconv/lib/

include ../../flags.make

O_FILES = oracle_parser.o
O_FILES += oracle_parser_deal_probe.o
O_FILES += oracle_parser_deal_parser.o
# O_FILES += oracle_stream.o
O_FILES += session_data.o

O_FILES += module_mgt_oracle_parser.o
O_FILES += cJSON.o cJSON_Utils.o utils.o

.PHONY: all clean

all: oracle_parser.so

%.o:%.cpp
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: ../.././utils/cjson/cJSON.c ../.././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: ../.././utils/cjson/cJSON_Utils.c ../.././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

utils.o: ../../core/utils.c ../../include/utils.h
	$(CC) -c $(CFLAGS) $(LIBS_CFLAGS) $<

oracle_parser.so: $(O_FILES)
	$(CC) -o $@ $^ $(LDFLAGS) $(LIBS) $(LIB)



clean:
	rm -f *.o *~ oracle_parser.so
