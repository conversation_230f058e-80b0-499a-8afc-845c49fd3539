/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __UTILS_H__
#define __UTILS_H__

#include <stdbool.h>
#include <stddef.h>
#include <assert.h>
#include <inttypes.h>
#include <pthread.h>

#ifdef __cplusplus
extern "C"
{
#endif

#ifndef ULLONG_MAX
#define ULLONG_MAX ((uint64_t)-1) /* 2^64-1 */
#endif

#ifndef likely
#define likely(x) __builtin_expect(!!(x), 1)
#endif

#ifndef unlikely
#define unlikely(x) __builtin_expect(!!(x), 0)
#endif

#define COUNTOF(x) (sizeof(x) / sizeof(x[0]))
#define SAFE_FREE(x)         \
  do                         \
  {                          \
    if (likely(NULL != (x))) \
      free(x);               \
  } while (0)
#define BSTR_SAFE_FREE(x) SAFE_FREE((void *)(x))

#define LOG_BUF_LEN (4096)

// core dump
#define DBG_CORE_DUMP()   \
  do                      \
  {                       \
    int i = 0;            \
    sscanf("0", "%d", i); \
    printf("%d", i);      \
  } while (0)

#ifndef fastcall
#define FASTCALL(x) x __attribute__((regparm(3)))
#define fastcall __attribute__((regparm(3)))
#endif

#define int_ntoa(x) inet_ntoa(*((struct in_addr *)&x))

#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

#define or_empty(pstr) (NULL==(pstr)?(""):(pstr))
#define or_null(pstr) (NULL==(pstr)?("null"):(pstr))

struct addr
{
  int v; // =0 =4 ipv4 or =1 =6 ipv6
  unsigned short port;
  union
  {
    // for ipv4
    struct
    {
      unsigned int ipv4;
    };
    // for ipv6
    struct
    {
      union
      {
        unsigned char ipv6[16];
        unsigned int iv6[4];
      };
    };
  };
};

typedef struct conn
{
  const unsigned char* p_client_mac;
  const unsigned char* p_server_mac;
  const char *pcap_filename;
  struct addr client;
  struct addr server;
} ConnData;

#ifdef _DEBUG
#define ASSERT(x) assert(x)
#else
#define ASSERT(x) ((void)0)
#endif

#define TCP_STATE_RESET 1
#define TCP_STATE_CLOSE 2
#define TCP_STATE_DATA 3
#define TCP_STATE_TIMEOUT 4

// resource type
#define RESOURCE_TYPE_UNKNOWN           -1
#define RESOURCE_TYPE_YARN               1
#define RESOURCE_TYPE_HDFS               2
#define RESOURCE_TYPE_HIVE               3
#define RESOURCE_TYPE_SPARK              4
#define RESOURCE_TYPE_HBASE              7
#define RESOURCE_TYPE_MONGO              8
#define RESOURCE_TYPE_MYSQL              9
#define RESOURCE_TYPE_ORACLE             10

#define DROP_MOD 512

enum
{
  STREAM_REQ = 0, // 请求
  STREAM_RSP = 1, // 响应
};

struct ssl_stream;
struct tcp_stream;
struct http_stream;
struct mongo_stream;
struct hbase_stream;
struct hive_stream;
struct hdfs_stream;
struct ftp_stream;
struct mysql_stream;
struct yarn_stream;
struct mail_stream;

struct list_session_http_parser;

typedef struct app_stream
{
  int dir;   // STREAM_REQ STREAM_RSP
  int state; // TCP_STATE_xxx
} app_stream_t;

struct StreamData
{
  union
  {

    // for tcp parser
    struct
    {
      struct tcp_stream *a_tcp;
    };

    // for http parser
    struct
    {
      struct http_stream *p_http_stream;
    };

    // for ssl parser
    struct
    {
      struct ssl_stream *p_ssl_stream;
    };

    // for mongo parser
    struct
    {
      struct mongo_stream *p_mongo_stream;
    };
	
    struct{
      struct hbase_stream *p_hbase_stream;
    };

    // for hive parser
    struct
    {
      struct hive_stream *p_hive_stream;
    };

    // for hdfs parser
    struct
    {
      struct hdfs_stream *p_hdfs_stream;
    };

    // for yarn parser
    struct
    {
      struct yarn_stream *p_yarn_stream;
    };

    // for ftp parser
    struct
    {
      struct ftp_stream *p_ftp_stream;
    };

    // for mysql parser
    struct
    {
      struct mysql_stream *p_mysql_stream;
    };

    struct 
    {
      struct oracle_stream *p_oracle_stream;
    };

    struct 
    {
      struct mail_stream *p_mail_stream;
    };

    struct 
    {
      struct smb_stream *p_smb_stream;
    };

    struct 
    {
      struct nfs_stream *p_nfs_stream;
    };

    // for other parser
    struct
    {
      void *dummy;
    };
  };
};

struct ssl_session_mgt;

struct SessionMgtData
{
  union
  {

    // for ssl parser
    struct
    {
      struct ssl_session_mgt *p_ssl_data;
    };

    // for other parser
    struct
    {
      void *dummy;
    };
  };
};

struct UploadMsg;
typedef void (*msg_destroy_func_t)(const struct UploadMsg *);

struct UploadMsg
{
 size_t cb; // 当前结构体大小 可以用于扩展
 bool log_for_analyze;
 bool upload_success;

  void *upload;                    // 上传对象实例
  void *parser;                    // 解析对象实例
  msg_destroy_func_t destroy_func; // 上传完成调用释放内存
  void *userdata;                  // 回调用户参数数据
  void *userdata2;                 // 回调用户参数数据

  //char msgtype[32];   //消息类型，不同类型的消息，可配置发送到不同的地方
  const char *msgtype;
  char context[1024];   //消息上下文关系，相同上下文关系的消息，具有上下文关联。存储时尽量连续存储，避免分片分离，方便处理。
  size_t mem_size; // 占用内存空间
  size_t length;   // 上传数据长度
  const char *s;   // 上传数据指针
  // int priproty;    // 优先级 用于Upload上选择不同优先级的队列（如在Kafka组件中，切换不同优先级的Topic）
};

#if 10

#if (__APPLE__ && __MACH__)

// OS X 线程锁
#define SPIN_DECL(lock) lock
#define SPIN_IMPL(lock) lock = PTHREAD_MUTEX_INITIALIZER
#define SPIN_IMPL_INIT(lock)                  \
  do                                          \
  {                                           \
    spinlock_t t = PTHREAD_MUTEX_INITIALIZER; \
    lock = t;                                 \
  } while (0)
typedef pthread_mutex_t spinlock_t;
#define spin_init(lock, x) pthread_mutex_init(lock, NULL)
#define spin_lock(lock) pthread_mutex_lock(lock)
#define spin_unlock(lock) pthread_mutex_unlock(lock)
#define spin_destroy(lock) pthread_mutex_destroy(lock)
#ifndef PTHREAD_PROCESS_SHARED
#define PTHREAD_PROCESS_SHARED 1
#endif
#ifndef PTHREAD_PROCESS_PRIVATE
#define PTHREAD_PROCESS_PRIVATE 0
#endif

#else

// Linux自旋锁
#define SPIN_DECL(lock) lock
#define SPIN_IMPL(lock) lock
#define SPIN_IMPL_INIT(lock) ((void)0)
typedef pthread_spinlock_t spinlock_t;
#define spin_init(lock, x) pthread_spin_init(lock, x)
#define spin_lock(lock) pthread_spin_lock(lock)
#define spin_unlock(lock) pthread_spin_unlock(lock)
#define spin_destroy(lock) pthread_spin_destroy(lock)

#endif

#else

// 不使用锁
#define SPIN_DECL(lock) lock
#define SPIN_IMPL(lock) lock = 0
#define SPIN_IMPL_INIT(lock) SPIN_IMPL(lock)
typedef int spinlock_t;
#define spin_init(lock, x)
#define spin_lock(lock)
#define spin_unlock(lock)
#define spin_destroy(lock)

#endif
void ntos(uint64_t u64_num, char *str, size_t *p_offset);
void get_ip6addr_str(uint32_t *p_u_ipv6addr, char *p_buf, size_t size);
int get_ms_timeval(uint64_t *p_u64_timaval);
void get_unique_event_id(const char *p_gw_ip, uint64_t u64_ms_timeval, uint32_t u32_ms_index, char *p_unique_code, size_t unique_code_len);
void base64_encode(unsigned char *p_encode_buf, const unsigned char *p_src_buf, int i_src_buf_len);
char *base64_decode(const unsigned char *p_src_buf, int src_len);
void base64_decode_with_len(const unsigned char *p_src_buf, int src_len, 
                          char **pp_des_buf, int *p_des_len);

typedef struct st_conv_paramer
{
    char* inbuf;
    char* original_inbuf;  
    size_t inbufSize;
    char *outbuf;
    char* original_outbuf;
    size_t outbufSize;
} st_conv_paramer;


int convert(const char *fromCode, const char *toCode, st_conv_paramer *paramer);
int convert2utf8(const char *fromCode, st_conv_paramer *paramer);
char* utf16le_2_utf8(const char *from, const int from_l, int *utf8_l);

#ifdef __cplusplus
extern "C" {
#endif

// C++ 便利函数：字符串编码转换
// 返回转换后的字符串，转换失败时返回原字符串
char* convert_string_encoding(const char* input, size_t input_len, const char* from_encoding, const char* to_encoding, size_t* output_len);

// C++ 便利函数：UTF-16 到 UTF-8 转换
// 返回转换后的字符串，转换失败时返回原字符串
char* convert_utf16_to_utf8_string(const char* input, size_t input_len, size_t* output_len);

#ifdef __cplusplus
}
#endif

uint64_t current_time_ms(void);
// 删除目录及子目录中的文件
void remove_dir(const char *path_raw);

const char *my_strstr(const char *p, size_t length, const char *s, size_t s_len);

const char *my_strchr(const char *dest, size_t dest_len, char src);

// 取小端编码数据  !!! 不安全
uint8_t get_le_uint8(const char*);
uint16_t get_le_uint16(const char*);
uint32_t get_le_uint32(const char*);
uint64_t get_le_uint64(const char*);

// 取大端编码数据  !!! 不安全
uint8_t get_be_uint8(const char*);
uint16_t get_be_uint16(const char*);
uint32_t get_be_uint32(const char*);
uint64_t get_be_uint64(const char*);

#ifdef __cplusplus
}
#endif

#endif // __UTILS_H__
