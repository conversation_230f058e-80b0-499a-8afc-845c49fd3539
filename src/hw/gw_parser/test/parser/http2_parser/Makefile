
MK<PERSON>LE_PATH :=$(abspath $(lastword $(MAKE<PERSON>LE_LIST))) 
MKFILE_DIR :=$(patsubst %/, %, $(dir $(MKFILE_PATH)))
MKFILE_DIR_STRIP :=$(strip $(<PERSON><PERSON><PERSON><PERSON>_DIR))
ROOT_DIR :=$(M<PERSON><PERSON>LE_DIR_STRIP)/../../../
ROOT_PATH :=$(ROOT_DIR)
CORE_PATH :=$(ROOT_DIR)/core/

ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else
CC              = gcc
endif
AR              = ar

CFLAGS          = -g -fvisibility=hidden  -fPIC 
CFLAGS          =  -fPIC 
CFLAGS += -I.
CFLAGS += -I$(ROOT_PATH)/test
CFLAGS += -I$(ROOT_PATH)
CFLAGS += -I$(ROOT_PATH)/core
CFLAGS += -I$(ROOT_PATH)/include
CFLAGS += -I$(ROOT_PATH)/liblicutils_c_sdk
CFLAGS += -I$(ROOT_PATH)/utils/queue
CFLAGS += -I$(ROOT_PATH)/utils/lockfree_queue
CFLAGS += -I$(ROOT_PATH)/utils/cjson
CFLAGS += -I$(ROOT_PATH)/parser/http2_parser


#-I. -I../../ -I../../../ 
#-I../../../parser/http_parser/../.././include -I../../../parser/http_parser/../.././utils/queue/ -I../../../parser/http_parser/../.././utils/cjson/ -I../../../parser/http_parser/../.././utils/



UNITTEST = 

LDFLAGS	:=
#LDFLAGS         =  -shared 
LDFLAGS        += -lstdc++ -lz -ldl -lpthread -lgtest -laws-cpp-sdk-core -laws-cpp-sdk-s3 -lfile_type -lnacos-cli


include ${ROOT_DIR}/flags.make


O_UNITTEST_FILES = gw_content.o

O_FILES =  gw_main.o

LIBS           :=
LIBS           +=  -L$(CORE_PATH) -lgw_core -L$(ROOT_PATH)/utils/file_type -lfile_type  -L$(ROOT_PATH)/liblicutils_c_sdk -llicutils -ldl -L../../../../libaws_api_c++/lib64/ -L../../../../utils/file_type/ -L../../../../nacos_c++/lib 
.PHONY: all run_test clean
 

all: $(UNITTEST) $(O_UNITTEST_FILES) $(O_FILES)
	$(CC) $(O_UNITTEST_FILES) $(O_FILES) $(LIBS) $(LDFLAGS)  -lgtest_main  -o gw_content


%.o:%.cpp
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<


#utils.o: $(ROOT_PATH)/core/utils.c $(ROOT_PATH)/core/include/utils.h
#	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

#cJSON.o: $(ROOT_PATH)/utils/cjson/cJSON.c $(ROOT_PATH)/utils/cjson/cJSON.h
#	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

#simple_json.o: $(ROOT_PATH)/utils/cjson/simple_json.c $(ROOT_PATH)/utils/cjson/simple_json.h
#	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

#cJSON_Utils.o: $(ROOT_PATH)/utils/cjson/cJSON_Utils.c $(ROOT_PATH)/utils/cjson/cJSON_Utils.h
#	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

#get_file_type.o: $(ROOT_PATH)/utils/file_type/get_file_type.c $(ROOT_PATH)/utils/file_type/get_file_type.h
#	$(CC) -c $(CFLAGS)	$(LIBS_CFLAGS) $<


gw_main.o: $(ROOT_PATH)/gw_main.cpp
	$(CC) -c $(CPPFLAGS) $<


run_test: all
	./gw_content

clean:
	rm -f *.o *~
	rm -f gw_content

