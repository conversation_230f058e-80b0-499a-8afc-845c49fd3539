#include <stdio.h>
#include <unistd.h>

#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include <getopt.h>

#include <string>
#include <stdexcept>
#include <iostream>
#include <fstream>

#include "gw_content.hpp"

#include "gw_main.h"
#include "gw_common.h"
#include "gw_logger.h"

#include "gw_config.h"

#include "gw_i_parser.h"
#include "http2_parser.h"
#include "worker_queue.h"

#define MAX_RETRY_COUNT 5

void copy(const char* src, const char* dst)
{
    using namespace std;
    ifstream in(src,ios::binary);
    ofstream out(dst,ios::binary);
    if (!in.is_open()) {
        cout << "error open file " << src << endl;
        //exit(EXIT_FAILURE);
        return ;
    }
    if (!out.is_open()) {
        cout << "error open file " << dst << endl;
        //exit(EXIT_FAILURE);
        return ;
    }
    if (strcmp(src, dst) == 0) {
        cout << "the src file can't be same with dst file" << endl;
        //exit(EXIT_FAILURE);
    }
    char buf[2048];
    long long totalBytes = 0;
    while(in)
    {
        //read从in流中读取2048字节，放入buf数组中，同时文件指针向后移动2048字节
        //若不足2048字节遇到文件结尾，则以实际提取字节读取。
        in.read(buf, 2048);    
        //gcount()用来提取读取的字节数，write将buf中的内容写入out流。
        out.write(buf, in.gcount());    
        totalBytes += in.gcount();
    }
    in.close();
    out.close();
    return ;
}


void CContent::SetUp() {
  m_pmain = new CGwMain();

  m_pmain->set_conf_string("{\"parser\": { \
                                        \"verbose\": \"2\" \
                                      , \"ssl_verbose\": \"0x1\" \
                                      , \"pcapfile_mode\": \"1\" \
                                      , \"upload_mode\": \"log\" \
                                      , \"log_filename\": \"/opt/upload_log/log.file\" \
                                      , \"pcap_dir\": \"/opt/pcap/task\" \
                                      , \"tcp_lost_packet_neighbour_ignore\": \"0\" \
                                      , \"http_pipeline_mode\": \"0\" \
                                      , \"stream_debug\": \"1\" \
                                      , \"http_parser_mode\": \"0\" \
                                      , \"http_gzip_mode\" : \"3\" \
                                      , \"file_source_parser_max_bps\" : \"2\" \
                                      , \"tcp_stream_first_cache_max_size\" : \"10485760\" \
                                      , \"tcp_nosyn_init_state\" : \"lost\" \
                                      , \"tcp_streams\" : \"100\" \
                                      , \"tcp_stream_lost_hold_min_bytes\" : \"81920\" \
                                      , \"tcp_nosyn_init_state\" : \"lost\" \
                                      , \"collect_flow_ip_white\" : \"********\" \
                                      , \"only_collect_ip_white\" : \"1\" \
                                      , \"http_content_type_filter\" : \"gif,html\" \
                                      } \
                                , \"plugin\": { \
                                        \"parser_path\": \"/opt/apigw/gwhw/parser/http2_parser\", \
                                        \"upload_path\": \"/opt/apigw/gwhw/upload/log_upload/\", \
                                        \"source_path\": \"/opt/apigw/gwhw/source/file_source/\", \
                                        \"load_files\": \"http2_parser.so:file_source.so:log_upload.so:**\", \
                                        \"parser_dep\": \"ssl:tcp hive:http,tcp ftp:ssl,tcp mongo:tcp http2:ssl,tcp hdfs:http2,tcp\", \
                                        \"parser_type\": \"http:1 ssl:2 hive:3 ftp:4 mongo:5 hdfs:6 http2:7\", \
                                        \"parser_upstream\": \"http:hive http:hdfs http2:hive http2:hdfs\", \
                                        \"\": null \
                                        } \
                                , \"\": null \
                                      }");


  m_pmain->init();
  m_pmain->run();
  
}

void CContent::TearDown() {
  m_pmain->set_quit_signal();
  
  m_pmain->wait_for_stop();

  m_pmain->fini();
  delete m_pmain;
  m_pmain = NULL;

}


TEST_F(CContent, ParsePriority)
{
  
  CGwCommon* p_common =  m_pmain->get_gw_common();
  CParser* parser_array[2] = {0};
  int n = p_common->get_parser_array(parser_array, 2);
  EXPECT_EQ(1, n);

  ASSERT_NE(parser_array[0], nullptr);
  EXPECT_EQ(parser_array[1], nullptr);
  CParser* p_parser = parser_array[0];
  void* p_parser_status = p_parser->get_parser_status();
  EXPECT_NE(p_parser_status, nullptr);
  CHttp2Parser* http2_parser = static_cast<CHttp2Parser*>(p_parser);

  stats_http_t* http_status = static_cast<stats_http_t*>(p_parser_status);

  CWorkerQueue *work_queue =  http2_parser->get_wq_http2_parser_msg();
  
  const char* src = "./priority.pcap";
  const char* dst1 = "/opt/pcap/task/priority.pcap";
  //const char* dst = "/opt/pcap/task/newtask_hh.pcap";

  
  copy(src, dst1);
  //rename(dst1, dst);
  
  

  //while(1)
  for (int i = 0; i < MAX_RETRY_COUNT; ++i)
  {
      if (http_status->cnt_session >= 1) {
          break;
      }
      sleep(1);
  }
  
  EXPECT_EQ(http_status->cnt_session, 1);

  StatsTaskData * stats_data = work_queue->get_stats_task_data();
  EXPECT_EQ(stats_data->cnt, 0);
  
}

TEST_F(CContent, ParsePushPromise)
{
  
  CGwCommon* p_common =  m_pmain->get_gw_common();
  CParser* parser_array[2] = {0};
  int n = p_common->get_parser_array(parser_array, 2);
  EXPECT_EQ(1, n);

  ASSERT_NE(parser_array[0], nullptr);
  EXPECT_EQ(parser_array[1], nullptr);
  CParser* p_parser = parser_array[0];
  void* p_parser_status = p_parser->get_parser_status();
  EXPECT_NE(p_parser_status, nullptr);
  CHttp2Parser* http2_parser = static_cast<CHttp2Parser*>(p_parser);

  stats_http_t* http_status = static_cast<stats_http_t*>(p_parser_status);

  //CWorkerQueue *work_queue =  http2_parser->get_wq_http2_parser_msg();
  
  const char* src = "./pushPromise.pcap";
  const char* dst1 = "/opt/pcap/task/pushPromise.pcap";
  //const char* dst = "/opt/pcap/task/newtask_hh.pcap";

  
  copy(src, dst1);
  //rename(dst1, dst);
  
  

  //while(1)
  for (int i = 0; i < MAX_RETRY_COUNT; ++i)
  {
      if (http_status->m.cnt_m_succ >= 1) {
          break;
      }
      sleep(1);
  }
  
  EXPECT_EQ(http_status->m.cnt_m_succ, 1);

  //StatsTaskData * stats_data = work_queue->get_stats_task_data();
  //EXPECT_EQ(stats_data->cnt, 0);
  
}

TEST_F(CContent, ParseUpgrade)
{
  
  CGwCommon* p_common =  m_pmain->get_gw_common();
  CParser* parser_array[2] = {0};
  int n = p_common->get_parser_array(parser_array, 2);
  EXPECT_EQ(1, n);

  ASSERT_NE(parser_array[0], nullptr);
  EXPECT_EQ(parser_array[1], nullptr);
  CParser* p_parser = parser_array[0];
  void* p_parser_status = p_parser->get_parser_status();
  EXPECT_NE(p_parser_status, nullptr);
  CHttp2Parser* http2_parser = static_cast<CHttp2Parser*>(p_parser);

  stats_http_t* http_status = static_cast<stats_http_t*>(p_parser_status);

  //CWorkerQueue *work_queue =  http2_parser->get_wq_http2_parser_msg();
  
  const char* src = "./upgrade.pcap";
  const char* dst1 = "/opt/pcap/task/upgrade.pcap";
  //const char* dst = "/opt/pcap/task/newtask_hh.pcap";

  
  copy(src, dst1);
  //rename(dst1, dst);
  
  

  //while(1)
  for (int i = 0; i < MAX_RETRY_COUNT; ++i)
  {
      if (http_status->m.cnt_m_succ >= 1) {
          break;
      }
      sleep(1);
  }
  
  EXPECT_EQ(http_status->m.cnt_m_succ, 1);

  //StatsTaskData * stats_data = work_queue->get_stats_task_data();
  //EXPECT_EQ(stats_data->cnt, 0);
  
}

