[Unit]
Description=quanzhi gateway-hw
After=syslog.target network.target
Before=shutdown.target

[Service]
Type=forking
#PIDFile=/opt/apigw/logs/hw-supervisord.pid
ExecStart=/opt/apigw/gwhw/supervisord -d -c /opt/apigw/gwhw/supervisord_hw.conf
ExecReload=/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl reload
ExecStop=/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl shutdown
#Restart=/usr/bin/supervisorctl -c /opt/apigw/gwhw/supervisord_hw.conf restart all
User=root

[Install]
WantedBy=multi-user.target
