#!/bin/bash
###
 # @Author: youweizhi
 # @LastEditors: youweizhi
### 

function env_check()
{
    command -v tar &> /dev/null || (echo "no command \"tar\"" ; exit -1)
    whoami | grep '^root$' &> /dev/null || (echo 'must run as root' ; exit -1)
}

function inst()
{
    # Install
    # wget https://github.com/prometheus/node_exporter/releases/download/v1.1.2/node_exporter-1.1.2.linux-amd64.tar.gz
    tar xf node_exporter-1.1.2.linux-amd64.tar.gz
    install node_exporter-1.1.2.linux-amd64/node_exporter /usr/local/bin/node_exporter

    # add user
    groupadd prometheus
    useradd -g prometheus -m -d /var/lib/prometheus -s /sbin/nologin prometheus

    # service
    cat > /etc/systemd/system/node_exporter.service << EOF
[Unit]
Description=node_exporter
Documentation=https://prometheus.io/
After=network.target
[Service]
Type=sample
User=prometheus
Environment='ARG1=--web.listen-address="127.0.0.1:9100"'
ExecStart=/usr/local/bin/node_exporter \$ARG1
Restart=on-failure
[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable node_exporter
    systemctl start node_exporter

    # quest & push
    cat > /var/lib/prometheus/push_job.sh << EOF
#!/bin/bash
PUSHGATEWAY_SERVER=http://pushgateway-server:19091
NODE_NAME='gwhw'
ARGS='--connect-timeout 1 -m 2'
curl \$ARGS -s localhost:9100/metrics | curl \$ARGS --data-binary @- \$PUSHGATEWAY_SERVER/metrics/job/node-exporter/instance/\$NODE_NAME
EOF

    crontab -l | grep push_job.sh  || echo "*/1 * * * * bash /var/lib/prometheus/push_job.sh" >> /var/spool/cron/root

}

function uninst()
{
    sed -i '/push_job/d' /var/spool/cron/root
    rm -rf /var/lib/prometheus/push_job.sh

    systemctl stop node_exporter
    systemctl disable node_exporter
    rm -rf /etc/systemd/system/node_exporter.service
    systemctl daemon-reload

    userdel -r prometheus
    groupdel prometheus

    rm -rf /usr/local/bin/node_exporter
}

function __main__()
{
    if [[ x"$1" == x"" ]]; then
        env_check && inst
    # elif [[ x"$1" == x"help" ]]; then
    #     echo -e "Usages:"
    #     echo -e "\t\t$0 \t\t install"
    #     echo -e "\t\t$0 uninst\tremove"
    elif [[ x"$1" == x"uninst"]]; then
        uninst
    else
        echo -e "Usages:"
        echo -e "\t\t$0 \t\t install"
        echo -e "\t\t$0 uninst\tremove"
    fi
}