#########################################################################
#
# @par
# This file is provided under a dual BSD/GPLv2 license.  When using or
#   redistributing this file, you may do so under either license.
#
#   GPL LICENSE SUMMARY
#
#   Copyright(c) 2007-2018 Intel Corporation. All rights reserved.
#
#   This program is free software; you can redistribute it and/or modify
#   it under the terms of version 2 of the GNU General Public License as
#   published by the Free Software Foundation.
#
#   This program is distributed in the hope that it will be useful, but
#   WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
#   General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program; if not, write to the Free Software
#   Foundation, Inc., 51 Franklin St - Fifth Floor, Boston, MA 02110-1301 USA.
#   The full GNU General Public License is included in this distribution
#   in the file called LICENSE.GPL.
#
#   Contact Information:
#   Intel Corporation
#
#   BSD LICENSE
#
#   Copyright(c) 2007-2018 Intel Corporation. All rights reserved.
#
#   Redistribution and use in source and binary forms, with or without
#   modification, are permitted provided that the following conditions
#   are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in
#       the documentation and/or other materials provided with the
#       distribution.
#     * Neither the name of Intel Corporation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
#   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
#   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
#   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
#   A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
#   OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
#   SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
#   LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
#   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
#   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
#   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#   OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
#
#########################################################################
########################################################
#
# This file is the configuration for a single dh895xcc_qa
# device.
#
# Each device has 32 independent banks.
#
# - Each bank can contain up to 2 crypto and/or up to 2 data
#   compression services.
#
# - The interrupt for each can be directed to a
#   specific core.
#
########################################################

##############################################
# General Section
##############################################

[GENERAL]
ServicesEnabled = cy;dc
#ServicesEnabled = cy

# Use version 2 of the config file
ConfigVersion = 2

# Look Aside Cryptographic Configuration
cyHmacAuthMode = 1

# Wireless Enable/Disable, valid values: 1,0
WirelessEnabled = 0

# Firmware Location Configuration
Firmware_MofPath = dh895xcc/mof_firmware.bin
Firmware_MmpPath = dh895xcc/mmp_firmware.bin

# Default selection of CY concurrent requests.
CyNumConcurrentSymRequests = 512
CyNumConcurrentAsymRequests = 64

# Default number of DC concurrent requests.
DcNumConcurrentRequests = 512

#Statistics, valid values: 1,0
statsGeneral = 1
statsDc = 1
statsDh = 1
statsDrbg = 1
statsDsa = 1
statsEcc = 1
statsKeyGen = 1
statsLn = 1
statsPrime = 1
statsRsa = 1
statsSym = 1

# Debug feature, if set to 1 it enables additional entries in /proc filesystem
ProcDebug = 1

# Enables or disables Single Root Complex IO Virtualization.
# If this is enabled (1) then SRIOV and VT-d need to be enabled in
# BIOS and there can be no Cy or Dc instances created in PF (Dom0).
# If this is disabled (0) then SRIOV and VT-d needs to be disabled
# in the BIOS and Cy and/or Dc instances can be used in PF (Dom0)
SRIOV_Enabled = 0

#######################################################
#
# Logical Instances Section
# A logical instance allows each address domain
# (kernel space and individual user space processes)
# to configure rings (i.e. hardware assisted queues)
# to be used by that address domain and to define the
# behavior of that ring.
#
# The address domains are in the following format
# - For kernel address domains
#       [KERNEL]
# - For user process address domains
#   [xxxxx]
#   Where xxxxx may be any ascii value which uniquely identifies
#   the user mode process.
#   To allow the driver correctly configure the
#   logical instances associated with this user process,
#   the process must call the icp_sal_userStartMultiProcess(...)
#   passing the xxxxx string during process initialisation.
#   When the user space process is finished it must call
#   icp_sal_userStop(...) to free resources.
#   NumProcesses will indicate the maximum number of processes
#   that can call icp_sal_userStartMultiProcess on this instance.
#   Warning: the resources are preallocated: if NumProcesses
#   is too high, the driver will fail to load
#
# Items configurable by a logical instance are:
# - Name of the logical instance
# - The response mode associated wth this logical instance
#   For instance in the kernel space :
#       0 for IRQ
#       1 for poll mode
#   For instance in the user space :
#       0 for IRQ (deprecated, please do not use it anymore)
#       1 for poll mode
#       2 for epoll mode (event based polling mode)
# - The core the instance is affinitized to (optional)
#
# The format of the logical instances are:
# - For crypto:
#               Cy<n>Name = "xxxx"
#               Cy<n>IsPolled = 1|2
#               Cy<n>CoreAffinity = 0-7
#
# - For Data Compression
#               Dc<n>Name = "xxxx"
#               Dc<n>IsPolled = 1|2
#               Dc<n>CoreAffinity = 0-7
#
# Where:
#       - n is the number of this logical instance starting at 0.
#       - xxxx may be any ascii value which identifies the logical instance.
#
# Note: for user space processes, a list of values can be specified for
# the core affinity: for example
#              Cy0CoreAffinity = 0-7
# These comma-separated lists will allow multiple processes to use
# different accelerators and cores, and will wrap around the numbers
# in the list. In the above example, process 0 will have affinity 0,
# and process 1 will have affinity 2 etc.
#
########################################################

# This flag is to enable SSF features (CNV and BnP)
StorageEnabled = 0

##############################################
# Kernel Instances Section
##############################################
[KERNEL]
NumberCyInstances = 0
NumberDcInstances = 0

##############################################
# User Process Instance Section
##############################################
[SHIM]
NumberCyInstances = 2
NumberDcInstances = 2
NumProcesses = 8
LimitDevAccess = 1

# Crypto - User space
Cy0Name = "UserCY0"
Cy0IsPolled = 2
Cy0CoreAffinity = 0-7

# Crypto - User space
Cy1Name = "UserCY1"
Cy1IsPolled = 2
Cy1CoreAffinity = 0-7

# Crypto - User space
Cy2Name = "UserCY2"
Cy2IsPolled = 2
Cy2CoreAffinity = 0-7

# Crypto - User space
Cy3Name = "UserCY3"
Cy3IsPolled = 2
Cy3CoreAffinity = 0-7

# Data Compression - User space
Dc0Name = "UserDC0"
Dc0IsPolled = 2
Dc0CoreAffinity = 0-7

# Data Compression - User space
Dc1Name = "UserDC1"
Dc1IsPolled = 2
Dc1CoreAffinity = 0-7

# Data Compression - User space
Dc2Name = "UserDC2"
Dc2IsPolled = 2
Dc2CoreAffinity = 0-7

# Data Compression - User space
Dc3Name = "UserDC3"
Dc3IsPolled = 2
Dc3CoreAffinity = 0-7
