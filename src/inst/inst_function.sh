#!/bin/bash

USERNAME=apigw
FLAG=""
product=""
productVersion=""
processor=0
mem=0

DEST_PATH=/opt/apigw/gwhw/
ETC_PATH=/opt/data/apigw/gwhw/

# 函数调用日志
function func_log()
{
	func=$1
	shift
	echo Enter ${func} ...
	${func} $@
	echo Leave ${func} .
	echo
}

function wait_rpm_status()
{
	while [[ true ]];
	do
		rpm -qa "$1" | grep "$1" >/dev/null && break
	done
}

# 检查是否按装指定的依赖包
function chk_pkg()
{
	if [[ $(rpm -qa "$1" | wc -l) -eq 0 ]]; then
		echo "Error: $1 package not found!"
		exit 1;
	fi

	return ;
}

#function conf_vsftp()
#{
#	if [[ ! -f /etc/vsftpd/vsftpd.conf ]];then
#		return
#	fi
#
#	if [[ -f /opt/data/apigw/gwhw/gw_parser.conf ]];then
#		if [[ `grep "\"pcap_dir\":" /opt/data/apigw/gwhw/gw_parser.conf | wc -l` -gt 0 ]];then
#			pcap_dir=$(grep "\"pcap_dir\"" /opt/data/apigw/gwhw/gw_parser.conf | awk -F ':' '{print $2}' | sed 's,^ *,,' | sed 's,^\",,' | sed 's,\".*$,,')
#		else
#			pcap_dir=/opt/pcap/
#		fi
#
#		num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "local_root=" | awk '{print $1}')
#		if [ -n "$num" ]
#		then
#			# echo "delete \"local_root=\" line num=$num"
#			sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#		fi
#		echo "local_root=$pcap_dir" >> /etc/vsftpd/vsftpd.conf
#
#		if [[ `grep "anonymous_enable=YES" /etc/vsftpd/vsftpd.conf  | wc -l` -eq 1 ]];then
#			sed -i 's,nonymous_enable=YES,nonymous_enable=NO,' /etc/vsftpd/vsftpd.conf
#		fi
#	fi
#
#	if [ -e /etc/pam.d/vsftpd ]
#	then
#		# echo "notes /etc/pam.d/vsftpd"
#		num=$(cat -n /etc/pam.d/vsftpd | awk '{if ("pam_shells.so" == $4) {print $1}}')
#		if [ -n "$num" ]
#		then
#			sed -i "$num s/^/# /g" /etc/pam.d/vsftpd
#		fi
#	fi
#
#	pcapDir=""
#	if [ -d /opt/pcap/task/ ]
#	then
#		pcapDir="/opt/pcap/task/"
#		# echo "pcap dir=/opt/pcap/task/"
#		chmod 777 /opt/pcap/task/
#	else
#		# echo "find directory from config"
#		pcapDir=$(cat /opt/data/apigw/gwhw/gw_parser.conf | grep "\"pcap_dir\"" | cut -d : -f 2 | cut -d \" -f 2)
#	fi
#
#	# echo "pcap dir=$pcapDir"
#
#	if [ -n "$pcapDir" ]
#	then
#		chmod 777 $pcapDir
#		isExist=$(cat /etc/rc.local | grep "chmod 777 $pcapDir")
#		if [ -z "$isExist" ]
#		then
#			# echo "add command to rc.local"
#			echo "chmod 777 $pcapDir" >> /etc/rc.local
#		fi
#	fi
#
#	echo "LfQKae1d" | passwd ftp --stdin
#
#	num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "userlist_enable=" | awk '{print $1}')
#	if [ -n "$num" ]
#	then
#		# echo "delete \"userlist_enable=\" line num=$num"
#		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#	fi
#	echo "userlist_enable=YES" >> /etc/vsftpd/vsftpd.conf
#
#	num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "userlist_deny=" | awk '{print $1}')
#	if [ -n "$num" ]
#	then
#		# echo "delete \"userlist_deny=\" line num=$num"
#		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#	fi
#	echo "userlist_deny=NO" >> /etc/vsftpd/vsftpd.conf
#
#	# 写入允许访问用户
#	if [ $(cat /etc/vsftpd/user_list | grep ^ftp | wc -l) -eq 0 ]
#	then
#		echo "ftp" >> /etc/vsftpd/user_list
#	fi
#
#	num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "chroot_local_user=" | awk '{print $1}')
#	if [ -n "$num" ]
#	then
#		# echo "delete \"chroot_local_user=\" line num=$num"
#		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#	fi
#	echo "chroot_local_user=NO" >> /etc/vsftpd/vsftpd.conf
#
#	num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "chroot_list_enable=" | awk '{print $1}')
#	if [ -n "$num" ]
#	then
#		# echo "delete \"chroot_list_enable=\" line num=$num"
#		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#	fi
#	echo "chroot_list_enable=YES" >> /etc/vsftpd/vsftpd.conf
#
#	num=$(cat -n /etc/vsftpd/vsftpd.conf | grep "allow_writeable_chroot=" | awk '{print $1}')
#	if [ -n "$num" ]
#	then
#		# echo "delete \"allow_writeable_chroot=\" line num=$num"
#		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
#	fi
#	echo "allow_writeable_chroot=YES" >> /etc/vsftpd/vsftpd.conf
#
#	rm -rf /etc/vsftpd/chroot_list
#	touch /etc/vsftpd/chroot_list
#	echo "ftp" >> /etc/vsftpd/chroot_list
#
#	# 开机启动
#	systemctl enable vsftpd
#	systemctl restart vsftpd
#}

function start_crontab()
{
    if [[ `crontab -l|grep 'systemctl restart gwhw'|wc -l` -eq 0 ]]; then
		echo "adding crontab task for gwhw"
        echo "59 01 * * * systemctl restart gwhw" >> /var/spool/cron/root
    fi
}

function inst_config_systemctl()
{
	if [[ -d /lib/systemd/system/ ]]; then
		cp -f gwhw.service /lib/systemd/system/
		systemctl daemon-reload
		systemctl enable gwhw
		systemctl restart gwhw
	else
		sudo /opt/apigw/gwhw/supervisord -c "${DEST_PATH}./supervisord_hw.conf"
	fi

	## 新版linux不再使用initd管理系统，而是使用systemd
	if [[ -d /etc/systemd/system ]]; then
		# 检查rc-local服务是否存在，如果不存在则创建
		if [ ! -f /etc/systemd/system/rc-local.service ]; then
			cp -f rc-local.service /etc/systemd/system/
		fi

		# 确保rc.local文件存在并具有可执行权限
		if [[ ! -f /etc/rc.local ]]; then
			touch /etc/rc.local
			echo "#!/bin/bash" > /etc/rc.local
			echo "exit 0" >> /etc/rc.local
			chmod +x /etc/rc.local
		elif [[ ! -x /etc/rc.local ]]; then
			chmod +x /etc/rc.local
		fi

		# 检查rc-local服务状态
		rc_status=$(systemctl is-active rc-local 2>/dev/null)
		if [[ "$rc_status" != "active" ]]; then
			# 如果服务不活跃，尝试重启
			sudo systemctl daemon-reload
			sudo systemctl enable rc-local

			# 再次检查服务状态
			sudo systemctl restart rc-local
			rc_status=$(systemctl is-active rc-local 2>/dev/null)

			# 如果服务仍然不活跃，可能是格式错误，修复格式并添加必要的shebang
			if [[ "$rc_status" != "active" ]]; then
				if [[ -f /etc/rc.local ]]; then
					# 检查文件第一行是否包含shebang
					if ! grep -q "^#!/bin/bash" /etc/rc.local && ! grep -q "^#!/bin/sh" /etc/rc.local; then
						# 如果没有shebang，添加它
						sudo sed -i '1i#!/bin/bash' /etc/rc.local
						sudo chmod +x /etc/rc.local
					fi

					# 检查文件是否以exit 0结尾
					if ! grep -q "exit 0" /etc/rc.local; then
						# 如果没有exit 0，添加它
						echo "exit 0" >> /etc/rc.local
					fi

					# 重新加载并启动服务
					sudo systemctl daemon-reload
					sudo systemctl enable rc-local
					sudo systemctl restart rc-local
				fi
			fi
		fi
	fi

	# 添加必要的脚本到rc.local
	if [[ `grep 'bind_eth.sh' /etc/rc.local | grep 'gwhw' | wc -l` -eq 0 ]]; then
		# 确保不覆盖exit 0语句，在文件末尾exit 0之前添加
		if grep -q "exit 0" /etc/rc.local; then
			sudo sed --follow-symlinks -i '/exit 0/i sudo bash \/opt\/apigw\/gwhw\/tools\/bind_eth.sh dpdk_bind' /etc/rc.local
		else
			echo "sudo bash /opt/apigw/gwhw/tools/bind_eth.sh dpdk_bind 1> /dev/null" >> /etc/rc.local
			echo "exit 0" >> /etc/rc.local
		fi
	fi
}

function install_agent_server()
{
	if [ -f qzkj_agent_server.zip ]; then
		unzip -o -q qzkj_agent_server.zip
	elif [ -f qzkj_agent_server.tar.gz ]; then
		tar -xzf qzkj_agent_server.tar.gz
	fi

	cd qzkj_agent_server && bash install.sh
	cd -
}
function check_base_env()
{
	systemctl stop gwhw 2> /dev/null

	# cpu
	processor=$(cat /proc/cpuinfo | grep processor | wc -l)
	if [ -z "$processor" ]
	then
		echo "get processor fail"
		processor=1
	fi
	echo "processor="$processor

	# memory
	num=$(cat /sys/devices/system/memory/block_size_bytes)
	((num="0x$num"))
	# echo $num
	size=$(ls /sys/devices/system/memory/ | grep "memory" | xargs -i cat /sys/devices/system/memory/{}/online | awk '{if ("1" == $1) {print $1}}' | wc -l)
	mem=$[num * size]
	if [ -z "$mem" ]
	then
		echo "get mem fail"
		mem=$[1024 * 1024 * 1024]
	fi
	echo memory=$mem
}

function inst_plugin_config()
{
	parser_sos="ftp_parser.so  http_parser.so http2_parser.so grpc_parser.so ssl_parser.so mail_parser.so smb_parser.so nfs_parser.so oracle_parser.so"
	for parser_so in $parser_sos;
	do
		head=`echo $parser_so | awk -F '.' '{print $1}'`
		dest_path="${DEST_PATH}.//parser/$head"
		if [ ! -d $dest_path ]; then
			mkdir -p $dest_path
		fi
		sudo cp -f parser/$parser_so $dest_path
	done


	src_sos="file_source.so dpdk_source.so nic_source.so pcap_source.so"
	for src_so in $src_sos;
	do
		head=`echo $src_so | awk -F '.' '{print $1}'`
		dest_path="${DEST_PATH}./source/$head"
		if [ ! -d $dest_path ]; then
			mkdir -p $dest_path
		fi
		sudo cp -f source/$src_so $dest_path
	done


	upload_sos="log_upload.so kafka_upload.so diy_upload.so"
	for upload_so in $upload_sos;
	do
		head=`echo $upload_so | awk -F '.' '{print $1}'`
		dest_path="${DEST_PATH}./upload/$head"
		if [ ! -d $dest_path ]; then
			mkdir -p $dest_path
		fi
		sudo cp -f upload/$upload_so $dest_path
	done

	# 配置文件强制覆盖
	isRightProductVersion="true"

	version_main=$(echo "$productVersion" | cut -d . -f 1)
	version_sub=$(echo "$productVersion" | cut -d . -f 2)
	if [ "API" == "$product" ]; then
		if [ $version_main -eq 1 ] && [ $version_sub -eq 4 ]; then
			sudo cp -f gw_parser_deploy_api_1.4.conf "${ETC_PATH}./"gw_parser.conf
		elif [ $version_main -eq 2 ] && [ $version_sub -eq 0 ]; then
			sudo cp -f gw_parser_deploy_api_2.0.conf "${ETC_PATH}./"gw_parser.conf
		elif [ $version_main -eq 2 ] && [ $version_sub -eq 4 ]; then
			sudo cp -f gw_parser_deploy_api_2.4.conf "${ETC_PATH}./"gw_parser.conf
		elif [ $version_main -eq 3 ] && [ $version_sub -eq 0 ]; then
			sudo cp -f gw_parser_deploy_api_3.0.conf "${ETC_PATH}./"gw_parser.conf
		elif [ $version_main -eq 3 ] && [ $version_sub -ge 1 ]; then
			# 当前默认api版本大于等于3.1都使用api3.1的配置文件
			sudo cp -f gw_parser_deploy_api_3.1.conf "${ETC_PATH}./"gw_parser.conf
		else
			isRightProductVersion="false"
		fi
	elif [ "AUDIT" == "$product" ]; then
		if [ $version_main -eq 2 ] && [ $version_sub -eq 2 ]; then
			sudo cp -f gw_parser_deploy_audit_2.2.conf "${ETC_PATH}./"gw_parser.conf
		elif [ $version_main -eq 2 ] && [ $version_sub -eq 7 ]; then
			sudo cp -f gw_parser_deploy_audit_2.7.conf "${ETC_PATH}./"gw_parser.conf
		else
			isRightProductVersion="false"
		fi
	elif [ "TOOL" == "$product" ]; then
		sudo cp -f gw_parser_tools.conf "${ETC_PATH}./"gw_parser.conf
	fi

	if [ "false" == "$isRightProductVersion" ]; then
		echo "Warning: unknow version $productVersion"
		echo -e "\n Try -h for more help infomation"
		exit 1
	fi

	lineNum=$(cat -n /opt/data/apigw/gwhw/gw_parser.conf | grep "plugin" | awk '{print $1}')
	lineNum=$[lineNum + 3]
	if [ "Performance" == "$FLAG" ]
	then
        sed -i '/"source_path":/{
        /pcap/{
            /dpdk_source/!s#"source_path": "\([^"]*pcap[^"]*\)"#"source_path": "source/dpdk_source/:\1"#
        }
        }'  /opt/data/apigw/gwhw/gw_parser.conf

        sed -i '/"load_files":/{
        /pcap_source\.so/{
            /dpdk_source\.so/!s#"load_files": "\([^"]*pcap_source\.so[^"]*\)"#"load_files": "dpdk_source.so:\1"#
        }
        }'  /opt/data/apigw/gwhw/gw_parser.conf
	fi

	lineNum=$(cat -n /opt/data/apigw/gwhw/gw_parser.conf | grep "gw_ip" | awk '{print $1}')
	if [  "" != "$lineNum" ]
	then
		ethName=$(ip route | grep "default via" | awk '{print $5}' | head -1)
		localIp=$(ip addr show $ethName | grep inet | grep -v "inet6" | awk '{print $2}' | head -1)
		localIp=${localIp%/*}
		sed -i "${lineNum}s/127.0.0.1/$localIp/g" /opt/data/apigw/gwhw/gw_parser.conf
	fi

	if [ "TOOL" == "$product" ]
	then
		FLAG="NIC"
	fi

	if [[ ! -f "${ETC_PATH}./"user_info_rule.conf ]]; then
		sudo cp -f user_info_rule.conf "${ETC_PATH}./"
	fi
	if [[ ! -f "${ETC_PATH}./"forward_info_rule.conf ]]; then
		sudo cp -f forward_info_rule.conf "${ETC_PATH}./"
	fi

	if [[ ! -f "${ETC_PATH}./"gw_parser_5g.conf ]];then
		sudo cp -f gw_parser_5g.conf "${ETC_PATH}./"
	fi
}

function help()
{
    echo Usage:
    echo -e "\t -t : installation mode, choose one from the following or -p TOOL [must]"
	echo -e "\t\t Performance"
	echo -e "\t\t Compatible"
    echo -e "\t -p : installed product, choose one from the following [dafault API]"
	echo -e "\t\t API"
	echo -e "\t\t AUDIT"
	echo -e "\t\t TOOL"
	echo -e "\t -v : product version [default 3.1]"
	echo -e "\t -h : help infomation\n"
	echo -e "\t [example] bash inst.sh -t Compatible -p API -v 2.0"
}

function parse_parameter()
{
	if [ $# -ge 1 ]
	then
		while [ -n "$1" ]
		do
			case $1 in
			-t)
				if [ $# -ge 2 ]
				then
					FLAG=$2
					shift 2
				else
					shift 1
				fi
				;;
			-p)
				if [ $# -ge 2 ]
				then
					product=$(echo $2 | tr "[:lower:]" "[:upper:]")
					shift 2
				else
					shift 1
				fi
				;;
			-v)
				if [ $# -ge 2 ]
				then
					productVersion=$2
					shift 2
				else
					shift 1
				fi
				;;
			-h)
				help
				exit 1
				;;
			*)
				echo -e "Warning: unknow option $1\n"
				help
				exit 1
				;;
			esac
		done
	else
		help
		exit 1
	fi

	isParamterOK="true"

	if [ "" == "$FLAG" ]
	then
		if [ "TOOL" != "$product" ]
		then
			echo "Warning: installation mode must supply, choose one from the following, or -p TOOL"
			echo -e "\t Performance"
			echo -e "\t Compatible"
			isParamterOK="false"
		fi
	else
		if [ "Performance" != "$FLAG" ] && [ "Compatible" != "$FLAG" ]
		then
			echo "Warning: please select a right installation mode, choose one from the following, or -p TOOL"
			echo -e "\t Performance"
			echo -e "\t Compatible"
			isParamterOK="false"
		fi
	fi

	if [ "" == "$product" ]
	then
		echo "Warning: product do not supply, default install API"
		product="API"
	fi

	if [ "API" != "$product" ] && [ "AUDIT" != "$product" ] && [ "TOOL" != "$product" ]
	then
		echo "Warning: please select a right product, choose one from the following"
		echo -e "\t API"
		echo -e "\t AUDIT"
		echo -e "\t TOOL"
		isParamterOK="false"
	fi

	if [ "" == "$productVersion" ]
	then
		echo "Warning: product version do not supply, default install 3.1"
		productVersion="3.1"
	fi

	isRightFormat="true"

	temp=$(echo "$productVersion" | sed 's/[^0-9]/./g')
	if [ "$temp" == "$productVersion" ]
	then
		temp=$(echo "$productVersion" | cut -d . -f 3)
		if [ "" != "$temp" ] && [ "$temp" != "$productVersion" ]
		then
			isRightFormat="false"
		fi
	else
		isRightFormat="false"
	fi

	if [ "false" == $isRightFormat ]
	then
		echo "Warning: product version is not a number"
		isParamterOK="false"
	fi

	if [ "false" == "$isParamterOK" ]
	then
		echo -e "\n Try -h for more help infomation"
		exit 1
	fi
}
