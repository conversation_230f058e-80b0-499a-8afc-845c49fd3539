#!/bin/bash
source ./inst_function.sh

# 配置 系统环境
function conf_system()
{
	# /etc/security/limits.conf 文件
	# * - nofile 4096
	# * - nofile 65536
	if [[ `grep '^[^#].*nofile' /etc/security/limits.conf | wc -l` -eq 0 ]]; then
		echo '* - nofile 65536' >> /etc/security/limits.conf >/dev/null 2>&1
	fi

	# /etc/selinux/config 文件
	if [[ -f /etc/selinux/config ]]; then
		 sed -i -e 's/^SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config
	fi
	# 关闭 selinux
	 setenforce 0 >/dev/null 2>&1

	# /etc/sysctl.conf 文件
	if [[ -f /etc/sysctl.conf ]]; then
		if [[ `grep kernel.nmi_watchdog /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			echo 'kernel.nmi_watchdog=0' >> /etc/sysctl.conf >/dev/null 2>&1
		fi
		if [[ `grep kernel.hung_task_timeout_secs /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			echo 'kernel.hung_task_timeout_secs=0' >> /etc/sysctl.conf >/dev/null 2>&1
		fi

		if grep -q '^kernel.core_pattern' /etc/sysctl.conf; then
		# 存在则替换
			sed -i 's|^kernel\.core_pattern=.*|kernel.core_pattern=core.%p|' /etc/sysctl.conf
		else
		# 不存在则追加
			echo 'kernel.core_pattern=core.%p' >> /etc/sysctl.conf
		fi
		 sysctl -p
	fi

	grep -q '^ulimit -c' ~/.bashrc && sed -i 's|^ulimit -c.*|ulimit -c unlimited|' ~/.bashrc || echo 'ulimit -c unlimited' >> ~/.bashrc
	bash -c "source ~/.bashrc"
	# 关闭firewalld或iptables并移除启动项
	command -v systemctl &>/dev/null && systemctl stop firewalld >/dev/null 2>&1 && systemctl disable firewalld >/dev/null 2>&1 || (service iptables stop >/dev/null 2>&1 && chkconfig iptables off >/dev/null 2>&1)

	# # 调整开机脚本权限
	# if [[ ! -x /etc/rc.d/rc.local ]]; then
	# 	 chmod +x /etc/rc.d/rc.local
	# fi

	# 禁用中断分配服务
	 systemctl stop irqbalance >/dev/null 2>&1;
	 systemctl disable irqbalance >/dev/null 2>&1;
	# 禁用无关的服务
	 systemctl stop lvm2-monitor.service >/dev/null 2>&1
	 systemctl stop tuned.service >/dev/null 2>&1

	# 增加普通用户
	 useradd ${USERNAME} >/dev/null 2>&1

	# 增加license文件路径
	 mkdir -p "/opt/licutils/"

	# 增加日志目录
	 mkdir -p "/opt/apigw/logs/"
	 chown -R ${USERNAME}  "/opt/apigw/logs/"

	# 增加运行状态目录
	 mkdir -p "/opt/apigw/run/"
	 chown -R ${USERNAME}  "/opt/apigw/run/"

	# 增加配置目录
     mkdir -p "/opt/data/apigw/gwhw/"
	 chown -R ${USERNAME}  "/opt/data/apigw/gwhw/"

	if [ $processor -lt 4 ]
	then
		cpuLimit=100000
	else
		cpuLimit=$[processor * 100000 / 4]
	fi

	if [ $mem -lt ********** ]
	then
		memLimit=$[1024 * 1024 * 1024]
	else
		memLimit=$[mem / 4]
	fi

	# 配置cgroup
	# if [ $(grep "group gwhw" /etc/cgconfig.conf | wc -l) -eq 1 ]
	# then
	# 	lineNumStart=$(cat -n /etc/cgconfig.conf | grep "group gwhw" | awk '{print $1}')
	# 	lineNumEnd=$[lineNumStart + 8]
	# 	sed -i "$lineNumStart, $lineNumEnd d" /etc/cgconfig.conf
	# fi

	# echo "group gwhw{" >> /etc/cgconfig.conf
	# echo -e "\tcpu{" >> /etc/cgconfig.conf
	# echo -e "\t\tcpu.cfs_quota_us = 1600000;" >> /etc/cgconfig.conf
	# echo -e "\t\tcpu.cfs_period_us = 100000;" >> /etc/cgconfig.conf
	# echo -e "\t}" >> /etc/cgconfig.conf
	# echo -e "\tmemory{" >> /etc/cgconfig.conf
	# echo -e "\t\tmemory.limit_in_bytes = 12884901888;" >> /etc/cgconfig.conf
	# echo -e "\t}" >> /etc/cgconfig.conf
	# echo "}" >> /etc/cgconfig.conf

	# if [ $(grep "/opt/apigw/gwhw/gw_parser" /etc/cgrules.conf | wc -l) -eq 0 ]
	# then
	# 	echo "*:/opt/apigw/gwhw/gw_parser cpu,memory gwhw" >> /etc/cgrules.conf
	# fi

	 # systemctl restart cgconfig > /dev/null 2>&1
	 # systemctl restart cgred > /dev/null 2>&1
	 # systemctl enable cgconfig > /dev/null 2>&1
	 # systemctl enable cgred > /dev/null 2>&1

	# 关闭rpcbind服务
	systemctl stop rpcbind.socket > /dev/null 2>&1
	systemctl stop rpcbind > /dev/null 2>&1
	systemctl disable rpcbind.socket > /dev/null 2>&1
	systemctl disable rpcbind > /dev/null 2>&1

	# 直接执行echo "-1" > /proc/sys/kernel/sched_rt_runtime_us会失败，原因未知
	echo -1 > /proc/sys/kernel/sched_rt_runtime_us

	return
}

# 安装主程序
function inst_gw_parser()
{
	check_base_env

	# 创建目录
	mkdir -p /opt/apigw/
	mkdir -p /opt/urlbase/
	mkdir -p /opt/licutils/
	mkdir -p /opt/apigw/run/
	mkdir -p /opt/apigw/gwhw/
	mkdir -p /opt/apigw/logs/
	mkdir -p /opt/apigw/gwhw/logs/
	mkdir -p /opt/apigw/gwhw/kmod/
	mkdir -p /opt/data/apigw/gwhw/

	sudo useradd ${USERNAME}

	# rpm包安装
	# cd rpm/
	# rpm -Uvh --force *rpm
	# cd -

	# 拷贝主进程、配置文件、插件和库文件
	cp -f gw_parser $DEST_PATH
	cp -f magic.mgc $DEST_PATH
	cp -f version.txt $DEST_PATH
	cp -f supervisord $DEST_PATH
	cp -f supervisord_hw.conf $DEST_PATH
	cp -f gw_parser.conf $ETC_PATH
	cp -f forward_info_rule.conf $ETC_PATH
	cp -f user_info_rule.conf $ETC_PATH
	cp -rf stats_srv/ $DEST_PATH
	cp -rf tools/ $DEST_PATH

	if [ $(command -v lspci | wc -l) -eq 0 ]; then
		cp -f tools/lspci /usr/bin/
	fi

	if [ $(command -v tcpdump | wc -l) -eq 0 ]; then
		cp -f tools/tcpdump /usr/bin/
	fi

	if [ $(command -v nload | wc -l) -eq 0 ]; then
		cp -f tools/nload /usr/bin/
	fi

	inst_plugin_config

	if [[ -f dpdk_black_list.txt && ! -f /opt/data/apigw/gwhw/dpdk_black_list.txt ]]; then
		sudo cp -f dpdk_black_list.txt /opt/data/apigw/gwhw/
	fi

	if [[ ! -d "/opt/urlbase/" ]];then
		sudo mkdir -p "/opt/urlbase/"
		sudo cp -f url_filter_base.file /opt/urlbase/
	fi

	if [[ ! -f "/opt/urlbase/url_filter_base.file" ]];then
		sudo cp -f url_filter_base.file /opt/urlbase/
	fi

	# 设置pcap目录大小
	sh $DEST_PATH/tools/agent_upload_quota.sh

	# 创建软链接
	if [ -d "${DEST_PATH}./lib" ]; then
		rm -rf "${DEST_PATH}./lib/"
	fi
	cp -rf lib/ $DEST_PATH
	cd $DEST_PATH/lib/
	ln -sf libiconv.so.2.6.1 libiconv.so.2
	cd -

	# 安装openssl库
	if [[ -f openssl-bin-1.1.1w.tar.gz ]]; then
		if [[ ! -d /opt/openssl/ ]]; then
			sudo mkdir /opt/openssl/
		fi
		sudo tar xf openssl-bin-1.1.1w.tar.gz -C /opt/openssl/ >/dev/null 2>&1
	fi

	sudo chown -R ${USERNAME}  "${DEST_PATH}./"

	#配置vsftp配置文件
	# conf_vsftp


	# 配置systemctl
	inst_config_systemctl
}

function main()
{
	func_log  parse_parameter $@
	func_log  conf_system
	func_log  inst_gw_parser
	func_log  start_crontab
	func_log  install_agent_server
	return
}

main $@
