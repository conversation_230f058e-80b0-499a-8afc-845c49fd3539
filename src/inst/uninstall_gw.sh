#!/bin/sh
USERNAME=apigw
WATCH_PROCESS_PATH=/opt/apigw/watch_process/
GW_PARSER_PATH=/opt/apigw/gwhw/
CONF_PATH=/opt/data/apigw/gwhw/


#检测gwhw服务是否存在
function check_gwhw()
{
    if [[ `sudo systemctl status gwhw | grep Active | grep -i running | wc -l` -eq 1 ]];then
        sudo systemctl stop gwhw
        if [[ -n $(/opt/apigw/gwhw/tools/lspci -d 8086::0200) ]]; then
            sudo bash /opt/apigw/gwhw/tools/dpdk_unbind.sh
        fi
		# sudo rmmod pf_ring >/dev/null 2>&1
    fi
}

#删除安装gwhw服务的系统配置
function del_conf_system()
{
    #删除用户
    if [[ `grep "${USERNAME}" /etc/passwd | wc -l` -eq 1 ]];then
        sudo userdel ${USERNAME}
    fi

    #删除日志目录
    if [[ -d "/opt/apigw/logs" ]];then
        sudo rm -rf "/opt/apigw/logs/"
    fi

    #删除运行目录
    if [[ -d "/opt/apigw/run/" ]];then
        sudo rm -rf "/opt/apigw/run/"
    fi

    #删除配置目录
    #if [[ -d "/opt/data/apigw/gwhw/" ]];then
        #sudo rm -rf "/opt/data/apigw/gwhw/"
    #fi

    if [[ -f "${CONF_PATH}./"gw_parser.conf ]];then
        sudo rm -rf "${CONF_PATH}/"gw_parser.conf
    fi

    if [[ -f "${CONF_PATH}./"forward_info_rule.conf ]];then
        sudo rm -rf "${CONF_PATH}/"forward_info_rule.conf
    fi

    if [[ -f "${CONF_PATH}./"user_info_rule.conf ]];then
        sudo rm -rf "${CONF_PATH}/"user_info_rule.conf
    fi

    if [[ -f "${CONF_PATH}./"gw_parser_5g.conf ]];then
        sudo rm -rf "${CONF_PATH}./"gw_parser_5g.conf
    fi

    #判断目录是否是空
    if [[ "`ls -A ${CONF_PATH}`" = "" ]];then
        sudo rm -rf ${CONF_PATH}
        if [[ "`ls -A /opt/data/apigw/`" = "" ]];then
            sudo rm -rf /opt/data/apigw/
        fi
    fi

    #echo "del openssl"
    # 删除openssl目录
    if [[ -d "/opt/openssl/" ]];then
        sudo rm -rf /opt/openssl/
    fi

    # uninstall qat
    #echo "del firmware"
    if [[ -d "/lib/firmware/qat_fw_gwhw_backup" ]]; then
        BIN_LIST="qat_895xcc.bin qat_895xcc_mmp.bin qat_c3xxx.bin qat_c3xxx_a0.bin qat_c3xxx_mmp.bin qat_c62x.bin qat_c62x_a0.bin qat_c62x_mmp.bin qat_mmp.bin qat_d15xx.bin qat_d15xx_mmp.bin"
        for bin in ${BIN_LIST}; do
            if [[ -e "/lib/firmware/${bin}" ]]; then
                sudo rm -f "/lib/firmware/${bin}"
            fi

            if [[ -e "/lib/firmware/qat_fw_gwhw_backup/${bin}" ]]; then
                sudo mv "/lib/firmware/qat_fw_gwhw_backup/${bin}" "/lib/firmware/"
            fi

            if [[ -d "/lib/firmware/qat_fw_gwhw_backup" ]]; then
                sudo rm -rf "/lib/firmware/qat_fw_gwhw_backup/"
            fi
        done
    fi

    #echo "del usdm_drv"
    if [[ `lsmod | grep "usdm_drv" | wc -l` != "0" ]]; then
        rmmod usdm_drv
    fi

    #echo "del qat_service"
    if [[ -e "/etc/init.d/qat_service" ]]; then
        /etc/init.d/qat_service shutdown
        if [[ -e /sbin/chkconfig ]]; then
            chkconfig --del qat_service
        fi

        sudo rm -f /etc/init.d/qat_service
    fi

    #echo "del conf backup"
    if [[ -d /etc/qat_conf_gwhw_backup ]]; then
        sudo rm -f /etc/dh895xcc_dev0.conf
        mv /etc/qat_conf_gwhw_backup/dh895xcc* /etc/
        if [[ "`ls -A /etc/qat_conf_gwhw_backup`" = "" ]]; then
            sudo rm -rf /etc/qat_conf_gwhw_backup
        fi
    fi
    sudo rm -f /lib64/libqat_s.so
    sudo rm -f /lib64/libusdm_drv_s.so
    sudo rm -f "/lib/modules/`uname -r`/kernel/drivers/usdm_drv.ko"
    sudo rm -f /usr/bin/adf_ctl
    sudo rm -rf "/lib/modules/`uname -r`/updates/drivers"

    sudo depmod -a
    modprobe intel_qat
}

#删除守护进程
function del_watch_process()
{
    if [[ -d "$WATCH_PROCESS_PATH" ]];then
        sudo rm -rf "$WATCH_PROCESS_PATH"
    fi
}

#删除gw_parser进程
function del_gw_parser()
{
    if [[ -d  "$GW_PARSER_PATH" ]];then
        sudo rm -rf "$GW_PARSER_PATH"
    fi

    if [[ "`ls -A /opt/apigw/`" = "" ]];then
        rm -rf /opt/apigw/
    fi

    if [[ -f "/usr/lib/systemd/system/gwhw.service" ]];then
        sudo rm -rf /usr/lib/systemd/system/gwhw.service
    fi

    if [[ -f "/usr/lib/systemd/system/gw_operation.service" ]]; then
        sudo rm -rf /usr/lib/systemd/system/gw_operation.service
    fi

    if [[ `grep init_dpdk.sh /etc/rc.local | wc -l` -gt 0 ]];then

        sed --follow-symlinks -i '/sudo sh \/opt\/apigw\/gwhw\/tools\/init_dpdk.sh/d' /etc/rc.local
    fi

    if [[ `grep gwhw.service /etc/rc.local | wc -l` -gt 0 ]];then
        sed --follow-symlinks -i '/sudo systemctl start gwhw.service/d' /etc/rc.local
    fi

    if [[ `grep 'load_driver.sh' /etc/rc.local | grep gwhw | wc -l` -ne 0 ]];then
		sed --follow-symlinks -i '/sudo sh \/opt\/apigw\/gwhw\/tools\/load_driver.sh/d' /etc/rc.local
	fi

    if [[ `grep 'agent_upload_quota.sh' /etc/rc.local | grep gwhw | wc -l` -ne 0 ]];then
        sed --follow-symlinks -i '/sudo sh \/opt\/apigw\/gwhw\/tools\/agent_upload_quota.sh/d' /etc/rc.local
    fi

    if [[ `grep 'renice_sshd.sh' /etc/rc.local | grep gwhw | wc -l` -ne 0 ]];then
		sed --follow-symlinks -i '/sudo sh \/opt\/apigw\/gwhw\/tools\/renice_sshd.sh/d' /etc/rc.local
	fi

}

function main()
{
    check_gwhw
    del_conf_system
    del_watch_process
    del_gw_parser
}


main
