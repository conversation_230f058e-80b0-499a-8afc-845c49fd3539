#!/bin/bash
source ./inst_function.sh

# 检查运行环境
function check_env()
{
	check_base_env

	if [[ -d ${DEST_PATH} ]]; then
		rm -rf ${DEST_PATH}
	fi

	return
}

# 配置 系统环境
function conf_system()
{
	# /etc/security/limits.conf 文件
	# * - nofile 4096
	# * - nofile 65536
	if [[ `grep '^[^#].*nofile' /etc/security/limits.conf | wc -l` -eq 0 ]]; then
		echo '* - nofile 65536' >> /etc/security/limits.conf >/dev/null 2>&1
	fi

	# /etc/selinux/config 文件
	if [[ -f /etc/selinux/config ]]; then
		 sed -i -e 's/^SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config
	fi
	# 关闭 selinux
	 setenforce 0 >/dev/null 2>&1

	# /etc/sysctl.conf 文件
	if [[ -f /etc/sysctl.conf ]]; then
		if [[ `grep kernel.nmi_watchdog /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			echo 'kernel.nmi_watchdog=0' >> /etc/sysctl.conf >/dev/null 2>&1
		fi
		if [[ `grep kernel.hung_task_timeout_secs /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			echo 'kernel.hung_task_timeout_secs=0' >> /etc/sysctl.conf >/dev/null 2>&1
		fi

		if grep -q '^kernel.core_pattern' /etc/sysctl.conf; then
		    # 存在则替换
			sed -i 's|^kernel\.core_pattern=.*|kernel.core_pattern=core.%p|' /etc/sysctl.conf
		else
		    # 不存在则追加
			echo 'kernel.core_pattern=core.%p' >> /etc/sysctl.conf
		fi

		# if grep -q '^kernel.sched_rt_runtime_us' /etc/sysctl.conf; then
		# 	# 存在则替换
		# 	sed -i 's|^kernel\.sched_rt_runtime_us=.*|kernel.sched_rt_runtime_us=-1|' /etc/sysctl.conf
		# else
		# 	# 不存在则追加
		# 	echo 'kernel.sched_rt_runtime_us=-1' >> /etc/sysctl.conf
		# fi
		sysctl -w vm.nr_hugepages=2048 # 系统默认分配到node0，多次执行，大页数值不变不会导致大页重新分配
		sysctl -p
		# 分配在非node0时使用该大页内存，不用再需要执行sysctl -w vm.nr_hugepages=
		# echo 2048 > /sys/devices/system/node/node1/hugepages/hugepages-2048kB/nr_hugepages
	fi

	grep -q '^ulimit -c' ~/.bashrc && sed -i 's|^ulimit -c.*|ulimit -c unlimited|' ~/.bashrc || echo 'ulimit -c unlimited' >> ~/.bashrc
	bash -c "source ~/.bashrc"
	# 关闭firewalld或iptables并移除启动项
	command -v systemctl &>/dev/null && systemctl stop firewalld >/dev/null 2>&1 && systemctl disable firewalld >/dev/null 2>&1 || (service iptables stop >/dev/null 2>&1 && chkconfig iptables off >/dev/null 2>&1)

	# # 调整开机脚本权限
	# if [[ ! -x /etc/rc.d/rc.local ]]; then
	# 	 chmod +x /etc/rc.d/rc.local
	# fi

	# 禁用中断分配服务
	 systemctl stop irqbalance >/dev/null 2>&1;
	 systemctl disable irqbalance >/dev/null 2>&1;
	# 禁用无关的服务
	 systemctl stop lvm2-monitor.service >/dev/null 2>&1
	 systemctl stop tuned.service >/dev/null 2>&1

	# 增加普通用户
	 useradd ${USERNAME} >/dev/null 2>&1

	# 增加license文件路径
	 mkdir -p "/opt/licutils/"

	# 增加日志目录
	 mkdir -p "/opt/apigw/logs/"
	 chown -R ${USERNAME}  "/opt/apigw/logs/"

	# 增加运行状态目录
	 mkdir -p "/opt/apigw/run/"
	 chown -R ${USERNAME}  "/opt/apigw/run/"

	# 增加配置目录
     mkdir -p "/opt/data/apigw/gwhw/"
	 chown -R ${USERNAME}  "/opt/data/apigw/gwhw/"

	# if [ $processor -lt 4 ]
	# then
	# 	cpuLimit=100000
	# else
	# 	cpuLimit=$[processor * 100000 / 4]
	# fi

	# if [ $mem -lt ********** ]
	# then
	# 	memLimit=$[1024 * 1024 * 1024]
	# else
	# 	memLimit=$[mem / 4]
	# fi

	# 配置cgroup
	# if [ $(grep "group gwhw" /etc/cgconfig.conf | wc -l) -eq 1 ]
	# then
	# 	lineNumStart=$(cat -n /etc/cgconfig.conf | grep "group gwhw" | awk '{print $1}')
	# 	lineNumEnd=$[lineNumStart + 8]
	# 	sed -i "$lineNumStart, $lineNumEnd d" /etc/cgconfig.conf
	# fi

	# echo "group gwhw{" >> /etc/cgconfig.conf
	# echo -e "\tcpu{" >> /etc/cgconfig.conf
	# echo -e "\t\tcpu.cfs_quota_us = 1600000;" >> /etc/cgconfig.conf
	# echo -e "\t\tcpu.cfs_period_us = 100000;" >> /etc/cgconfig.conf
	# echo -e "\t}" >> /etc/cgconfig.conf
	# echo -e "\tmemory{" >> /etc/cgconfig.conf
	# echo -e "\t\tmemory.limit_in_bytes = 12884901888;" >> /etc/cgconfig.conf
	# echo -e "\t}" >> /etc/cgconfig.conf
	# echo "}" >> /etc/cgconfig.conf

	# if [ $(grep "/opt/apigw/gwhw/gw_parser" /etc/cgrules.conf | wc -l) -eq 0 ]
	# then
	# 	echo "*:/opt/apigw/gwhw/gw_parser cpu,memory gwhw" >> /etc/cgrules.conf
	# fi

	 # systemctl restart cgconfig > /dev/null 2>&1
	 # systemctl restart cgred > /dev/null 2>&1
	 # systemctl enable cgconfig > /dev/null 2>&1
	 # systemctl enable cgred > /dev/null 2>&1

	# 关闭rpcbind服务
	systemctl stop rpcbind.socket > /dev/null 2>&1
	systemctl stop rpcbind > /dev/null 2>&1
	systemctl disable rpcbind.socket > /dev/null 2>&1
	systemctl disable rpcbind > /dev/null 2>&1

	return
}

# 安装主程序
function inst_gw_parser()
{
	# 增加配置目录
	 mkdir -p "${DEST_PATH}./"
	 mkdir -p "${DEST_PATH}./logs/"
	 mkdir -p "${DEST_PATH}./lib/"
	 mkdir -p "${DEST_PATH}./kmod/"
	 mkdir -p "${DEST_PATH}./kmod/xinchuang/"
	 mkdir -p "${DEST_PATH}./tools/"
	 mkdir -p "${DEST_PATH}./stats_srv"
	 mkdir -p "${DEST_PATH}./stats_srv/tmpdir/"
     cp -f version.txt "${DEST_PATH}./"
	 cp -f gw_parser "${DEST_PATH}./"
     cp magic.mgc "${ETC_PATH}./"

	inst_plugin_config

	 chmod +x "${DEST_PATH}./"gw_parser
	 cp -f liblicutils.so "${DEST_PATH}./lib/"
	 cp -f libfile_type.so "${DEST_PATH}./lib/"
	 cp -f libnacos-cli.so "${DEST_PATH}./lib/"
	 cp -f libcurl.so "${DEST_PATH}./lib/"
	 cp -f librdkafka.so "${DEST_PATH}./lib/"
	 cp -f libaws-cpp-sdk*.so "${DEST_PATH}./lib/"
	 cp -f libbrotli.so "${DEST_PATH}./lib/"

	# kernel_version=$(uname -r)
	# if [ $(ls pfring_ko | grep "$kernel_version" | wc -l) -eq 0 ]; then
	# 	echo "Warning: 没有${kernel_version}内核对应的pfring驱动！！！"

	# else
	# 	cp -f pfring_ko/$kernel_version/pf_ring.ko "${DEST_PATH}./kmod/"
	# fi

	 cp -f *.sh "${DEST_PATH}./tools/"
	 cp -f *.py "${DEST_PATH}./tools/"
	 cp -f dpdk-devbind "${DEST_PATH}./tools/"
	 cp -f set_irq_affinity "${DEST_PATH}./tools/"
	 cp -f dpdk_alloc_GB "${DEST_PATH}./tools/"
	 cp -f licutils_demo "${DEST_PATH}./tools/"
	 cp -f tcpreplay "${DEST_PATH}./tools/"
	chmod +x "${DEST_PATH}./tools/tcpreplay"
	cp -f ethtool "${DEST_PATH}./tools/"
	chmod +x "${DEST_PATH}./tools/ethtool"
	cp -f lspci "${DEST_PATH}./tools/"
	cp -f tcpdump "${DEST_PATH}./tools/"
	cp -f nload "${DEST_PATH}./tools/"

	if [ $(command -v lspci | wc -l) -eq 0 ]; then
		cp -f lspci /usr/bin/
	fi

	if [ $(command -v tcpreplay | wc -l) -eq 0 ]; then
		cp -f tcpreplay /usr/bin/
		chmod +x /usr/bin/tcpreplay
	fi

	if [ $(command -v ethtool | wc -l) -eq 0 ]; then
		cp -f ethtool /usr/bin/
		chmod +x /usr/bin/ethtool
	fi

	if [ $(command -v tcpdump | wc -l) -eq 0 ]; then
		cp -f tcpdump /usr/bin/
	fi

	if [ $(command -v nload | wc -l) -eq 0 ]; then
		cp -f nload /usr/bin/
	fi

	if [[ -f dpdk_black_list.txt && ! -f /opt/data/apigw/gwhw/dpdk_black_list.txt ]]; then
		 cp -f dpdk_black_list.txt /opt/data/apigw/gwhw/
	fi

	if [[ -f dpdk_white_list.txt && ! -f /opt/data/apigw/gwhw/dpdk_white_list.txt ]]; then
		sudo cp -f dpdk_white_list.txt /opt/data/apigw/gwhw/
	fi

	if [[ ! -d "/opt/urlbase/" ]];then
		 mkdir -p "/opt/urlbase/"
		 cp -f url_filter_base.file /opt/urlbase/
	fi

	if [[ ! -f "/opt/urlbase/url_filter_base.file" ]];then
		 cp -f url_filter_base.file /opt/urlbase/
	fi

	# 提高ssh服务的优先级,增加ssh连接的响应速度
	# if [[ -f renice_sshd.sh ]]; then
	# 	sh renice_sshd.sh
	# fi

	# 安装时SO库需要进行符号连接
	# 删除当前所有库文件
	 rm -f "${DEST_PATH}./lib/"lib*.so.*
	 cp -rf lib/* "${DEST_PATH}./lib/"
	cd "${DEST_PATH}./lib/"
	ls lib*.so.* | awk -Fso. '{printf(" ln -sf %s %sso\n", $0, $1)}' | sh

	# 给protobuf创建动态链接
	ln -s libprotobuf.so.31.0.1 libprotobuf.so.31

	if [[ -f libgcrypt.so.20.2.5 ]];then
		ln -s libgcrypt.so.20.2.5 libgcrypt.so.20
	fi

	if [[ -f libgpg-error.so.0.27.0 ]];then
		ln -s libgpg-error.so.0.27.0 libgpg-error.so.0
	fi

	if [[ -f libpfring.so ]];then
    	ln -sf libpfring.so libpfring.so.8
    fi

	if [[ -f libcurl.so ]];then
       	ln -sf libcurl.so libcurl.so.4
    fi

   	if [[ -f librdkafka.so ]];then
           	ln -sf librdkafka.so librdkafka.so.1
    fi

	cd - >>/dev/null

	 cp -f supervisord "${DEST_PATH}./"
	 cp -f supervisord_hw.conf "${DEST_PATH}./"
	#  cp -f "stats/"*.sh  "${DEST_PATH}./stats_srv"
	#  cp -f "stats/"*.conf  "${DEST_PATH}./stats_srv"
	#  cp -f "stats/gw_stat_srv"  "${DEST_PATH}./stats_srv"
	 cp -f "stats/gw_stats_srv-exe"  "${DEST_PATH}./stats_srv"
	 cp -f "stats/"*.conf  "${DEST_PATH}./stats_srv"
	# 配置文件
	if [[ ! -f "${DEST_PATH}./stats_srv/gw_stats_srv.conf" ]]; then
		 cp -f "stats/gw_stats_srv.conf" "${DEST_PATH}./stats_srv/"
	fi

	# 安装openssl库
	if [[ -f openssl-bin-1.1.1w.zip ]]; then
		if [[ ! -d /opt/openssl/ ]]; then
			 mkdir /opt/openssl/
		fi
		 unzip -qo openssl-bin-1.1.1w.zip -d /opt/openssl/ >/dev/null 2>&1
	fi

	 chown -R ${USERNAME}  "${DEST_PATH}./"

	#配置vsftp配置文件
	#conf_vsftp

	# sh "${DEST_PATH}./tools/"agent_upload_quota.sh

	#配置systemctl
	inst_config_systemctl

	if [ "TOOL" == "$product" ]; then
	    /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser
	fi
}

function main()
{
	func_log  parse_parameter $@
	func_log  check_env
	func_log  conf_system
	func_log  inst_gw_parser
	func_log  start_crontab
	func_log  install_agent_server
	return
}

main $@
