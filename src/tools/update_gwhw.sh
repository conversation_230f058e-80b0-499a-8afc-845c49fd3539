#!/bin/bash

if [ -f /home/<USER>/1.txt ]; then
    rm -f /home/<USER>/1.txt
    # echo "need update gwhw" >> /home/<USER>
else
    lineNum=$(crontab -l | grep -n "sh /opt/apigw/gwhw/tools/update_gwhw.sh" | cut -d : -f 1)
    sed -i "${lineNum}d" /var/spool/cron/root
    # echo "cancel update gwhw" >> /home/<USER>
    exit 1
fi

gw_parser_config_file_path="/opt/data/apigw/gwhw/gw_parser.conf"

source_path_content=$(grep "source_path" $gw_parser_config_file_path | tail -1)
# echo "source_path_content=$source_path_content" >> /home/<USER>
load_files_content=$(grep "load_files" $gw_parser_config_file_path)
# echo "load_files_content=$load_files_content" >> /home/<USER>
nic_device_name_content=$(grep "nic_device_name" $gw_parser_config_file_path)
# echo "nic_device_name_content=$nic_device_name_content" >> /home/<USER>

cd /home/<USER>/
unzip -q update_gwhw.zip
cd inst/
sh inst.sh -t mirror -p api -v 3.1

# echo "install gwhw finish" >> /home/<USER>

# echo "source_path_content=$source_path_content" >> /home/<USER>
lineNum=$(grep -n "source_path" $gw_parser_config_file_path | tail -1 | cut -d : -f 1)
# echo "source_path line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${source_path_content}" $gw_parser_config_file_path

# echo "load_files_content=$load_files_content" >> /home/<USER>
lineNum=$(grep -n "load_files" $gw_parser_config_file_path | cut -d : -f 1)
# echo "load_files line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${load_files_content}" $gw_parser_config_file_path

# echo "nic_device_name_content=$nic_device_name_content" >> /home/<USER>
lineNum=$(grep -n "nic_device_name" $gw_parser_config_file_path | cut -d : -f 1)
# echo "nic_device_name line num=$lineNum" >> /home/<USER>
sed -i "${lineNum}c \\${nic_device_name_content}" $gw_parser_config_file_path

systemctl restart gwhw