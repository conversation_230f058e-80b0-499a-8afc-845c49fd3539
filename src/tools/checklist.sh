#!/bin/bash

inputNum=0
configFile="/opt/data/apigw/gwhw/gw_parser.conf"
errFile="/opt/apigw/gwhw/logs/hw.err"
logFile="/opt/apigw/gwhw/logs/hw.log"

function get_user_input()
{
    while [ 1 ]
    do
        read -p "请输入一个括号中的数字：" inputNum

        if [ -z "$inputNum" ]
        then
            continue
        fi

        isFind="false"
        for ((i = 1; i <= $1; i++))
        do
            if [ "$inputNum" == "$i" ]
            then
                isFind="true"
                break
            fi
        done

        if [ "false" == "$isFind" ]
        then
            echo "错误的输入"
            continue
        fi

        break
    done

    echo ""
}

# 安装配置模块开始

function bind_eth()
{
    bash /opt/apigw/gwhw/tools/bind_eth.sh
}

function toggle_mode()
{
    echo "（1）agent模式"
    echo "（2）镜像模式"
    echo "（3）混合模式"

    inputNum=""
    get_user_input 3

    sourcePath=""
    loadFiles=$(grep "load_files" $configFile | tail -1 | cut -d \" -f 4 | sed -e s/file_source.so//g -e s/::/:/g -e s/nic_source.so//g -e s/::/:/g)
    if [ "1" == "$inputNum" ]
    then
        sourcePath="\"source_path\": \"source/file_source/\","
        loadFiles="\"load_files\": \"file_source.so:$loadFiles\","
    fi

    if [ "2" == "$inputNum" ]
    then
        sourcePath="\"source_path\": \"source/nic_source/\","
        loadFiles="\"load_files\": \"nic_source.so:$loadFiles\","
    fi

    if [ "3" == "$inputNum" ]
    then
        sourcePath="\"source_path\": \"source/file_source/:source/nic_source/\","
        loadFiles="\"load_files\": \"file_source.so:nic_source.so:$loadFiles\","
    fi
    loadFiles=$(echo "$loadFiles" | sed s/::/:/g)

    lineNum=$(grep -n "source_path" $configFile | tail -1 | cut -d : -f 1)
    sed -i "${lineNum}d" $configFile
    sed -i "${lineNum}i $sourcePath" $configFile
    sed -i "${lineNum}s/^/    /g" $configFile

    lineNum=$(grep -n "load_files" $configFile | tail -1 | cut -d : -f 1)
    sed -i "${lineNum}d" $configFile
    sed -i "${lineNum}i ${loadFiles}" $configFile
    sed -i "${lineNum}s/^/    /g" $configFile
}

function vxlan()
{
    echo "（1）开启"
    echo "（2）关闭"

    inputNum=""
    get_user_input 2

    dataOffsetSetting=""
    if [ "1" == "$inputNum" ]
    then
        dataOffsetSetting="\"data_offset\":\"64\","
    fi

    if [ "2" == "$inputNum" ]
    then
        dataOffsetSetting="\"data_offset\":\"0\","
    fi

    lineNum=$(grep -n "data_offset" $configFile | tail -1 | cut -d : -f 1)
    sed -i "${lineNum}d" $configFile
    sed -i "${lineNum}i $dataOffsetSetting" $configFile
    sed -i "${lineNum}s/^/    /g" $configFile
}

function timestamp()
{
    echo "（1）pcap包时间"
    echo "（2）服务器时间"

    inputNum=""
    get_user_input 2

    pcapTimestamp=""
    if [ "1" == "$inputNum" ]
    then
        pcapTimestamp="\"pcap_timestamp\": \"1\","
    fi

    if [ "2" == "$inputNum" ]
    then
        pcapTimestamp="\"pcap_timestamp\": \"0\","
    fi

    lineNum=$(grep -n "pcap_timestamp" $configFile | tail -1 | cut -d : -f 1)
    sed -i "${lineNum}d" $configFile
    sed -i "${lineNum}i $pcapTimestamp" $configFile
    sed -i "${lineNum}s/^/    /g" $configFile
}

ftpPort=0

# 返回0正常，返回1异常
function check_ftp_port()
{
    let ftpPort+=0 &> /dev/null
    if [ $? -ne 0 ]
    then
        echo "输入为非整数值"
        return 1
    fi

    if [ $ftpPort -lt 1024 ] || [ $ftpPort -gt 65535 ]
    then
        echo "输入的整数不在1024-65535的范围"
        return 1
    fi

    return 0
}

function ftp_port_range()
{
    ftpPortMin=0
    ftpPortMax=0

    while [ 1 ]
    do
        while [ 1 ]
        do
            read -p "请输入端口最小值（1024-65535）：" ftpPort
            check_ftp_port
            if [ $? -eq 1 ]
            then
                continue
            fi

            break
        done
        ftpPortMin=$ftpPort

        echo ""
        while [ 1 ]
        do
            read -p "请输入端口最大值（1024-65535）：" ftpPort
            check_ftp_port
            if [ $? -eq 1 ]
            then
                continue
            fi

            break
        done
        ftpPortMax=$ftpPort

        if [ $ftpPortMin -gt $ftpPortMax ]
        then
            echo "ftp端口最小值大于ftp端口最大值"
            continue
        fi

        break
    done

	num=$(grep -n "pasv_min_port=" /etc/vsftpd/vsftpd.conf | cut -d : -f 1)
	if [ -n "$num" ]
	then
		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
	fi
	echo "pasv_min_port=$ftpPortMin" >> /etc/vsftpd/vsftpd.conf

	num=$(grep -n "pasv_max_port=" /etc/vsftpd/vsftpd.conf | cut -d : -f 1)
	if [ -n "$num" ]
	then
		sed -i "$num, $num d" /etc/vsftpd/vsftpd.conf
	fi
	echo "pasv_max_port=$ftpPortMax" >> /etc/vsftpd/vsftpd.conf

    systemctl restart vsftpd
}

function sepcial_config_modify()
{
    echo "（1）vxlan"
    echo "（2）时间戳"
    echo "（3）ftp端口范围"

    inputNum=""
    get_user_input 3

    if [ "1" == "$inputNum" ]
    then
        # vxlan

        vxlan
        echo "vxlan配置修改成功"
        return
    fi

    if [ "2" == "$inputNum" ]
    then
        # 时间戳

        timestamp
        echo "时间戳配置修改成功"
        return
    fi

    if [ "3" == "$inputNum" ]
    then
        # ftp端口范围

        ftp_port_range
        echo "ftp端口范围修改成功"
        return
    fi
}

function reload_pfring_ko()
{
    if [ $(lsmod | grep "pf_ring" | wc -l) -ne 0 ]
    then
        rmmod pf_ring
    fi

    /opt/apigw/gwhw/tools/load_driver.sh
}

function install_and_config()
{
    echo "（1）绑定网口"
    echo "（2）特殊配置修改"
    echo "（3）重新加载pf_ring驱动"

    inputNum=""
    get_user_input 3

    if [ "1" == "$inputNum" ]
    then
        # 绑定网口

        bind_eth
        return
    fi

    if [ "2" == "$inputNum" ]
    then
        # 特殊配置修改

        sepcial_config_modify
        systemctl restart gwhw
        return
    fi

    if [ "3" == "$inputNum" ]
    then
        # 重新加载pf_ring驱动

        systemctl stop gwhw
        reload_pfring_ko
        systemctl restart gwhw
        echo "重新加载pf_ring驱动成功"
        return
    fi
}

# 安装配置模块结束

# 状态检查模块开始

# 返回0正常，返回1异常
function running_status()
{
    if [ $(systemctl status gwhw | grep dead | wc -l) -eq 1 ]
    then
        echo "（1）网关未启动"
        return 1
    fi

    lineNum=$(grep -n "GwLicense init" $errFile | tail -1 | cut -d : -f 1)
    let lineNum+=1
    if [ $(sed -n "${lineNum}p" $errFile | grep "successfully" | wc -l) -eq 0 ]
    then
        echo "（2）网关授权失败"
        return 1
    fi

    lineNumTemp=$(grep -n "product expired" $errFile | tail -1 | cut -d : -f 1)
    if [ -n "$lineNumTemp" ] && [ $lineNumTemp -gt $lineNum ]
    then
        echo "（3）网关授权已过期"
        return 1
    fi

    kafkaDomain=$(grep "ApiEvents" $configFile | tail -1 | cut -d \" -f 6)
    kafkaPort=$(echo "$kafkaDomain" | cut -d : -f 2)
    kafkaDomain=$(echo "$kafkaDomain" | cut -d : -f 1)
    if [ $(grep "$kafkaDomain" /etc/hosts | wc -l) -eq 0 ]
    then
        echo "（4）kafka域名对应ip未配置"
        return 1
    fi

    if timeout 1 bash -c "echo > /dev/tcp/$kafkaDomain/$kafkaPort" 2>/dev/null; then
        echo "（6）kafka域名端口telnet通"
    else
        echo "（6）kafka域名端口telnet不通"
        return 1
    fi

    lineNumTemp=$(grep -n "Error while ListBuckets" $errFile | tail -1 | cut -d : -f 1)
    if [ -n "$lineNumTemp" ] && [ $lineNumTemp -gt $lineNum ]
    then
        echo "（7）minio连接出错"
        return 1
    fi

    if [ $(grep "source_path" $configFile | tail -1 | grep "nic_source" | wc -l) -eq 1 ]
    then
        # 网关打开网口失败后不会尝试去打开起他网口
        content=$(tac $errFile|grep -m1 "mtu(15854)")

        if echo "$content" | grep -wq "failed"; then
            err_msg=$(echo "$content" | awk -F ']|,' '{print $2}')
            echo "$err_msg"
            return 1
        fi


        if [ "true" == "$isEthOpenFail" ]
        then
            return 1
        fi
    fi

    isHaveError="false"
    content=$(grep -n \\[ERROR\\] $errFile | tail -10)
    lineCount=$(echo "$content" | wc -l)
    for ((i = 1; i <= $lineCount; i++))
    do
        if [ -z "$content" ]
        then
            break
        fi

        oneLine=$(echo "$content" | sed -n "${i}p")
        lineNumTemp=$(echo "$oneLine" | cut -d : -f 1)
        if [ $lineNumTemp -lt $lineNum ]
        then
            continue
        fi

        isHaveError="true"
        echo "（9）ERROR信息：${oneLine#*:}"
    done
    if [ $isHaveError == "true" ]
    then
        return 1
    fi

    return 0
}

totalBytes=0

function get_flow_info()
{
    totalBytes=0
    bindEth=$(grep "nic_device_name" $configFile |\
              cut -d : -f 2 |\
              cut -d \" -f 2 |\
              awk -F , '{for (i = 1; i <= NF; i++) {print $i}}')

    if [ -z "$bindEth" ]
    then
        echo "未绑定网口"
        exit
    fi

    for eth in $bindEth
    do
        ethInfo=$(ifconfig $eth)
        rxBytes=$(echo "$ethInfo" | grep "RX packets" | awk '{print $5}')
        txBytes=$(echo "$ethInfo" | grep "TX packets" | awk '{print $5}')
        let totalBytes=totalBytes+rxBytes+txBytes
    done
}

http_qps_current=0
ip_bytes_current=0
upload_qps_current=0

function get_parse_info()
{
    tryNum=0
    http_qps_current=0
    ip_bytes_current=0
    upload_qps_current=0

    while [ true ]
    do
        if [ $tryNum -eq 3 ]
        then
            echo "获取当前qps信息失败"
            exit
        fi

        sleep 1
        let tryNum+=1
        content=$(grep -n "stats qps" $logFile | tail -3)

        if [ $(echo "$content" | wc -l) -ne 3 ]
        then
            # echo "cant't get 3 line"
            # echo "==="
            continue
        fi

        lineNum1=$(echo "$content" | sed -n '1p' | cut -d : -f 1)
        lineNum2=$(echo "$content" | sed -n '2p' | cut -d : -f 1)
        lineNum3=$(echo "$content" | sed -n '3p' | cut -d : -f 1)
        # echo "lineNum1=$lineNum1"
        # echo "lineNum2=$lineNum2"
        # echo "lineNum3=$lineNum3"
        # echo "==="

        key1=$(echo "$content" | sed -n '1p' | awk '{print $2}')
        key2=$(echo "$content" | sed -n '2p' | awk '{print $2}')
        key3=$(echo "$content" | sed -n '3p' | awk '{print $2}')
        # echo "key1=$key1"
        # echo "key2=$key2"
        # echo "key3=$key3"
        # echo "==="

        http_qps_lineNum=0
        ip_bytes_lineNum=0
        upload_qps_lineNum=0

        if [ "$key2" == "qps" ]
        then
            http_qps_lineNum=$[lineNum2 + 1]
            ip_bytes_lineNum=$[lineNum2 + 2]
            upload_qps_lineNum=$[lineNum2 + 11]
        else
            http_qps_lineNum=$[lineNum1 + 1]
            ip_bytes_lineNum=$[lineNum1 + 2]
            upload_qps_lineNum=$[lineNum1 + 11]
        fi
        # echo "http_qps_lineNum=$http_qps_lineNum"
        # echo "ip_bytes_lineNum=$ip_bytes_lineNum"
        # echo "upload_qps_lineNum=$upload_qps_lineNum"
        # echo "==="

        http_qps_current=$(sed -n "${http_qps_lineNum}p" $logFile  | awk '{print $5}')
        ip_bytes_current=$(sed -n "${ip_bytes_lineNum}p" $logFile | awk '{print $4}')
        upload_qps_current=$(sed -n "${upload_qps_lineNum}p" $logFile | awk '{print $3}')
        if [ -z "$http_qps_current" ]
        then
            # echo "can't get http_qps_current"
            # echo "==="
            continue
        elif [ "nan" == "$http_qps_current" ]
        then
            http_qps_current=0
        fi

        if [ -z "$ip_bytes_current" ]
        then
            # echo "can't get ip_bytes_current"
            # echo "==="
            continue
        elif [ "nan" == "$ip_bytes_current" ]
        then
            ip_bytes_current=0
        fi

        if [ -z "$upload_qps_current" ]
        then
            # echo "can't get upload_qps_current"
            # echo "==="
            continue
        elif [ "nan" == "$upload_qps_current" ]
        then
            upload_qps_current=0
        fi
        # echo "http_qps_current=$http_qps_current"
        # echo "ip_bytes_current=$ip_bytes_current"
        # echo "upload_qps_current=$upload_qps_current"
        # echo "==="

        break
    done
}

# 返回0正常，返回1异常
function parse_status()
{
    isCheckEthFlow="false"
    if [ $(grep "source_path" $configFile | tail -1 | grep "nic_source" | wc -l) -eq 1 ]
    then
        isCheckEthFlow="true"
    fi

    totalBytesBefore=0
    if [ "true" == "$isCheckEthFlow" ]
    then
        get_flow_info
        totalBytesBefore=$totalBytes
    fi

    get_parse_info
    http_qps_before=$http_qps_current
    ip_bytes_before=$ip_bytes_current
    upload_qps_before=$upload_qps_current

    for ((i = 0; i < 5; i++))
    do
        sleep 1
    done

    totalBytesAfter=0
    totalBytesDelta=0
    if [ "true" == "$isCheckEthFlow" ]
    then
        get_flow_info
        totalBytesAfter=$totalBytes

        let totalBytesDelta=totalBytesAfter-totalBytesBefore
        if [ $totalBytesDelta -eq 0 ]
        then
            echo "（10）绑定的网口没有流量"
            return 1
        fi
    fi

    get_parse_info
    http_qps_after=$http_qps_current
    ip_bytes_after=$ip_bytes_current
    upload_qps_after=$upload_qps_current

    if [ "true" == "$isCheckEthFlow" ]
    then
        if [ $(echo "$ip_bytes_after * 5 * 10 < $totalBytesDelta" | bc) -eq 1 ]
        then
            echo "（11）可能存在网口流量被大量丢弃"
        fi

        if [ $(echo "($totalBytesDelta / 5) * 8 < 1048576" | bc) -eq 1 ]
        then
            echo "（12）绑定的网口流量小于1Mb/s，请确认该情况是否正常"
        fi
    fi

    if [ $(echo "0 == $http_qps_before" | bc) -eq 1 ] && [ $(echo "0 == $http_qps_after" | bc) -eq 1 ]
    then
        echo "（13）没有http流量"
        return 1
    fi

    if [ $(echo "0 == $upload_qps_before" | bc) -eq 1 ] && [ $(echo "0 == $upload_qps_after" | bc) -eq 1 ]
    then
        echo "（14）没有数据上传到kafka"
        return 1
    fi

    return 0
}

function status_check()
{
    running_status && parse_status && echo "网关运行正常"
}

# 状态检查模块结束

# 监控信息模块开始

function get_current_qps()
{
    get_parse_info
    echo "当前qps为$upload_qps_current"
}

function dpdk_bind_status()
{
    /opt/apigw/gwhw/tools/dpdk-devbind -s
}

function monitor_info()
{
    echo "（1）网关当前qps"
    echo "（2）查看dpdk网口绑定情况"

    inputNum=""
    get_user_input 2

    if [ "1" == "$inputNum" ]
    then
        # 网关当前qps

        get_current_qps
        return
    fi

    if [ "2" == "$inputNum" ]
    then
        # 查看dpdk网口绑定情况

        dpdk_bind_status
        return
    fi
}

# 监控信息模块结束

function main()
{
    echo "（1）安装配置"
    echo "（2）状态检查"
    echo "（3）监控信息"

    inputNum=""
    get_user_input 3

    if [ "1" == "$inputNum" ]
    then
        # 安装配置

        install_and_config
        return
    fi

    if [ "2" == "$inputNum" ]
    then
        # 状态检查

        status_check
        return
    fi

    if [ "3" == "$inputNum" ]
    then
        # 监控信息

        monitor_info
        return
    fi
}

main
