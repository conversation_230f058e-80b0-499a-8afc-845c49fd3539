#! /bin/bash

gwhw_PROPATH=/opt/apigw/gwhw
LOG_PATH=/opt/apigw/gwhw/logs
SUPERVISORD_PATH=/opt/apigw/logs/
JSON_PASH=/opt/data/apigw/gwhw/gw_parser.conf #json 文件的目录
#在所有hw.err文件中顺序查找license相关的内容有的话判断是里面有没有error有的话输出证书有错误然后exit
#找到了输出1 找不到输出0
#判断下
Check_license(){
    #判断是否有hw.err文件
    LAST_HWERR_NUM=`ls -t $LOG_PATH |grep hw.err |wc -l`
    if [ $LAST_HWERR_NUM -eq 0 ];then
        echo ' there is no hw.err* in path $LOG_PATH  '
        return 0
    fi
    for i in $(seq 1 $LAST_HWERR_NUM )
    do
        LAST_HWERR=`ls -t $LOG_PATH  |grep hw.err |head -n $i`
        #判断是license个数是否大于0 大于零进入判断
        license_ERR_NUM=`grep  license  $LOG_PATH/$LAST_HWERR |wc -l`
        if [ $license_ERR_NUM -gt 0 ];then
            license_SEARCH=`grep  license  $LOG_PATH/$LAST_HWERR |tail -n 1`
            license_SEARCH_IFHAVERROR=` echo $license_SEARCH  0>/dev/null |grep ERROR |wc -l`
            if [ $license_SEARCH_IFHAVERROR -gt 0 ];then
                echo " ERROR:There is an error with your license "
                echo " $license_SEARCH "
                return 1
            else
                echo "Your license has no errors "
                return 0
            fi
        fi
        if [ $i -eq $LAST_HWERR_NUM ];then
            echo 'The license related log does not exist in hw.err'
            return 0
        fi
    done
}

#传递进program_path 然后进行判断有没有core
#如果有core 打印core的最后修改时间 superisod /log../log*
Check_core(){
    Check_path_return=`ls -t $gwhw_PROPATH |grep core |wc -l`
    if [ $Check_path_return -gt 0 ];then
        CORE_NAME=`ls -t $gwhw_PROPATH |grep core |head -n 1`
        MODIFICATION_TIME=`echo ""|stat $CORE_NAME 0>/dev/null| sed -n '7p'`
        echo "core 文件生成时间为 "$MODIFICATION_TIME
    else
        echo "There is no core* file in $gwhw_PROPATH "
    fi
    return $Check_path_return
}

#检查core对应的进程号和 ../logs/hw-supervisord.log中的最新的进程号是不是相同
#不同没问题
#相同则有问题
Check_core2(){
    SUPER_NUM=`ls -t $SUPERVISORD_PATH |grep hw-supervisord.log | wc -l `
    if [ $SUPER_NUM -eq 0 ];then
        echo " There is no hw-supervisord.log in path $SUPERVISORD_PATH"
        return 0
    fi
    #加一步判断有没有core文件在路径中
    CORE_NUM=`ls -t  |grep core |wc -l`
    if [ $CORE_NUM -eq 0 ];then
        echo "There is no core* file in $gwhw_PROPATH"
        return 0
    fi
    #判断完毕
    SUPER_LIST=`ls -t $SUPERVISORD_PATH |grep hw-supervisord.log`
    for i in $(seq 1 $SUPER_NUM )
    do
        SUPERVISORD_FILE=`ls -t $SUPERVISORD_PATH |grep hw-supervisord.log |head -n $i |tail -n 1`
        SUPER_PID_NUM=`grep "'gw_parser' with pid" "$SUPERVISORD_PATH/$SUPERVISORD_FILE" | wc -l`
        if [ $SUPER_PID_NUM -gt 0 ];then
            SUPER_PID=`grep "'gw_parser' with pid" "$SUPERVISORD_PATH/$SUPERVISORD_FILE" | tail -n 1| awk '{print $8 }'`
            echo $SUPER_PID
            break
        fi
    done
    CORE_PID=`ls -t $gwhw_PROPATH |grep core |head -n 1| awk -F "." '{print $2 }'`
    echo $CORE_PID
    if [ $CORE_PID -eq $SUPER_PID ];then
        echo " ERROR: Please check the core file "
    else
        echo " There is no problem with your core file "
    fi
}

#传递进去域名 然后查看是否连通 ping 域名
#找到问题退出1 否则退出0
Check_domain_name(){
    Domain_name=$1
    TEL_PORT_INT=`echo ""|ping -c 5 $Domain_name 2>/dev/null|grep  "bytes"| wc -l`
    if [ $TEL_PORT_INT -gt 0 ];then
        TEL_PORT_INT=`expr $TEL_PORT_INT - 1`
    fi
    return $TEL_PORT_INT
}

#传两个参数看对端的端口是否打开 telnet 网址 端口号
Check_connect(){
    IP_HOST=$1
    PORT=$2
    TEL_PORT_INT=`echo ""|telnet $IP_HOST $PORT 0>/dev/null|grep "\^]"|wc -l`
    if [ $TEL_PORT_INT -eq 0  ];then
        echo " error :The opposite $IP_HOST service （port $PORT ） is not open "
        exit 1
    else
        echo " The opposite $IP_HOST service （port $PORT ） has been opened "
    fi
    return $TEL_PORT_INT
    #  echo $TEL_PORT_INT
}

#检查dns配置
#精确搜索 kafka-server.app-audit.svc.qzprod 看有几行 多行echo退出66666
#只有一行看前面有没有空格 搜索“ kafka-server.app-audit.svc.qzprod”看是不是1
#######按照空格分段看第二段是不是“kafka-server.app-audit.svc.qzprod”没做
#ping第一段看通不通不同则提示第一段地址不通
#通的话提示：
#检查ip是不是正确的
Check_DNSCONF(){
    DNSCONF_PATH=/etc/hosts
    HOST=kafka-server.app-audit.svc.qzprod
    DNSCONF_NUM=`cat $DNSCONF_PATH 0>/dev/null  | grep -Ev '^#' |grep  $HOST | wc -l`
    if [ $DNSCONF_NUM -eq 0 ];then
        echo "ERROE: There is no $HOST in the configuration file $DNSCONF_PATH "
        elif [ $DNSCONF_NUM -eq 1 ];then
        DNSCONF=`cat $DNSCONF_PATH 0>/dev/null  | grep -Ev '^#' | grep  $HOST `
        #这一行就是 DNSCONF
        DNSCONF_SPACES_NUM=`echo $DNSCONF 0>/dev/null |grep " $HOST" |wc -l`
        if [ $DNSCONF_SPACES_NUM -eq 1 ];then
            DNSCONF_IP=`echo $DNSCONF 0>/dev/null  | awk '{print $1 }'`
            Check_domain_name $DNSCONF_IP
            NUM=$?
            if [ $NUM -eq 0 ];then
                echo " ERROR：The network to  $DNSCONF_IP is blocked "
                return 1
            else
                echo " Please check if the address is correct "
                echo $DNSCONF
            fi
        else
            echo " ERROE：Domain name configuration error "
            return 1
        fi
    else
        echo " ERROE：There is too many $HOST in the configuration file $DNSCONF_PATH "
        return 1
    fi
}

#传入三个参数 阈值 关键字 文件名*
#判断为 文件名的*最新几个数据中是不是都是0
Check_values_all0(){
    SIGN0=0
    IP_BYTES_NUMBER0=$1
    KEY0=$2
    FILE_NAME0=$3
    #echo $IP_BYTES_NUMBER0 $KEY0 $FILE_NAME0
    #先判断下有没有log文件在/opt/apigw/gwhw/logs中有则继续没有退出
    NUMBER0=`ls -t $LOG_PATH |grep $FILE_NAME0| wc -l `
    if [ $NUMBER0 -le 0 ];then
        echo "There is no $FILE_NAME0 in $LOG_PATH0"
        exit 1
    fi
    #先获取最新的一个文件名
    LAST_LOG0=`ls -t $LOG_PATH  |grep $FILE_NAME0 | head -n 1`
    #在最后一个文件中key值存在的次数
    KEY_VALUE_NUMBER0=`grep "$KEY0" $LAST_LOG0 |wc -l`
    #最后一个文件中数值大于等于阈值
    #最后一个文件中不够阈值
    ##没有第二个文件
    ##有第二个文件
    
    if [ $KEY_VALUE_NUMBER0 -ge $IP_BYTES_NUMBER0 ];then
        for i in $(seq 1 $IP_BYTES_NUMBER0 )
        do
            A=`expr $i - 1`
            #cat hw.log|grep -w 'stats qps' -A10|grep 'http session qps'
            IP_BYTES_VALUE_STRING0[ $A ]=`cat $LOG_PATH/$LAST_LOG0 | grep -w 'stats qps' -A15| grep  "$KEY0" |tail -n $i  |head -n 1 |awk '{ print $(NF-2) }'`
            
            if [[ $(echo "${IP_BYTES_VALUE_STRING0[ $A ]} > 0 "|bc) -eq 1 ]]; then #判断是否大于0 如果大于0退出·
                SIGN0=1
            fi
        done
    else
        for i in $(seq 1 $KEY_VALUE_NUMBER0)
        #先在最新的log文件中查找然后再在上一个log文件中查找 但是需要判断下上一个log文件存不存在
        do
            A=`expr $i - 1`
            IP_BYTES_VALUE_STRING[ $A ]=`cat $LOG_PATH/$LAST_LOG0 | grep -w 'stats qps' -A15| grep  "$KEY0" |tail -n $i  |head -n 1 |awk '{ print $(NF-2) }'`
            
            if [[ $(echo "${IP_BYTES_VALUE_STRING0[ $A ]} > 0 "|bc) -eq 1 ]]; then #判断是否大于0 如果大于0退出·
                SIGN0=1
            fi
        done
        #判断下第二个文件存不存在  打印后退出 否则 查看倒数第二个文件剩下的key对应的值
        if [ $NUMBER0 -lt 2 ];then
            if [ $SIGN0 -eq 0 ]; then
                echo "Checked the values of $KEY_VALUE_NUMBER0 only $KEY0, all of which are 0 ()"
                return 1
            else
                echo "Checked the values of $KEY_VALUE_NUMBER0 only $KEY0, not all values are 0 ()"
                return 0
            fi
        fi
        #获取第二个文件的名字
        LINSHI0=`expr $IP_BYTES_NUMBER0 - $KEY_VALUE_NUMBER0`
        LAST_LOG0=`ls -t  $LOG_PATH  |grep $FILE_NAME0 | head -n 2 | tail -n 1`
        for i in  $( seq 1 $LINSHI0)
        do
            IP_BYTES_VALUE_STRING[`expr $i - 1`]=`cat $LOG_PATH/$LAST_LOG0 | grep -w 'stats qps' -A15| grep  "$KEY0" |tail -n $i  |head -n 1 |awk '{ print $(NF-2) }'`
            if [[ $(echo "${IP_BYTES_VALUE_STRING0[`expr $i - 1`]} > 0 " |bc) -eq 1 ]]; then #判断是否大于0 如果大于0退出·
                SIGN0=1
            fi
        done
        
    fi
    
    return $SIGN0
}

#检查json配置文件是否有明显错误 校验json格式
#找到输出1 找不到输出0
Check_json(){
    JSON_WRONG=`cat $JSON_PASH |jq  2>&1 |grep "parse error" |wc -l`
    if [ $JSON_WRONG -eq 1 ];then
        echo " ERROR: Your $JSON_PASH  file format is incorrect"
        echo `cat $JSON_PASH |jq  2>&1 |grep "parse error"`
        return 1
    fi
    return 0
}

#错误1 进程不存在
Process_not_exist(){
    Check_json
    check_json_out=$?
    if [ $check_json_out -eq 1 ];then
        exit 1
    fi
    Check_license
    Check_core
    Check_core2
}
#错误2  交给别的服务的流量 0
Traffic_to_other_services_wrong(){
    echo " Traffic to other services is wrong"
}
#错误3 过来的流量有多少是http流量 0
HTTP_traffic_wrong(){
    echo "http流量接收为0 请找相关技术人员"
    exit 3
}
#错误4 upload_msg值是0
Upload_traffic_wrong(){
    #判断域名是否配置正常 如果ping不通在看是不是配置文件没有正确配置
    Check_DNSCONF #先检查下DNS看看有没有问题
    #   if [ $? -eq 1 ];then
    #   exit 1
    #   fi
    OPPOSITE_DOMAIN_NAME=kafka-server.app-audit.svc.qzprod
    Check_domain_name $OPPOSITE_DOMAIN_NAME
    if [ $? -eq 0 ];then
        echo "error :The opposite domain name $OPPOSITE_DOMAIN_NAME is incorrectly configured "
        #Check_DNSCONF
        exit 1
    fi
    OPPOSITE_DOMAIN_NAME=nacos-server
    Check_domain_name $OPPOSITE_DOMAIN_NAME
    if [ $? -eq 0 ];then
        echo "error :The opposite domain name $OPPOSITE_DOMAIN_NAME is incorrectly configured "
    fi
    OPPOSITE_DOMAIN_NAME=minio-server
    Check_domain_name $OPPOSITE_DOMAIN_NAME
    if [ $? -eq 0 ];then
        echo "error :The opposite domain name $OPPOSITE_DOMAIN_NAME is incorrectly configured "
    fi
    
    #判断对端端口号是否打开
    REC_ADDR=kafka-server.app-audit.svc.qzprod
    Check_connect $REC_ADDR 9093
    #前三个都没有问题 echo提示
    echo ' Upload_ MSG in hw.log value is 0, please contact relevant personnel for troubleshooting'
    exit 0
}

cd $gwhw_PROPATH
PROGRAM_NAME=gw_parser
IP_BYTES_NUMBER=2
HTTP_SESSION_QPS_NUBER=2
UP_LOAD_MSG_NUMBER=2
#1查看进程是否存在
PROGRAM_NUMBER=`ps -ef | grep -w $PROGRAM_NAME |grep -v grep |wc -l`
if [ $PROGRAM_NUMBER -le 0 ];then
    echo "$PROGRAM_NAME is not run "
    Process_not_exist
    exit 1
else
    echo "$PROGRAM_NAME is running..."
fi

cd $LOG_PATH

#2查看是否交给别的服务的流量为0
#策略为从最新的几个log文件中扫描 ip bytes数值，如连续三个都是0 则无流量
KEY="ip bytes"
FILE_NAME="hw.log"
Check_values_all0 "$IP_BYTES_NUMBER" "$KEY" "$FILE_NAME"
TARG2=$?
if [ $TARG2 -eq 0 ];then
    Traffic_to_other_services_wrong
    exit 1
else
    echo "流量转发正常"
fi
#3过来的流量有多少是http是不是0
KEY='http session qps'
Check_values_all0 $HTTP_SESSION_QPS_NUBER "$KEY" $FILE_NAME
TARG3=$?
if [ $TARG3 -eq 0 ];then
    HTTP_traffic_wrong
    exit 1
else
    echo "接收http流量正常"
fi
#4上传的流量是0
KEY='upload_msg'
Check_values_all0 $UP_LOAD_MSG_NUMBER "$KEY" $FILE_NAME
TARG4=$?
if [ $TARG4 -eq 0 ];then
    Upload_traffic_wrong
    exit 1
else
    echo "流量上传正常 请找相关技术人员排查"
fi