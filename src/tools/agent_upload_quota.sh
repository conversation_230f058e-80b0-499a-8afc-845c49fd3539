#! /bin/sh
###
 # @Author: youweizhi
 # @LastEditors: youweizhi
### 

QUOTA_FILE=/home/<USER>

if [ ! -f /etc/vsftpd/vsftpd.conf ];then
   echo "vsftp conf file not exist"
   exit 0
else
  if [ `grep 'local_root' /etc/vsftpd/vsftpd.conf | wc -l` -eq 0 ];then 
 	  echo "vsftpd conf no local_root item"
	  exit 0
   fi

   local_root=`grep 'local_root' /etc/vsftpd/vsftpd.conf | awk -F '=' '{print $2}'`
fi

if [[ ! -d "$local_root" ]]; then 
  mkdir -p $local_root
fi

if [[ `mount | grep ${local_root} | wc -l` -gt 0 ]];then
  exit 0
fi

if [ $# == 1 ];then
   QUOTA_SIZE=$1
else
   QUOTA_SIZE=10000
fi

if [ -f ${QUOTA_FILE} ];then
   quota_file_size=`expr $(stat -c %s ${QUOTA_FILE}) / 1024 / 1024` 
   umount ${local_root} 2>/dev/null
   losetup -d /dev/loop2 2>/dev/null
   if [[ $quota_file_size -ne $QUOTA_SIZE ]];then
	 dd if=/dev/zero of=${QUOTA_FILE} bs=1M count=${QUOTA_SIZE} 1>/dev/null 2>/dev/null
   fi
else
   dd if=/dev/zero of=${QUOTA_FILE} bs=1M count=${QUOTA_SIZE} 1>/dev/null 2>/dev/null
fi
   losetup /dev/loop2 ${QUOTA_FILE}
   mkfs.xfs -f /dev/loop2 1>/dev/null
   mount -o grpquota,quota -o loop ${QUOTA_FILE} ${local_root}
   xfs_quota -x -c 'limit bsoft=8G bhard=9G isoft=1900 ihard=2000 ftp' ${local_root}
   chmod 777 ${local_root}
