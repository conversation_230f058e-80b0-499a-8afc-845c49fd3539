#!/bin/bash

errFile="/opt/apigw/gwhw/logs/hw.err"
configFile="/opt/data/apigw/gwhw/gw_parser.conf"

if [ $(systemctl status gwhw | grep dead | grep -v grep | wc -l) -eq 1 ]
then
    echo "网关服务未启动"
    exit 1
fi

if [ $(/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl status | grep gw_parser | grep -E "Running|Starting" | wc -l) -eq 0 ]
then
    echo "网关未启动"
    exit 1
fi

lineNum=$(grep -n "GwLicense init" $errFile | tail -1 | cut -d : -f 1)
let lineNum+=1
if [ $(sed -n "${lineNum}p" $errFile | grep "successfully" | wc -l) -eq 0 ]
then
    echo "网关授权失败"
    exit 1
fi

lineNumTemp=$(grep -n "product expired" $errFile | tail -1 | cut -d : -f 1)
if [ -n "$lineNumTemp" ] && [ $lineNumTemp -gt $lineNum ]
then
    echo "网关授权已过期"
    exit 1
fi

kafkaDomain=$(grep "ApiEvents" $configFile | tail -1 | cut -d \" -f 6)
kafkaPort=$(echo "$kafkaDomain" | cut -d : -f 2)
kafkaDomain=$(echo "$kafkaDomain" | cut -d : -f 1)
if [ $(grep "$kafkaDomain" /etc/hosts | wc -l) -eq 0 ]
then
    echo "kafka域名对应ip未配置"
    exit 1
fi

if timeout 1 bash -c "echo > /dev/tcp/$kafkaDomain/$kafkaPort" 2>/dev/null; then
    echo "kafka telnet success"
else
    echo "kafka telnet failed"
    return 1
fi

content=$(grep -n "Error while ListBuckets" $errFile | tail -1)
lineNumTemp=$(echo "$content" | cut -d : -f 1)
timestamp=$(echo "$content" | cut -d "[" -f 2 | cut -d "]" -f 1)
timestamp=$(date -d "$timestamp" +%s)
let timestamp+=100
curr=$(date "+%s")
if [ -n "$lineNumTemp" ] && [ $lineNumTemp -gt $lineNum ] && [ $timestamp -gt $curr ]
then
    echo "minio连接出错"
    exit 1
fi

if [ $(grep "source_path" $configFile | tail -1 | grep "nic_source" | wc -l) -eq 1 ]
then
    content=$(grep -n "open device.* failed" $errFile | tail -4)
    lineCount=$(echo "$content" | wc -l)

    isEthOpenFail="false"
    for ((i = 1; i <= $lineCount; i++))
    do
        if [ -z "$content" ]
        then
            break
        fi

        oneLine=$(echo "$content" | sed -n "${i}p")
        lineNumTemp=$(echo "$oneLine" | cut -d : -f 1)
        if [ $lineNumTemp -lt $lineNum ]
        then
            continue
        fi

        isEthOpenFail="true"
        ethName=$(echo "$oneLine" | cut -d : -f 2 | cut -d \( -f 2 | cut -d \) -f 1)
        echo "网口$ethName打开失败"
    done

    if [ "true" == "$isEthOpenFail" ]
    then
        exit 1
    fi
fi

exit 0
