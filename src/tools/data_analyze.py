#!/usr/bin/python
#coding=utf-8


import optparse
import os
import json
import socket,struct,codecs
try:
    import scapy.all as scapy
except ImportError:
    import scapy



DATA_FILE = '/opt/sample/sample.pcap'
DATA_DIR = '/opt/sample/'
PARAM_FILE = "/opt/analyze/pkt_param.json"
ANALYZE_FILE = "/opt/analyze/pkt_analyze.txt"


sip = ''
dip = ''
dport = 0

pkt_time = 0


sessions = {}

# 根据四元组，判断数据包是否进行记录
def check_tcp(pkt_sip,pkt_dip,pkt_sport,pkt_dport):
    # print(pkt_sip,pkt_dip,pkt_sport,pkt_dport)
    if dip == pkt_sip:
        if dport > 0 and dport != pkt_sport:
            return False
        if len(sip)>1 and sip!= pkt_dip:
            return False
        return True
    elif dip == pkt_dip:
        if dport > 0 and dport != pkt_dport:
            return False
        if len(sip)>1 and sip != pkt_sip:
            return False
        return True
    else:
        return False

# 根据源、目的IP，判断数据包是否进行记录
def check_ip(pkt_sip,pkt_dip):
    if dip == pkt_sip:
        if len(sip)>0 and sip!= pkt_dip:
            return False
        return True
    elif dip == pkt_dip:
        if len(sip)>0 and sip != pkt_sip:
            return False
        return True
    else:
        return False

# 记录TCP包的摘要信息
def log_tcp(pkt,f,index):
    global pkt_time,s_seq,s_ack,d_seq,d_ack
    cur_pt = pkt.time
    if pkt_time == 0:
        pkt_time = cur_pt
    
    cur_pt = cur_pt - pkt_time

    cur_seq = 0
    cur_ack = 0
    if pkt["IP"].dst == dip :
        sport = pkt["TCP"].sport
        dport = pkt["TCP"].dport
        if not sessions.has_key((sport,dport)):
            s_seq = pkt["TCP"].seq-1
            s_ack = pkt["TCP"].ack-1
            if pkt["TCP"].flags ==2:
                s_seq +=1
                s_ack +=1
            d_ack = s_seq
            d_seq = s_ack
            sessions[(sport,dport)]=(s_seq,s_ack,d_seq,d_ack)
        cur_seq = pkt["TCP"].seq - sessions[(sport,dport)][0]
        cur_ack = pkt["TCP"].ack - sessions[(sport,dport)][1]
    else:
        sport = pkt["TCP"].dport
        dport = pkt["TCP"].sport
        if not sessions.has_key((sport,dport)):
            d_seq = pkt["TCP"].seq-1
            d_ack = pkt["TCP"].ack-1
            s_seq = d_ack
            s_ack = d_seq
            sessions[(sport,dport)]=(s_seq,s_ack,d_seq,d_ack)
        if sessions[(sport,dport)][1] == 0:
            sessions[(sport,dport)]=(sessions[(sport,dport)][0],pkt["TCP"].seq,sessions[(sport,dport)][2],pkt["TCP"].seq)
        cur_seq = pkt["TCP"].seq - sessions[(sport,dport)][2]
        cur_ack = pkt["TCP"].ack - sessions[(sport,dport)][3]


    if cur_seq <0 :
        cur_seq += 2**32
    if cur_ack<0:
        cur_ack +=2**32
    data_len = 0
    info = ''
    if 'Raw' in pkt:
        info = str(pkt["Raw"].load)
        data_len = len(pkt["Raw"].load)
        idx = info.find('\n')
        if idx >80 or (idx<0 and data_len>80):
            idx = 80
        info = info[0:idx]
    else:
        data_len = 0
        flag = pkt["TCP"].flags
        if (flag & 0x1)>0:
            info += "[FIN]"
        if (flag & 0x2)>0:
            info += "[SYN]"
        if (flag & 0x4)>0:
            info += "[RST]"
        if (flag & 0x8)>0 :
            info += "[PSH]"
        if (flag & 0x10)>0:
            info += "[ACK]"
        if (flag & 0x20)>0:
            info +="[URG]"
    try:
        f.write("%8d %12.6f %16s %5d %16s %5d %12d %12d %8d %-s \r\n"%(index,cur_pt,pkt["IP"].src,pkt["TCP"].sport,pkt["IP"].dst,pkt["TCP"].dport,cur_seq,cur_ack,data_len,info))
    except:
        f.write("%8d %12.6f %16s %5d %16s %5d %12d %12d %8d %-s \r\n"%(index,cur_pt,pkt["IP"].src,pkt["TCP"].sport,pkt["IP"].dst,pkt["TCP"].dport,cur_seq,cur_ack,data_len,''))

# 记录IP包的摘要信息
def log_ip(pkt,f,index):
    global pkt_time
    cur_pt = pkt.time
    if pkt_time == 0:
        pkt_time = cur_pt
    
    cur_pt = cur_pt - pkt_time

    proto = str(pkt["IP"].proto)
    if pkt["IP"].proto == 1:
        proto = 'ICMP'
    elif pkt["IP"].proto == 2:
        proto = 'IGMP'
    elif pkt["IP"].proto == 6:
        proto = 'TCP'
    elif pkt["IP"].proto == 17:
        proto = 'UDP'
    elif pkt["IP"].proto == 47:
        proto = 'GRE'

    f.write("%8d %12.6f %16s %16s %7d %6s,%8s \r\n"%(index,cur_pt,pkt["IP"].src,pkt["IP"].dst,pkt["IP"].version,proto,pkt["IP"].len))

def main():
    global sip,dip,dport
    try:
        f = codecs.open(ANALYZE_FILE,'w','utf-8')
        f.write("%8s %12s %16s %5s %16s %5s %12s %12s %8s %-s \r\n"%("index","time","sip","sport","dip","dport","seq","ack","len","info"))


        if not os.path.isfile(PARAM_FILE):
            print("analyze param error: param not found \n")
            return
        try:
            with open(PARAM_FILE, 'r') as fjson:
                param_dict = json.loads(fjson.read())
            sip = param_dict["source_ip"].encode('utf-8')
            dip = param_dict["dest_ip"].encode('utf-8')
            dport = int(param_dict["dest_port"])
        except Exception:
            print("analyze param error: read param error\n")
        # print(sip,dip,dport)
        
        if (not dip) or (len(dip) == 0):
            print("analyze param error: dest ip not found\n")
            return
        
        count = 0
        pcap_files = os.listdir(DATA_DIR)
        for pcap_file in pcap_files:
            if len(pcap_file.split('.', 1)) > 1 and pcap_file.rsplit('.', 1)[1] == 'pcap':
                try:
                    pkts = scapy.rdpcap(DATA_DIR + pcap_file)
                    print pcap_file
                    for pkt in pkts:
                        if pkt.haslayer("TCP") and check_tcp(pkt["IP"].src,pkt["IP"].dst,pkt["TCP"].sport,pkt["TCP"].dport):
                            count+=1
                            log_tcp(pkt,f,count)
                except Exception, e:
                    print pcap_file, e
        f.close()
        #没有符合条件的TCP包
        if count ==0 :
            pcap_files = os.listdir(DATA_DIR)
            f = codecs.open(ANALYZE_FILE,'w','utf-8')
            for pcap_file in pcap_files:
                if len(pcap_file.split('.', 1)) > 1 and pcap_file.rsplit('.', 1)[1] == 'pcap':
                    try:
                        pkts = scapy.rdpcap(DATA_DIR + pcap_file)
                        f.write("%8s %12s %16s %16s %7s %6s %8s \r\n"%("index","time","sip","dip","version","proto","len"))
                        for pkt in pkts:
                            if pkt.haslayer("IP") and check_ip(pkt["IP"].src,pkt["IP"].dst):
                                count +=1
                                log_ip(pkt,f,count)
                    except Exception,e:
                        print pcap_file, e
            f.close()
        if count == 0:
            print("no packet to analyze\n")
            return
    except Exception,e:
        print(e)
        return

if __name__ == '__main__':
    main()
