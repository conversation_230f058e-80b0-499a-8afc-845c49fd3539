#!/bin/bash
 
# Housekeeping...
rm -f /tmp/ramdisk.img
rm -f /tmp/ramdisk.img.gz
rm -fr /mnt/initrd 
mkdir -p /mnt/initrd
 
# Ramdisk Constants
RDSIZE=4000
BLKSIZE=1024
 
# Create an empty ramdisk image
dd if=/dev/zero of=/tmp/ramdisk.img bs=$BLKSIZE count=$RDSIZE
#mkfs.xfs /tmp/ramdisk.img
mkfs.ext3 /tmp/ramdisk.img
 
# Make it an ext2 mountable file system
#/sbin/mke2fs -F -m 0 -b $BLKSIZE /tmp/ramdisk.img $RDSIZE
 
# Mount it so that we can populate
#mount /tmp/ramdisk.img /mnt/initrd -t ext2 -o loop=/dev/loop0
mount -o loop /tmp/ramdisk.img /mnt/initrd
 
# Populate the filesystem (subdirectories)
mkdir /mnt/initrd/bin
mkdir /mnt/initrd/sys
mkdir /mnt/initrd/dev
mkdir /mnt/initrd/proc
 
# Grab busybox and create the symbolic links
cp /root/busybox/busybox-1.26.2/_install/* /mnt/initrd -R
 
# Grab the necessary dev files
mknod /mnt/initrd/dev/console c 5 1
mknod /mnt/initrd/dev/ram0 b 1 0
cp -a /dev/null /mnt/initrd/dev
cp -a /dev/tty1 /mnt/initrd/dev
cp -a /dev/tty2 /mnt/initrd/dev
cp -R /dev/zero /mnt/initrd/dev
 
# Create the init file
cat >> /mnt/initrd/linuxrc << EOF
#!/bin/sh
echo
echo "Simple initrd is active"
echo
mount -t proc proc /proc
mount -t sysfs sysfs /sys
mkdir /hello
/bin/sh --login
EOF
 
chmod +x /mnt/initrd/linuxrc

#Create recovery_factory.sh
cat >> /mnt/initrd/recovery_factory.sh << EOF
#!/bin/sh
echo
echo "recovery factory"
mount -t proc proc /proc
mount -t sysfs sysfs /sys
echo /bin/mdev > /proc/sys/kernel/hotplug
mdev -s
mkdir /hello
/bin/sh --login
EOF
 
chmod +x /mnt/initrd/recovery_factory.sh

#Create recovery_program.sh
cat >> /mnt/initrd/recovery_program.sh << EOF
#!/bin/sh
echo
echo "recovery program"
EOF
 
chmod +x /mnt/initrd/recovery_program.sh

#Create recovery_defconfig.sh
cat >> /mnt/initrd/recovery_defconfig.sh << EOF
#!/bin/sh
echo
echo "recovery default config"
EOF
 
chmod +x /mnt/initrd/recovery_defconfig.sh

#find /mnt/initrd/ | cpio -c -o > ramdisk_cpio.img
#gzip ramdisk_cpio.img
 
# Finish up...
umount /mnt/initrd
cp /tmp/ramdisk.img ./
gzip -9 /tmp/ramdisk.img
cp /tmp/ramdisk.img.gz  ./
