#! /bin/sh

interval=1

function help
{
    echo "get GateWay system info"
    echo -e "\t" cpu_rate: get cpu rate
    echo -e "\t" mem_rate: get mem rate
    echo -e "\t" disk_rate: get disk rate
    echo -e "\t" net: get net card name
    echo -e "\t" net name: get net name info
}

function cpu_rate
{
    start=$(cat /proc/stat | grep "cpu " | awk '{print $2" "$3" "$4" "$5" "$6" "$7" "$8}')
    start_idle=$(echo ${start} | awk '{print $4}')
    start_total=$(echo ${start} | awk '{printf "%.f", $1+$2+$3+$4+$5+$6+$7}')

    sleep ${interval}

    end=$(cat /proc/stat | grep "cpu " | awk '{print $2" "$3" "$4" "$5" "$6" "$7" "$8}')
    end_idle=$(echo ${end} | awk '{print $4}')
    end_total=$(echo ${end} | awk '{printf "%.f", $1+$2+$3+$4+$5+$6+$7}')

    idle=`expr ${end_idle} - ${start_idle}`
    total=`expr ${end_total} - ${start_total}`
    idle_normal=`expr ${idle} \* 100`
    cpu_usage=`expr ${idle_normal} / ${total}`
    cpu_rate=`expr 100 - ${cpu_usage}`
    echo -e "${cpu_rate}%\c"
}

function mem_rate
{
    mem_total=`free | awk 'NR==2{print $2}'`
    mem_used=`free | awk 'NR==2{print $3}'`
    mem_rate=`expr ${mem_used} \* 100 / ${mem_total}`
    mem_rate=`awk 'BEGIN{printf "%0.f\n",('${mem_used}'/'${mem_total}')*100}'`
    echo -e "$mem_rate%\c"
}

function disk_rate
{
    fdisk_rate=`df -h | awk '"/"==$6 {print $5}'`
    echo -e "$fdisk_rate\c"
}

function net_list
{
    net_card=`ls /sys/class/net | grep -v lo | grep -v bond*`
    echo "${net_card}"
}

function get_transfer_speed
{
    eth=$1
    RXpre=$(cat /proc/net/dev | grep $eth | tr : " " | awk '{print $2}')
    TXpre=$(cat /proc/net/dev | grep $eth | tr : " " | awk '{print $10}')
    sleep ${interval}
    RXnext=$(cat /proc/net/dev | grep $eth | tr : " " | awk '{print $2}')
    TXnext=$(cat /proc/net/dev | grep $eth | tr : " " | awk '{print $10}')
    RX=`expr ${RXnext} - ${RXpre}`
    TX=`expr ${TXnext} - ${TXpre}`
    if [ $RX -lt 1024 ];then
        RX="${RX}B/s"
    elif [ $RX -gt 1048576 ];then
        RX=$(echo $RX | awk '{print $1/1048576 "MB/s"}')
    else
        RX=$(echo $RX | awk '{print $1/1024 "KB/s"}')
    fi

    if [ $TX -lt 1024 ];then
        TX="${TX}B/s"
    elif [ $TX -gt 1048576 ];then
        TX=$(echo $TX | awk '{print $1/1048576 "MB/s"}')
    else
        TX=$(echo $TX | awk '{print $1/1024 "KB/s"}')
     fi
    echo "$RX $TX"
}

function net_info
{
    net_name=$1
    net_info=""
    if [[ `ls /sys/class/net | grep -v lo | grep "$1$" | wc -l` -eq 1 ]];then
        net_info=${net_name}
        IP_ADDR=`ifconfig -a ${net_name} | grep inet | grep -v inet6 | awk '{print $2}'` #获取IP
        if [[ ${IP_ADDR} != "" ]];then
            net_info=${net_info}" "${IP_ADDR}
            if [[ `ifconfig -a ${net_name} | grep "flags" | grep "RUNNING" | wc -l` -eq 1 ]];then
                net_info=${net_info}" ""running"
                SPEED=`ethtool ${net_name} | grep 'Speed' | awk -F ': ' '{print $2}'`
                net_info=${net_info}" "${SPEED}
                transfer_speed=`get_transfer_speed $net_name`
                net_info=${net_info}" $transfer_speed"
                echo  -e "$net_info\c"
            else
                net_info=${net_info}" ""dead 0Mb/s 0Mb/s 0Mb/s"
                echo  -e "$net_info\c"
            fi

        else
            net_info=${net_info}" "
            if [[ `ifconfig -a ${net_name} | grep "flags" | grep "RUNNING" | wc -l` -eq 1 ]];then
                net_info=${net_info}" ""running"
                SPEED=`ethtool ${net_name} | grep 'Speed' | awk -F ': ' '{print $2}'`
                net_info=${net_info}" "${SPEED}
                transfer_speed=`get_transfer_speed $net_name`
                net_info=${net_info}" $transfer_speed"
                echo -e "$net_info\c"
            else
                net_info=${net_info}" ""dead 0Mb/s 0MB/s 0MB/s"
                echo -e "$net_info\c"
            fi
            #echo "$net_info"
        fi
    else
        echo -e "$net_info\c"

    fi
}

function net_info_ex()
{
    net_name=$1
    net_info=$net_name

    type=`ethtool ${net_name} | grep "Supported ports" | awk '{print $4}'`
    net_info=${net_info}" "${type}

    speed=`ethtool ${net_name} | tr '\n\t ' ' ' | tr -s ' ' | cut -d ':' -f 4 | sed 's,Supported.*,,' | sed 's,^ ,,' | sed 's,.$,,' | tr ' ' '\n' | tail -n 1 | awk -F "base" '{print $1}'`
    net_info=${net_info}" "${speed}

    if [[ `ifconfig -a ${net_name} | grep "flags" | grep "RUNNING" | wc -l` -eq 1 ]];then
        net_info=${net_info}" up"
    else
        net_info=${net_info}" down"
    fi

    ip_addr=`ifconfig $net_name | grep inet | grep -v inet6 | awk '{print $2}'`
    net_info=${net_info}" "${ip_addr}

    #mirror
    net_info=${net_info}" 0"

    bus_info=`ethtool -i ${net_name} | grep "bus-info" | awk '{print $2}'`
    net_info=${net_info}" "${bus_info}

    echo -e "${net_info}\c"
}

function mirror_bus_info()
{
    bus_info=$(python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep drv=igb_uio | awk '{print $1}' | tr '\n' ' ' | sed 's, $,,')
    echo -e "$bus_info\c"
}

function mirror_net_info()
{
    bus_info=$1
    net_info="mirror"

    #type
    if [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "SFP+" | wc -l` -gt 0 ]];then
        net_info=${net_info}" FIBRE"
    else
        net_info=${net_info}" TP"
    fi

    if [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep 'I350 Gigabit' | wc -l` -eq 1 ]]; then
        net_info=${net_info}" 1000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep '82580 Gigabit' | wc -l` -eq 1 ]];then
        net_info=${net_info}" 1000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "1GbE" | grep "Connection X722"  | wc -l` -eq 1  ]]; then
        net_info=${net_info}" 1000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "I210" | wc -l` -eq 1  ]]; then
        net_info=${net_info}" 1000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "10-Gigabit" | wc -l` -eq 1 ]];then
        net_info=${net_info}" 10000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "Ethernet 10G" | grep "X520 Adapter" | wc -l` -eq 1 ]];then
        net_info=${net_info}" 10000"
    elif [[ `python /opt/apigw/gwhw/tools/dpdk-devbind --status | grep ${bus_info} | grep "10GbE" | grep "Connection X722" | wc -l` -eq 1 ]];then
        net_info=${net_info}" 10000"
    else
        net_info=${net_info}" UNKNOWN"
    fi

    net_info=${net_info}" UNKNOWN"
    net_info=${net_info}" "
    net_info=${net_info}" 1"
    net_info=${net_info}" "${bus_info}
    echo -e "${net_info}\c"
}

#function net_info
#{
#
#}

#function main
#{
#    cpu=`cpu_rate`
#    echo "cpu rate : ${cpu}%"
#    mem_rate
#    disk_rate
#    net_info
#}
if [ $# == 0 ];then
    help
elif [ $1 == "help" ];then
    help
elif [ $1 == "cpu_rate" ];then
    cpu_rate
elif [ $1 == "mem_rate" ];then
    mem_rate
elif [ $1 == "disk_rate" ];then
    disk_rate
elif [ $1 == "net" ];then
    net_list
elif [ $1 == "net_info" ] ;then
    net_info_ex  $2
elif [ $1 == "bus_info" ]; then
    mirror_bus_info
elif [ $1 == "mirror_net_info" ];then
    mirror_net_info $2
else
    net_info $1
fi
