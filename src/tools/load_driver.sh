#!/bin/bash

DEST_PATH=/opt/apigw/gwhw/

/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser
rmmod pf_ring >/dev/null 2>&1
# We assume that you have compiled PF_RING
insmod ${DEST_PATH}./kmod/pf_ring.ko min_num_slots=786432 quick_mode=0

INTERFACES=$(cat /proc/net/dev|grep ':'|grep -v 'lo'|grep -v 'sit'|awk -F":" '{print $1}'|tr -d ' ')
for IF in $INTERFACES ; do
	TOCONFIG=$(ethtool -i $IF|grep -i "ixgbe"|wc -l)
        if [ "$TOCONFIG" -eq 1 ]; then
		printf "Configuring %s\n" "$IF"
		ifconfig $IF up
		sleep 1
		bash set_irq_affinity $IF

		ethtool -G $IF rx 32768

		ethtool -G $IF tx 32768

		ethtool -K $IF rxvlan off

		#ethtool -A $IF autoneg off
		ethtool -A $IF rx off
		ethtool -A $IF tx off
		ethtool -K $IF gro off
		ethtool -K $IF lro off > /dev/null 2>&1
	fi
	
	TOCONFIG=$(ethtool -i $IF | grep -i "igb" | wc -l)
	if [ "$TOCONFIG" -eq 1 ];then
		printf "Configuring %s\n" "$IF"
		ifconfig $IF up
		sleep 1
		
		ethtool -G $IF rx 4096

		ethtool -G $IF tx 4096
		ethtool -G $IF gro off > /dev/null 2>&1
		ethtool -G $IF lro off > /dev/null 2>&1
	fi

	TOCONFIG=$(ethtool -i $IF | grep -i "e1000e" | wc -l)
	if [ "$TOCONFIG" -eq 1 ];then
		printf "Configuring %s\n" "$IF"
		ifconfig $IF up

		# Disabling receive offloads
		ethtool -K $IF tso off
		ethtool -K $IF gso off
		ethtool -K $IF gro off
		sleep 1
	fi

	TOCONFIG=$(ethtool -i $IF | grep -i "i40e" | wc -l)
	if [ "$TOCONFIG" -eq 1 ];then
		printf "Configuring %s\n" "$IF"
		ifconfig $IF up

		ethtool -L $IF combined 1
		ethtool -G $IF rx 4096
		ethtool -G $IF tx 4096
		ethtool -K $IF sg off tso off gso off gro off > /dev/null 2>&1
		ethtool -K $IF rxvlan off
		sleep 1
	fi
done

# 如果去掉sleep，在程序中通过system调用load_driver.sh脚本有很大概率gw_parser起不来
# 这只是临时解决方案，还需查明原因，正式解决
sleep 10

/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser