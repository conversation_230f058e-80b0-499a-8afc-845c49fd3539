#!/bin/bash

ethNum=0
ethArray=[]
DPDK_WHITE_LIST_TXT="/opt/data/apigw/gwhw/dpdk_white_list.txt"

# 显示所有网口状态
function display_all_eth_status()
{
    ipCheckArray=[]
    mirrorCheckArray=[]
    rxFlowCheckArray=[]
    txFlowCheckArray=[]
    ethNames=$(cat /proc/net/dev | grep ":" | cut -d : -f 1 | sort -n)
    virtualEths=$(ls /sys/devices/virtual/net/)
    for ethName in $ethNames
    do
        # 排除掉虚拟网口、被bond的网口
        isFind="false"
        for virtualEth in $virtualEths
        do
            if [ "$ethName" == "$virtualEth" ]; then
                isFind="true"
                break
            fi
        done

        if [ "true" == "$isFind" ]; then
            continue
        fi

        ethConf="/etc/sysconfig/network-scripts/ifcfg-$ethName"
        if [ -e $ethConf ] && [ $(grep -E "SLAVE|MASTER" $ethConf | wc -l) -ge 2 ]; then
            continue
        fi

        # 按以下逻辑，给网口提供一些标识
        # if (网口没有ip)
        # {
        #     if (网口为RUNNING状态)
        #     {
        #         给出(mirror)标识

        #         if (网口tx或rx的5s差值大于0)
        #         {
        #             给出(flow)标识
        #         }
        #     }
        # }
        # else
        # {
        #     给出网口ip标识
        # }
        ip=""
        isMirror=""
        ifconfigInfo=$(ifconfig $ethName)

        if [ $(echo "$ifconfigInfo" | grep "inet " | wc -l) -eq 0 ]; then
            if [ $(echo "$ifconfigInfo" | grep "RUNNING" | wc -l) -eq 1 ]; then
                isMirror="(mirror)"
            fi
        else
            ip=$(echo "$ifconfigInfo" | grep "inet " | awk '{print $2}')
            ip="(${ip})"
        fi

        ipCheckArray[$ethNum]=$ip
        mirrorCheckArray[$ethNum]=$isMirror

        ethArray[$ethNum]=$ethName
        rxFlowCheckArray[$ethNum]=$(ifconfig $ethName | grep "RX packets" | awk '{print $5}')
        txFlowCheckArray[$ethNum]=$(ifconfig $ethName | grep "TX packets" | awk '{print $5}')
        let ethNum+=1
    done

    # 等待5s，用于检测网卡是否有流量
    echo -e "\nChecking network flow..."
    for ((i = 5; i > 0; i--))
    do
        echo "$i"
        sleep 1
    done

    # 展示所有可选的网卡信息
    echo ""
    date +"%Y-%m-%d %H:%M:%S"
    echo "All eth infomation:"
    for ((i = 0; i < ethNum; i++))
    do
        isHaveFlow=""
        ethName=${ethArray[$i]}

        if [ "(mirror)" == "${mirrorCheckArray[$i]}" ]; then
            rxCurrentFlow=$(ifconfig $ethName | grep "RX packets" | awk '{print $5}')
            txCurrentFlow=$(ifconfig $ethName | grep "TX packets" | awk '{print $5}')

            if [ $rxCurrentFlow -gt ${rxFlowCheckArray[$i]} ] || [ $txCurrentFlow -gt ${txFlowCheckArray[$i]} ]; then
                isHaveFlow="(flow)"
            fi
        fi

        index=$[i + 1]
        echo -e "\t($index)${ethName}${mirrorCheckArray[$i]}${isHaveFlow}${ipCheckArray[$i]}"
    done
}

function dpdk_umount()
{
    # 解绑dpdk
    bash /opt/apigw/gwhw/tools/dpdk_unbind.sh 1> /dev/null
    # 清除大页
    for node in $(ls /sys/devices/system/node/ | grep node[0-9])
    do
        echo 0 > /sys/devices/system/node/$node/hugepages/hugepages-2048kB/nr_hugepages
        echo 0 > /sys/devices/system/node/$node/hugepages/hugepages-1048576kB/nr_hugepages
    done
    # 卸载大页
    if [ $(mount | grep "/mnt/huge" | wc -l) -gt 0 ]; then
        umount /mnt/huge
        rm -rf /mnt/huge
    fi
}

function dpdk_bind()
{
    # 读取dpdk_white_list.txt文件，获取需要绑定的网卡列表
    bindEthArray=()
    if [ -f "$DPDK_WHITE_LIST_TXT" ]; then
        # 读取文件中非注释行，并过滤空行
        while IFS= read -r line || [ -n "$line" ]; do
            # 跳过注释行和空行
            if [[ ! "$line" =~ ^[[:space:]]*# && -n "${line// }" ]]; then
                # 去除行首行尾空白字符
                eth_name=$(echo "$line" | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')
                # 如果非空则添加到数组
                if [ -n "$eth_name" ]; then
                    bindEthArray+=("$eth_name")
                fi
            fi
        done < "$DPDK_WHITE_LIST_TXT"
    else
        echo "警告: 网卡白名单文件 $DPDK_WHITE_LIST_TXT 不存在!" >&2
    fi

    # 检查是否有网卡需要绑定
    if [ ${#bindEthArray[@]} -eq 0 ]; then
        sudo /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser
        return
    fi

    # 检查是否已挂载huge pages
    if [[ $(mount | grep "/mnt/huge" | wc -l) -eq 0 ]]; then
        # 未挂载huge pages,需要初始化大页内存
        bash /opt/apigw/gwhw/tools/init_dpdk.sh 1> /dev/null
    fi

    echo "以下网卡将被绑定到DPDK:" >&2
    for bindEth in "${bindEthArray[@]}"
    do
        echo " - $bindEth" >&2
        /opt/apigw/gwhw/tools/dpdk-devbind --bind=vfio-pci $bindEth
    done

    # sleep用于等待dpdk绑定脚本，若不加有一定几率会收不到流量
    sleep 2
    sudo /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser

    # 输出绑定结果并返回绑定失败的网卡列表
    bindStatus=$(/opt/apigw/gwhw/tools/dpdk-devbind -s)
    echo "" >&2
    failBindArray=()
    for bindEth in "${bindEthArray[@]}"
    do
        echo -n "Bind $bindEth " >&2
        if [ $(echo "$bindStatus" | grep "$bindEth" | wc -l) -eq 1 ]
        then
            echo "fail" >&2
            failBindArray+=("$bindEth")
        else
            echo "success" >&2
        fi
    done

    # 只输出绑定失败的网卡名到stdout
    echo "${failBindArray[@]}"
}

function bind_eth()
{
    # 先解绑
    bash /opt/apigw/gwhw/tools/dpdk_unbind.sh 1> /dev/null

    # 展示网关当前安装模式
    echo "Current installation mode:"
    echo -n -e "\t"

    sourcePath=$(grep "source_path" /opt/data/apigw/gwhw/gw_parser.conf | tail -1)
    if [ $(echo $sourcePath | grep "file_source" | wc -l) -eq 1 ]
    then
        echo -n "FILE"
        if [ $(echo $sourcePath | grep -E "nic_source|dpdk_source" | wc -l) -eq 0 ]
        then
            # agent模式，即读pcap包的方式不用绑定网口，直接退出
            echo -e "\nDo not need bind eth! ! !"
            exit
        else
            echo -n " + "
        fi
    fi

    if [ $(echo $sourcePath | grep dpdk_source | wc -l) -eq 1 ]
    then
        # 性能模式
        echo "Performance"

        # 展示所有网口状态
        display_all_eth_status

        # 获取用户输入
        index=0
        bindEthNum=0
        bindEthArray=[]
        while [ true ]
        do
            read -p "Input number you want to bind(q to finish): " index

            if [ -z $index ]
            then
                continue
            fi

            if [ "q" == "$index" ]
            then
                break
            fi

            # 去重
            if [ $index -gt 0 ] && [ $index -le $ethNum ]
            then
                let index-=1
                isFind="false"
                for bindEth in ${bindEthArray[*]}
                do
                    if [ "$bindEth" == "${ethArray[$index]}" ]
                    then
                        isFind="true"
                        break
                    fi
                done

                if [ "true" == "$isFind" ]
                then
                    continue
                fi

                bindEthArray[$bindEthNum]=${ethArray[$index]}
                let bindEthNum+=1
            else
                echo -e "\nWarning: error number! ! !\n"
            fi
        done

        #绑定用户选择的网卡
        if [ $bindEthNum -gt 0 ]
        then
            # 直接清空并重写白名单文件，不保留原有内容
            > "$DPDK_WHITE_LIST_TXT"
            for bindEth in ${bindEthArray[*]}
            do
                echo "$bindEth" >> "$DPDK_WHITE_LIST_TXT"
            done

            # 进行dpdk绑定
            failBindEthArray=($(dpdk_bind))
            
            # 将绑定失败的网卡名从白名单中删除
            if [ ${#failBindEthArray[@]} -gt 0 ]; then
                # 清空并重写白名单文件
                > "$DPDK_WHITE_LIST_TXT"
                for bindEth in "${bindEthArray[@]}"
                do
                    isFail="false"
                    for failEth in "${failBindEthArray[@]}"
                    do
                        if [ "$bindEth" == "$failEth" ]; then
                            isFail="true"
                            break
                        fi
                    done
                    if [ "$isFail" == "false" ]; then
                        echo "$bindEth" >> "$DPDK_WHITE_LIST_TXT"
                    fi
                done
            else
                echo -e "\nNo eth bind failed, all bind successfully"
            fi
        else
            echo -e "\nNo eth bind"
        fi

        exit
    elif [ $(echo $sourcePath | grep -E "nic_source|pcap_source" | wc -l) -eq 1 ]
    then
        # pfring或libpcap
        lineNum=$(grep -n "nic_device_name" /opt/data/apigw/gwhw/gw_parser.conf | cut -d : -f 1)
        # 展示当前已绑定的网卡的网卡名
        alreadyBindEths=$(grep "nic_device_name" /opt/data/apigw/gwhw/gw_parser.conf |\
                            cut -d : -f 2 |\
                            cut -d \" -f 2 |\
                            awk -F , '{for (i = 1; i <= NF; i++) {print $i}}')
        alreadyBindNum=0
        echo -e "\nCurrent bind eth:"
        for ethName in $alreadyBindEths
        do
            let alreadyBindNum+=1
            echo -e "\t($alreadyBindNum)$ethName"
        done

        # 展示所有网口状态
        display_all_eth_status

        #获取用户输入
        index=0
        bindEthNum=0
        bindEthArray=[]
        bindEthStr=""
        while [ true ]
        do
            read -p "Input number you want to bind(q to finish): " index

            if [ -z $index ]
            then
                continue
            fi

            if [ "q" == "$index" ]
            then
                break
            fi

            if [ $index -gt 0 ] && [ $index -le $ethNum ]
            then
                let index-=1
                isFind="false"

                # 去重
                for bindEth in ${bindEthArray[*]}
                do
                    if [ "$bindEth" == "${ethArray[$index]}" ]
                    then
                        isFind="true"
                        break
                    fi
                done

                if [ "true" == "$isFind" ]
                then
                    continue
                fi

                bindEthArray[$bindEthNum]=${ethArray[$index]}
                if [ $bindEthNum -eq 0 ]
                then
                    bindEthStr="${ethArray[$index]}"
                else
                    bindEthStr="${bindEthStr},${ethArray[$index]}"
                fi

                let bindEthNum+=1
            else
                echo -e "\nWarning: error number! ! !\n"
            fi
        done

        # 如果直接按q退出，则不做任何改动
        if [ "" == "$bindEthStr" ]; then
            echo -e "\nDo not change anything"
            exit
        fi

        # 绑定用户选择的网卡
        newNicBind="\"nic_device_name\":\"${bindEthStr}\","
        sed -i "${lineNum}d" /opt/data/apigw/gwhw/gw_parser.conf
        sed -i "${lineNum}i ${newNicBind}" /opt/data/apigw/gwhw/gw_parser.conf
        sed -i "${lineNum}s/^/    /g" /opt/data/apigw/gwhw/gw_parser.conf

        /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser;sleep 5;/opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl start gw_parser

        # 输出绑定结果
        if [ $bindEthNum -gt 0 ]
        then
            echo ""
            for bindEth in ${bindEthArray[*]}
            do
                echo "Bind $bindEth success"
            done
        fi

        exit
    fi
}

function main()
{
    if [[ $# -eq 0 ]];then
        bind_eth
    elif [[ $# -eq 1 ]];then
        dpdk_bind 
    fi
}

main $@