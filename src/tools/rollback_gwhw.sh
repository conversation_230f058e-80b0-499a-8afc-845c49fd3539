#! /bin/sh

INSTALL_PATH=/opt/gwhw/install/
BAK_PATH=/opt/gwhw/bak/
ETC_PATH=/opt/data/apigw/gwhw/

if [[ `systemctl status gwhw | grep "running" | wc -l` -eq 1 ]];then
    systemctl stop gwhw
fi

sh ./inst.sh 1>/dev/null 2>/dev/null

if [[ -f gw_parser.conf ]];then
    sudo cp -f gw_parser.conf "${ETC_PATH}./"
fi

if [[ -f user_info_rule.conf ]]; then
    sudo cp -f user_info_rule.conf "${ETC_PATH}./"
fi
if [[ -f forward_info_rule.conf ]]; then
    sudo cp -f forward_info_rule.conf "${ETC_PATH}./"
fi

if [[ -f gw_parser_5g.conf ]];then
    sudo cp -f gw_parser_5g.conf "${ETC_PATH}./"
fi

mkdir -p ${INSTALL_PATH}
cd ${INSTALL_PATH}
#删除当前版本的安装包
rm -f *
cd - 1>/dev/null
cd ${BAK_PATH}

file_name_set=`ls -lt  | grep "^-" | awk '{print $9}'`
file_name_arr=($file_name_set)

for file_name in ${file_name_arr[*]}
do 
    if [[ `echo "$file_name" | grep -E ".*zip$" | wc -l` -eq 1 ]];then
        mv ${file_name} ${INSTALL_PATH}
        break
    fi 
done

echo -e "rollback gwhw successfully\c"
rm -rf inst/
