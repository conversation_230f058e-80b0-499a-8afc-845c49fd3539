#!/bin/sh

PCAP_DIR=`grep "\"pcap_dir\"" /opt/data/apigw/gwhw/gw_parser.conf | awk -F: '{print $2}' | awk -F "[\"\"]" '{print $2}'`

if [ -z "$PCAP_DIR" ]; then
    PCAP_DIR="/opt/pcap/"
fi

if [ ! -d "$PCAP_DIR" ]; then 
    mkdir -p $PCAP_DIR
fi

CONFIG_DIR=/opt/data/apigw/gwhw/
CONFIG_FILE_PATH=$CONFIG_DIR/gw_parser.conf

# 检测间隔时间(s)
INTERVAL_TIME_S=2

TASKTAG=newtask_

run() {
  while true; do

    FILELIST=`ls $PCAP_DIR/*.pcap $PCAP_DIR/*.cap $PCAP_DIR/*.pcapng 2>>/dev/null | grep -v $TASKTAG`

    for file in $FILELIST
    do
        is_transferring="false"
        vsftpd_pid=$(ps -ef | grep '/usr/sbin/vsftpd /etc/vsftpd/vsftpd.conf' | grep -v grep | awk '{print $2}')
        for i in $vsftpd_pid
        do
          if [ $(timeout 10 find /proc/$i/fd/ -lname $file | wc -l) -ne 0 ]
          then
            is_transferring="true"
            break
          fi
        done

        if [ "true" == "$is_transferring" ]
        then
          continue
        fi

        filename=`basename $file`
        newname="$TASKTAG"$filename
        mv $file $PCAP_DIR/$newname
        echo "mv $file $PCAP_DIR/$newname"
    done

    sleep $INTERVAL_TIME_S
  done
}

STATUS=`grep "pcapfile_mode" $CONFIG_FILE_PATH | grep "1" `
RET=$?

if [ $RET -ne 0 ]; then
  sleep 100000000
fi

run
