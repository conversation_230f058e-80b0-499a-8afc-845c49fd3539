#! /bin/sh

SAMPLE_DIR="/opt/sample/"
SAMPLE_TMP_DIR="/opt/sample/tmp/"
SAMPLE_DST_FILE="/opt/sample/sample.tar.gz"
FILTER=$1

if [[ -d $SAMPLE_TMP_DIR ]];then
    rm -f "${SAMPLE_TMP_DIR}"*
else
    mkdir -p ${SAMPLE_TMP_DIR}
fi

if [[ -f ${SAMPLE_DST_FILE} ]];then
    rm -f ${SAMPLE_DST_FILE}
fi

cd $SAMPLE_DIR
#遍历SAMPLE_DIR所有的pcap文件进行过滤
file_list=`ls -l | grep "^-" | grep "pcap$" | awk '{print $9}'`
file_arr=($file_list)

for file_name in ${file_arr[*]}
do
    #echo "file name $file_name"
    if [[ x"$FILTER" != x"" ]];then
        tcpdump -r $file_name $1 -w "${SAMPLE_TMP_DIR}${file_name}"
    else
        cp ${file_name} ${SAMPLE_TMP_DIR}
    fi
done


cd $SAMPLE_TMP_DIR
if [[ `ls -l | wc -l` -gt 1 ]];then
    tar -zcf ${SAMPLE_DST_FILE} *
fi
cd ${SAMPLE_DIR}
rm -rf ${SAMPLE_TMP_DIR}


