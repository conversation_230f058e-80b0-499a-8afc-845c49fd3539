#!/bin/bash
sleep 1h

http_qps_current=0
ip_bytes_current=0
upload_msg_current=0

function getData() {
    content=""

    while [ true ]
    do
        content=$(cat -n /opt/apigw/gwhw/logs/hw.log | grep "stats qps" | tail -3)
    
        if [ $(echo "$content" | wc -l) -lt 3 ]
        then
echo "cant't get 3 line"
echo "==="
            sleep 1
            continue
        fi

        lineNum1=$(echo "$content" | sed -n '1p' | awk '{print $1}')
        lineNum2=$(echo "$content" | sed -n '2p' | awk '{print $1}')
        lineNum3=$(echo "$content" | sed -n '3p' | awk '{print $1}')
echo "lineNum1=$lineNum1"
echo "lineNum2=$lineNum2"
echo "lineNum3=$lineNum3"
echo "==="

        key1=$(echo "$content" | sed -n '1p' | awk '{print $3}')
        key2=$(echo "$content" | sed -n '2p' | awk '{print $3}')
        key3=$(echo "$content" | sed -n '3p' | awk '{print $3}')
echo "key1=$key1"
echo "key2=$key2"
echo "key3=$key3"
echo "==="

        http_qps_lineNum=0
        ip_bytes_lineNum=0
        upload_msg_lineNum=0

        if [ "$key2" == "qps" ]
        then
            http_qps_lineNum=$[lineNum2 + 1]
            ip_bytes_lineNum=$[lineNum2 + 2]
            upload_msg_lineNum=$[lineNum2 + 14]
        else
            http_qps_lineNum=$[lineNum1 + 1]
            ip_bytes_lineNum=$[lineNum1 + 2]
            upload_msg_lineNum=$[lineNum1 + 14]
        fi
echo "http_qps_lineNum=$http_qps_lineNum"
echo "ip_bytes_lineNum=$ip_bytes_lineNum"
echo "upload_msg_lineNum=$upload_msg_lineNum"
echo "==="

        http_qps_current=$(cat /opt/apigw/gwhw/logs/hw.log | sed -n "$http_qps_lineNum p" | awk '{print $5}')
        ip_bytes_current=$(cat /opt/apigw/gwhw/logs/hw.log | sed -n "$ip_bytes_lineNum p" | awk '{print $4}')
        upload_msg_current=$(cat /opt/apigw/gwhw/logs/hw.log | sed -n "$upload_msg_lineNum p" | awk '{print $3}')
        if [ "" == "$http_qps_current" ]
        then
echo "can't get http_qps_current"
echo "==="
            sleep 1
            continue
        elif [ "nan" == "$http_qps_current" ]
        then
            http_qps_current=0
        fi

        if [ "" == "$ip_bytes_current" ]
        then
echo "can't get ip_bytes_current"
echo "==="
            sleep 1
            continue
        elif [ "nan" == "$ip_bytes_current" ]
        then
            ip_bytes_current=0
        fi

        if [ "" == "$upload_msg_current" ]
        then
echo "can't get upload_msg_current"
echo "==="
            sleep 1
            continue
        elif [ "nan" == "$upload_msg_current" ]
        then
            upload_msg_current=0
        fi
echo "http_qps_current=$http_qps_current"
echo "ip_bytes_current=$ip_bytes_current"
echo "upload_msg_current=$upload_msg_current"
echo "==="

        break
    done
}

average_base=0
average_last=0
average_current=0
average_head_5=0
average_tail_5=0

#http_qps_data=0
#ip_bytes_data=0
upload_msg_data=0

#http_qps_delta=0
#ip_bytes_delta=0
upload_msg_delta=0

#http_qps_reduceRate=0
#ip_bytes_reduceRate=0
upload_msg_reduceRate=0

#http_qps_reduceRateTotall=0
#ip_bytes_reduceRateTotall=0
upload_msg_reduceRateTotall=0

#http_qps_reduceRateRelative=0
#ip_bytes_reduceRateRelative=0
upload_msg_reduceRateRelative=0

isGetBase="true"

while [ true ]
do
    sleep 2m

    date +"%Y-%m-%d %H:%M:%S"
echo "==="
    for ((i = 0; i < 10; i++))
    do
        sleep 5
        getData
        http_qps_data[$i]=$http_qps_current
        ip_bytes_data[$i]=$ip_bytes_current
        upload_msg_data[$i]=$upload_msg_current
    done

    total=0
    for i in ${upload_msg_data[*]}
    do
        total=$(echo "$total + $i" | bc)
    done
    average_current=$(echo "scale=2;$total / 10" | bc)
echo "average_current=$average_current"
echo "==="

    if [ "true" == "$isGetBase" ]
    then
        average_base=$average_current
        isGetBase="false"
        continue
    fi
echo "average_base=$average_base"
echo "==="

    total=0
    for ((i = 0; i < 5; i++))
    do
        total=$(echo "$total + ${upload_msg_data[$i]}" | bc)
    done
    average_head_5=$(echo "scale=2;$total / 5" | bc)
echo "average_head_5=$average_head_5"
echo "==="

    total=0
    for ((i = 5; i < 10; i++))
    do
        total=$(echo "$total + ${upload_msg_data[$i]}" | bc)
    done
    average_tail_5=$(echo "scale=2;$total / 5" | bc)
echo "average_tail_5=$average_tail_5"
echo "==="

    isRestart="false"

    upload_msg_delta=$(echo "$average_last - $average_current" | bc)
    upload_msg_reduceRate=0
    if [ $(echo "$upload_msg_delta > 0" | bc) -eq 1 ]
    then
        upload_msg_reduceRate=$(echo "scale=2;$upload_msg_delta * 100 / $average_last" | bc)
        if [ $(echo "$upload_msg_reduceRate > 60" | bc) -eq 1 ]
        then
            isRestart="true"
        fi
    fi
echo "average_last=$average_last"
echo "average_current=$average_current"
echo "upload_msg_reduceRate=$upload_msg_reduceRate"
echo "==="
    average_last=$average_current

    upload_msg_delta=$(echo "$average_base - $average_current" | bc)
    upload_msg_reduceRateTotall=0
    if [ $(echo "$upload_msg_delta > 0" | bc) -eq 1 ]
    then
        upload_msg_reduceRateTotall=$(echo "scale=2;$upload_msg_delta * 100 / $average_base" | bc)
        if [ $(echo "$upload_msg_reduceRateTotall > 60" | bc) -eq 1 ]
        then
            isRestart="true"
        fi
    fi
echo "average_base=$average_base"
echo "average_current=$average_current"
echo "upload_msg_reduceRateTotall=$upload_msg_reduceRateTotall"
echo "==="

    upload_msg_delta=$(echo "$average_head_5 - $average_tail_5" | bc)
    upload_msg_reduceRateRelative=0
    if [ $(echo "$upload_msg_delta > 0" | bc) -eq 1 ]
    then
        upload_msg_reduceRateRelative=$(echo "scale=2;$upload_msg_delta * 100 / $average_head_5" | bc)
        if [ $(echo "$upload_msg_reduceRateRelative > 30" | bc) -eq 1 ]
        then
            isRestart="true"
        fi
    fi
echo "average_head_5=$average_head_5"
echo "average_tail_5=$average_tail_5"
echo "upload_msg_reduceRateRelative=$upload_msg_reduceRateRelative"
echo "==="

    if [ "true" == "$isRestart" ]
    then
        gwhwProcessID=$(ps -ef | grep gw_parser | grep -v grep | awk '{print $2}')
echo "kill $gwhwProcessID"
echo "==="
        kill $gwhwProcessID
        isGetBase="true"
    fi
done