#!/bin/bash

function uninst_env()
{
    # Tomcat 卸载
    rm -rf /usr/local/share/tomcat/*

    # 删除 gwcfg.py
    rm -rf /usr/local/share/gwcfg

    # 删除字符界面程序
    rm -rf /usr/local/share/hwtui
    rm -rf /opt/data/hwtui

    # 删除版本文件
    rm -rf /etc/version.conf

    # 卸载 mongodb
    #rpm -evh mongodb-org-mongos-3.4.10-1.el7.x86_64

    # 卸载gwcfg依赖

    # 卸载gwtui依赖库
    echo 'uninst env'
}

function stop_svr()
{
    # 关闭 gwfg.py
    systemctl stop gwcfg
    echo gwcfg stoped

    # 关闭自负界面程序
    systemctl stop hwtui
    echo hwtui stoped

    # 关闭 tomcat
    systemctl stop tomcat
    sleep 5
    if [[ `id tomcat | wc -l` -eq 1 ]]; then
        userdel -r  tomcat
    fi

    MONGO_STORAGE=/opt/data/mongodb/storage/
    # 关闭mongodb
    systemctl stop mongod
    sleep 5
    rm -rf ${MONGO_STORAGE}
    rm -rf /etc/mongod.conf
    rm -rf /var/log/mongodb/mongod.log
    echo mongod stoped
}

# 注销服务
function unreg_svr()
{
    # 关闭自启动
    systemctl disable gwcfg 
    systemctl disable tomcat
    systemctl disable hwtui

    # 删除启动项
    rm -rf /usr/lib/systemd/system/tomcat.service
    rm -rf /usr/lib/systemd/system/gwcfg.service
    rm -rf /usr/lib/systemd/system/hwtui.service
    echo 'remove service'
}

function __main__()
{
    stop_svr
    unreg_svr
    uninst_env
}

__main__
