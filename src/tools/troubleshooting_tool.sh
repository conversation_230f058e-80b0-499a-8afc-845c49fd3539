#!/bin/bash

CONF_PATH=/opt/data/apigw/gwhw/gw_parser.conf

function set_coredump()
{
    prlimit -p `ps -ef | grep gw_parser | grep -v 'grep\|supervisorctl' | awk '{print $2}'` -cunlimited 2>/dev/null && echo "coredump ok" \
    && prlimit -p `ps -ef | grep gw_parser | grep -v 'grep' | awk '{print $2}'` -c || echo "Error: gw_parser died"
}

function module_info()
{
    pldd `ps -ef | grep gw_parser | grep -v 'grep' | awk '{print $2}'` 2>/dev/null | grep -v "^/" | grep ".so$" || echo "Error: gw_parser died"
}

function sys_info()
{
    # cpu信息
    echo -e "\033[40;31m --------------------------cpu_info------------------------------- \033[0m"
    echo "CPU(s): `cat /proc/cpuinfo| grep "physical id"| sort| uniq| wc -l`"
    cat /proc/cpuinfo| grep "cpu cores" | uniq | awk -F ":" '{print "cores per CPU:",$2}'
    echo "Threads: `cat /proc/cpuinfo| grep "processor" | wc -l`"
    # 内存信息
    echo -e "\033[40;31m --------------------------mem_info------------------------------- \033[0m"
    free -m | grep Mem | awk '{print "Mem(total/used/free):",$2,$3,$4}'
    # 绑定的网卡信息
    ls /opt/apigw/gwhw/tools/dpdk-devbind &>/dev/null &&\
       echo  -e "\033[40;31m --------------------------eth_bind------------------------------- \033[0m" &&  /opt/apigw/gwhw/tools/dpdk-devbind -s | grep "drv=igb_uio" | awk -F "'" '{print $2}'

}

function split_pcap
{
    tcpdump -r $1 -w "${1}_" -C $2
}

# .pcap 后缀
function rename
{
    files=`ls ${1}_* | grep -v "\.pcap$"`
    for file in $files
    do
        mv $file ${file}.pcap
    done
}

function check_mode
{
    jq '.plugin.load_files' $CONF_PATH | grep ${1}_source >/dev/null && jq '.plugin.source_path' $CONF_PATH | grep ${1}_source >/dev/null && echo $1 on || echo $1 off
}

function set_mode
{
    new_load_files=`jq '.plugin.load_files' $CONF_PATH | sed "s/[a-z]*_source.so://" | sed "s/\":/\"/" | sed "s/\"/\"${1}_source.so:/"`
    new_source_path="\"source/${1}_source/\""
    jq ".plugin.load_files=$new_load_files" $CONF_PATH | jq ".plugin.source_path=$new_source_path" > ${CONF_PATH}.tmp
    mv ${CONF_PATH}.tmp $CONF_PATH
}

function __help__()
{
    echo Usage:
    echo -e "\t$0 sys_info      : print information for cpu, memory, ethernet,"
    echo -e "\t$0 module_info   : print all module information that gw_parser loaded"
    echo -e "\t$0 set_coredump  : create core file if gw_parser will die"
    echo -e "\t$0 mode_info     : gw_parser source mode (dpdk | file)"
    echo -e "\t$0 set_mode      : set source mode"
    echo -e "\t$0 split_pcap    : split pcap by size(M). defualt:10M"
    echo -e ""
}

function __main__()
{
    case $1 in
        "sys_info")
            sys_info
            ;;
        "module_info")
            module_info
            ;;
        "set_coredump")
            set_coredump
            ;;
        "mode_info")
            check_mode dpdk
            check_mode file
            ;;
        "set_mode")
            set_mode $2
            systemctl restart gwhw
            ;;
        "split_pcap")
            split_pcap $2 $3 && rename $2
            ;;
        *)
            __help__
            ;;
    esac
}
__main__ $*
